/* Import Vazirmatn font for Persian/Dari text */
@import url('https://cdn.jsdelivr.net/gh/rastikerdar/vazirmatn@v33.003/Vazirmatn-font-face.css');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Font family configuration for different languages */
:root {
  --font-vazirmatn: 'Vazirmatn', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  --font-english: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Apply Vazirmatn font for RTL languages (Persian/Dari and Arabic) */
[dir="rtl"] {
  font-family: var(--font-vazirmatn);
}

/* Apply English font for LTR languages */
[dir="ltr"] {
  font-family: var(--font-english);
}

/* Specific font for Persian/Dari content */
.font-vazirmatn,
[lang="fa"],
[lang="fa-IR"] {
  font-family: var(--font-vazirmatn) !important;
}

:root {
  --border-radius: var(--mui-shape-borderRadius);
  --border-color: var(--mui-palette-divider);
  --primary-color: var(--mui-palette-primary-main);
  --background-color: var(--mui-palette-background-default);
  --background-color-rgb: var(--mui-palette-background-paperChannel);
  --header-height: 64px;
  --header-z-index: var(--mui-zIndex-appBar);
  --footer-z-index: 10;
  --customizer-z-index: var(--mui-zIndex-drawer);
  --search-z-index: var(--mui-zIndex-tooltip);
  --drawer-z-index: var(--mui-zIndex-drawer);
  --backdrop-color: rgba(0, 0, 0, 0.5);
  --mui-mainColorChannels-shadow: var(--mui-mainColorChannels-lightShadow);
}

[data-dark] {
  --backdrop-color: rgb(16 17 33 / 0.6);
  --mui-mainColorChannels-shadow: var(--mui-mainColorChannels-darkShadow);
}

*,
::before,
::after {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
  border-width: 0;
  border-style: solid;
  border-color: theme('borderColor.DEFAULT', currentColor);
}

html {
  display: flex;
  inline-size: 100%;
  min-block-size: 100%;
}

a {
  color: inherit;
  text-decoration: none;
}

ul:not([class]) {
  margin-block: 1rem;
  padding-inline-start: 40px;
}

/*! Do not remove the following styles */
/*
  Fix the popout / submenu closing issue when using hover trigger to open the menu and using
  perfectscrollbar where the bridge create with safe polygon by floating ui was not working
*/
[data-floating-ui-safe-polygon] .scrollbar-container {
  pointer-events: auto;
}
[data-floating-ui-safe-polygon] .scrollbar-container .ps__rail-y {
  pointer-events: none;
}

.ps__rail-y {
  inset-inline-end: 0 !important;
  inset-inline-start: auto !important;
  & .ps__thumb-y {
    inset-inline-end: 3px !important;
    inset-inline-start: auto !important;
    background-color: var(--mui-palette-divider);
    inline-size: 6px;
    &:hover,
    &:focus,
    &.ps--clicking {
      background-color: var(--mui-palette-action-disabled) !important;
    }
  }
  &:hover,
  &:focus,
  &.ps--clicking {
    background-color: var(--mui-palette-action-hover) !important;
  }
  &:hover,
  &:focus,
  &.ps--clicking {
    .ps__thumb-y {
      background-color: var(--mui-palette-action-disabled) !important;
    }
  }
}

.ts-vertical-nav-root {
  .ps__thumb-y {
    inline-size: 4px;
    &:hover,
    &:focus,
    &.ps--clicking {
      inline-size: 6px;
    }
  }
  .ps__rail-y {
    inline-size: 10px;
    &:hover,
    &:focus,
    &.ps--clicking {
      background-color: transparent !important;
      .ps__thumb-y {
        inline-size: 6px;
      }
    }
  }
}

:where([class^='ri-']) {
  font-size: 1.5rem;
}

/* Fix width issue for the input-otp component */
input[autocomplete='one-time-code'][data-input-otp='true'] {
  inline-size: 100% !important;
}

code {
  font-family: inherit;
  padding-block: 2px;
  padding-inline: 4px;
  border-radius: 4px;
  font-size: 90%;
  color: var(--mui-palette-info-main);
  background-color: rgb(var(--mui-palette-info-mainChannel) / 0.08);
  border: 0;
}
