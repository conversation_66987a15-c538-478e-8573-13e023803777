// React Imports
import type { SVGAttributes } from 'react'

const LaptopCharging = (props: SVGAttributes<SVGElement>) => {
  return (
    <svg xmlns='http://www.w3.org/2000/svg' width='42' height='43' viewBox='0 0 42 43' fill='none' {...props}>
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M7.76542 6.38916C8.1194 6.03519 8.59949 5.83633 9.10009 5.83633H32.9001C33.4007 5.83633 33.8808 6.03519 34.2348 6.38916C34.5887 6.74314 34.7876 7.22323 34.7876 7.72383V23.9998H7.21259V7.72383C7.21259 7.22323 7.41145 6.74314 7.76542 6.38916ZM6.81985 27.2248H35.1803L20.9362 32.3613H19.2501C18.5141 32.3613 17.8932 32.8544 17.6999 33.5283L6.37709 37.6114C6.0856 37.6112 5.79811 37.5435 5.53715 37.4137C5.27605 37.2837 5.04861 37.095 4.87268 36.8624C4.69676 36.6298 4.57713 36.3596 4.52319 36.0729C4.46926 35.7864 4.48246 35.4912 4.56174 35.2106L4.56179 35.2104L6.81985 27.2248ZM17.6999 33.5284L6.37757 37.6114H35.6226H35.6231C35.9146 37.6112 36.2021 37.5435 36.463 37.4137C36.7241 37.2837 36.9516 37.095 37.1275 36.8624C37.3034 36.6298 37.4231 36.3596 37.477 36.0729C37.5309 35.7863 37.5177 35.4911 37.4384 35.2104L35.1803 27.2248L20.9364 32.3613H22.7501C23.6407 32.3613 24.3626 33.0833 24.3626 33.9738C24.3626 34.8644 23.6407 35.5863 22.7501 35.5863H19.2501C18.3595 35.5863 17.6376 34.8644 17.6376 33.9738C17.6376 33.8193 17.6593 33.6699 17.6999 33.5284ZM3.98759 7.72383V25.3887L1.45843 34.3331L1.45838 34.3333C1.24351 35.0934 1.20772 35.8931 1.35383 36.6694C1.49993 37.4458 1.82396 38.1777 2.30047 38.8077C2.77698 39.4378 3.39302 39.9489 4.10025 40.3009C4.80747 40.6528 5.58664 40.8361 6.37661 40.8364H6.37709H35.6231H35.6236C36.4136 40.8361 37.1927 40.6528 37.8999 40.3009C38.6072 39.9489 39.2232 39.4378 39.6997 38.8077C40.1762 38.1777 40.5003 37.4458 40.6464 36.6694C40.7925 35.8931 40.7567 35.0934 40.5418 34.3333L40.5418 34.3331L38.0126 25.3887V7.72383C38.0126 6.36791 37.474 5.06753 36.5152 4.10875C35.5564 3.14996 34.256 2.61133 32.9001 2.61133H9.10009C7.74417 2.61133 6.44378 3.14997 5.485 4.10875C4.52622 5.06753 3.98759 6.36791 3.98759 7.72383ZM21.8269 10.257C22.2594 9.4786 21.979 8.49688 21.2006 8.06432C20.4221 7.63176 19.4404 7.91216 19.0078 8.69061L16.0906 13.9406C15.8131 14.44 15.8206 15.0491 16.1103 15.5415C16.4 16.034 16.9287 16.3363 17.5001 16.3363H21.7594L20.1733 19.1906C19.7408 19.9691 20.0212 20.9508 20.7996 21.3833C21.5781 21.8159 22.5598 21.5355 22.9924 20.757L25.9096 15.507C26.1871 15.0076 26.1796 14.3986 25.8899 13.9061C25.6002 13.4137 25.0715 13.1113 24.5001 13.1113H20.2408L21.8269 10.257Z'
        fill='var(--mui-palette-primary-main)'
      />
    </svg>
  )
}

export default LaptopCharging
