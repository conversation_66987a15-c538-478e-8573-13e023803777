// React Imports
import type { SVGAttributes } from 'react'

const Levi<PERSON> = (props: SVGAttributes<SVGElement>) => {
  return (
    <svg width='64' height='29' viewBox='0 0 64 29' fill='none' xmlns='http://www.w3.org/2000/svg' {...props}>
      <g id='Logo-5' clipPath='url(#clip0_425_86424)'>
        <path
          id='Vector'
          fillRule='evenodd'
          clipRule='evenodd'
          d='M0.539307 0.447754L6.57237 27.0783C12.6294 23.5579 25.1076 22.3235 32.0469 27.0953C38.9881 22.3336 51.453 23.5599 57.5062 27.0783L63.5393 0.447754H0.539307Z'
          fill='currentColor'
        />
        <path
          id='Vector_2'
          fillRule='evenodd'
          clipRule='evenodd'
          d='M8.36499 6.93097V16.9397H15.32V14.5899H11.6913V6.93097H8.36499ZM25.92 6.94724H29.6259L32.1255 13.6702L34.5389 6.94724H38.2017L34.022 16.9234H30.1431L25.92 6.94724ZM39.1264 6.93775V16.9329H42.449V6.93775H39.1264ZM20.9362 6.76509C17.8941 6.76509 15.7366 7.3516 15.7366 12.0006C15.7366 15.824 16.6643 17.4102 21.0655 17.4102C25.7688 17.4102 25.8553 15.1724 25.9847 14.1079H22.662C22.662 14.9552 21.8205 15.3243 20.9574 15.3243C20.6985 15.3243 19.1236 15.5418 19.1236 12.9779H25.9843C26.0275 12.6085 25.9853 11.1953 25.9628 10.9792C25.726 8.72033 25.5102 6.74338 20.9362 6.76509ZM51.2635 6.77662C53.9773 6.77662 55.7005 7.79528 55.9153 10.006H52.4697C52.4697 9.52939 52.1253 8.64067 51.1339 8.64067C50.1435 8.64067 49.6047 8.96561 49.6047 9.70243C49.6047 10.4826 50.0573 10.6777 50.4018 10.7645C50.5742 10.8079 52.6418 11.1112 53.3305 11.198C53.8433 11.2624 55.9581 11.3714 55.9153 13.5386C55.8718 15.7063 54.5366 17.0499 51.1773 17.0499C47.817 17.0499 46.2235 15.8796 46.2235 13.9725H49.6698C49.6906 14.6441 50.057 15.1428 51.1339 15.1428C52.2111 15.1428 52.7708 14.6672 52.7708 13.9291C52.7708 13.3653 52.2754 13.0621 51.737 13.0186C51.3831 12.9901 49.1313 12.9752 48.0544 12.4982C47.094 12.0732 45.9938 11.4182 46.1807 9.76718C46.3959 7.86045 47.7742 6.77662 51.2635 6.77662ZM46.0703 7.10499C46.0781 7.86549 46.0137 8.36522 44.9995 8.93033C45.0645 8.78277 45.2638 8.47372 45.2315 8.26617C45.0608 8.33204 44.8795 8.36564 44.6967 8.36522C43.9384 8.36522 43.3242 7.80109 43.3242 7.10533C43.3242 6.40958 43.9384 5.84546 44.6967 5.84546C45.4561 5.84512 46.0703 6.40925 46.0703 7.10499Z'
          fill='white'
        />
        <path
          id='Vector_3'
          fillRule='evenodd'
          clipRule='evenodd'
          d='M19.1877 10.8682H22.7502C22.7717 9.49811 22.2535 8.75891 20.9365 8.75891C19.7491 8.75891 19.1877 9.52016 19.1877 10.8682ZM55.3212 27.3272C55.2613 27.2654 55.1895 27.2166 55.1103 27.1837C55.031 27.1509 54.9458 27.1347 54.8602 27.1362C54.6814 27.1362 54.5281 27.2 54.4019 27.3272C54.3405 27.3871 54.2918 27.4591 54.2591 27.5386C54.2263 27.6181 54.21 27.7035 54.2113 27.7896C54.2113 27.9717 54.2745 28.1268 54.4002 28.2547C54.5271 28.3832 54.6803 28.4477 54.8605 28.4477C55.0406 28.4477 55.1945 28.3832 55.3215 28.2547C55.4484 28.1261 55.5114 27.9711 55.5114 27.7896C55.5128 27.7035 55.4965 27.6181 55.4638 27.5386C55.4311 27.4591 55.3826 27.3871 55.3212 27.3272ZM55.2558 28.1915C55.1474 28.3021 55.0151 28.3574 54.8598 28.3574C54.7045 28.3574 54.5722 28.3021 54.4631 28.1915C54.4107 28.139 54.3693 28.0763 54.3415 28.0072C54.3138 27.9381 54.3002 27.8641 54.3015 27.7896C54.3015 27.6336 54.3554 27.5006 54.4645 27.3903C54.5736 27.2797 54.7052 27.2244 54.8598 27.2244C55.0144 27.2244 55.1467 27.2797 55.2558 27.3903C55.3649 27.5006 55.4188 27.6336 55.4188 27.7896C55.4192 27.9467 55.3649 28.0807 55.2558 28.1915Z'
          fill='currentColor'
        />
        <path
          id='Vector_4'
          fillRule='evenodd'
          clipRule='evenodd'
          d='M55.1501 28.1385C55.1468 28.1324 55.1447 28.1199 55.1434 28.1012C55.1421 28.0826 55.1414 28.0646 55.1414 28.0476V27.995C55.1403 27.9547 55.1268 27.9157 55.1027 27.8834C55.0883 27.8636 55.0698 27.8471 55.0487 27.8349C55.0275 27.8227 55.004 27.8152 54.9798 27.8129C55.0162 27.8085 55.0514 27.7966 55.0831 27.7779C55.1299 27.7477 55.1535 27.7002 55.1535 27.6361C55.1535 27.5455 55.1168 27.4848 55.043 27.4536C55.0017 27.4367 54.9366 27.4279 54.8477 27.4279H54.5979V28.1477H54.7235V27.8637H54.8228C54.8898 27.8637 54.9366 27.8715 54.9629 27.8868C55.0084 27.9136 55.0306 27.9685 55.0306 28.052V28.109L55.0333 28.1321L55.0346 28.1398C55.0352 28.1423 55.0359 28.1447 55.0367 28.147H55.1545L55.1501 28.1385ZM54.9569 27.7589C54.9183 27.7716 54.8778 27.7771 54.8373 27.7752H54.7235V27.5143H54.8306C54.9003 27.5143 54.9508 27.5231 54.9831 27.5411C55.0148 27.5591 55.0306 27.594 55.0306 27.6466C55.0302 27.7019 55.0053 27.7396 54.9569 27.7589Z'
          fill='currentColor'
        />
      </g>
      <defs>
        <clipPath id='clip0_425_86424'>
          <rect width='63' height='28' fill='white' transform='translate(0.539307 0.447754)' />
        </clipPath>
      </defs>
    </svg>
  )
}

export default Levis
