// React Imports
import type { SVGAttributes } from 'react'

const ElementTwo = (props: SVGAttributes<SVGElement>) => {
  return (
    <svg width='24' height='37' viewBox='0 0 24 37' fill='none' xmlns='http://www.w3.org/2000/svg' {...props}>
      <path
        id='Vector'
        opacity='0.16'
        d='M13.0052 22.0284C13.2452 19.6684 13.3851 17.4284 13.7051 15.2084C14.2651 11.3784 15.6751 7.79842 17.3551 4.33842C17.8751 3.26842 18.5851 2.26842 19.3051 1.30842C19.9051 0.508425 20.7051 -0.0515632 21.8251 0.258437C22.9251 0.558437 23.3351 1.42842 23.5151 2.41842C23.9151 4.64842 23.9851 6.89843 23.6251 9.12843C23.0851 12.5084 22.4251 15.8684 21.8851 19.2484C21.2551 23.1884 20.6952 27.1384 20.0951 31.0884C19.9552 31.9984 19.7751 32.8984 19.6251 33.8084C19.3651 35.4784 17.7451 36.6884 15.8451 35.6484C10.6252 32.7684 5.69515 29.4884 1.65515 25.0384C0.955147 24.2784 0.515128 23.2284 0.135128 22.2484C-0.254872 21.2584 0.235137 20.1584 1.14514 19.5884C2.08514 18.9984 3.11515 18.7884 4.22515 18.8984C6.87515 19.1584 9.25513 20.2084 11.5751 21.4184C11.8951 21.5784 12.1951 21.7684 12.5151 21.9184C12.6551 21.9784 12.8052 21.9884 13.0052 22.0284Z'
        fill='var(--mui-palette-text-primary)'
      />
    </svg>
  )
}

export default ElementTwo
