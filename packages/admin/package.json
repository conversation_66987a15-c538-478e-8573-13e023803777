{"name": "@azkuja/admin", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 5050", "build": "next build", "start": "next start -p 8080", "lint": "next lint"}, "dependencies": {"@azkuja/shared": "1.0.0", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^5.14.19", "@mui/lab": "^5.0.0-alpha.155", "@mui/material": "^5.14.19", "@mui/system": "^7.1.1", "@mui/x-data-grid": "^6.18.2", "@mui/x-date-pickers": "^6.18.2", "@refinedev/core": "^4.45.1", "@refinedev/inferencer": "^4.5.21", "@refinedev/kbar": "^1.3.0", "@refinedev/mui": "^5.13.21", "@refinedev/nextjs-router": "^5.5.5", "@refinedev/react-hook-form": "^4.8.11", "@refinedev/react-table": "^5.6.5", "@tanstack/react-query": "^4.36.1", "@tanstack/react-table": "^8.21.3", "@types/lodash-es": "^4.17.12", "axios": "^1.6.2", "date-fns": "^2.30.0", "dayjs": "^1.11.13", "lodash-es": "^4.17.21", "next": "14.1.0", "nookies": "^2.5.2", "react": "^18", "react-dom": "^18", "react-hook-form": "^7.48.2", "recharts": "^2.8.0"}, "devDependencies": {"@types/lodash-es": "^4.17.12", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.0.1", "eslint": "^8", "eslint-config-next": "14.1.0", "postcss": "^8", "tailwindcss": "^3.3.0", "typescript": "^5"}}