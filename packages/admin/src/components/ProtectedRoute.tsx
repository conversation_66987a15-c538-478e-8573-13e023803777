"use client";

import React, { useEffect, useState } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { Box, CircularProgress, Typography } from '@mui/material';
import { useGetIdentity, useIsAuthenticated } from '@refinedev/core';

interface ProtectedRouteProps {
  children: React.ReactNode;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ children }) => {
  const [isClient, setIsClient] = useState(false);
  const router = useRouter();
  const pathname = usePathname();
  
  const { data: authData, isLoading: isAuthLoading } = useIsAuthenticated();
  const { data: identity, isLoading: isIdentityLoading } = useGetIdentity();

  useEffect(() => {
    setIsClient(true);
  }, []);

  useEffect(() => {
    if (!isClient || isAuthLoading || isIdentityLoading) return;

    // Allow access to login page
    if (pathname === '/login') {
      // If already authenticated, redirect to dashboard
      if (authData?.authenticated) {
        router.replace('/');
      }
      return;
    }

    // For all other pages, check authentication
    if (!authData?.authenticated) {
      router.replace('/login');
      return;
    }

    // Check user role for admin access
    if (identity && !['admin', 'restaurant_owner'].includes((identity as any).role)) {
      // For unauthorized roles, logout and redirect to login
      localStorage.removeItem('access_token');
      localStorage.removeItem('user');
      router.replace('/login');
      return;
    }
  }, [isClient, authData, identity, pathname, router, isAuthLoading, isIdentityLoading]);

  // Show loading screen while checking authentication
  if (!isClient || isAuthLoading || isIdentityLoading) {
    return (
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          minHeight: '100vh',
          flexDirection: 'column',
          gap: 2,
        }}
      >
        <CircularProgress size={40} />
        <Typography variant="body2" color="textSecondary">
          Checking authentication...
        </Typography>
      </Box>
    );
  }

  // Allow login page to render without authentication
  if (pathname === '/login') {
    return <>{children}</>;
  }

  // For authenticated routes, check if user is authenticated and has proper role
  if (!authData?.authenticated) {
    return (
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          minHeight: '100vh',
          flexDirection: 'column',
          gap: 2,
        }}
      >
        <Typography variant="h6" color="error">
          Authentication Required
        </Typography>
        <Typography variant="body2" color="textSecondary">
          Redirecting to login...
        </Typography>
      </Box>
    );
  }

  // If user doesn't have proper role
  if (identity && !['admin', 'restaurant_owner'].includes((identity as any).role)) {
    return (
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          minHeight: '100vh',
          flexDirection: 'column',
          gap: 2,
        }}
      >
        <Typography variant="h6" color="error">
          Access Denied
        </Typography>
        <Typography variant="body2" color="textSecondary">
          You don't have permission to access this admin panel
        </Typography>
      </Box>
    );
  }

  return <>{children}</>;
};

export default ProtectedRoute; 