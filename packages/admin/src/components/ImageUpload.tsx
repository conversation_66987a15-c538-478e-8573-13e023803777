import React, { useState, useCallback } from 'react';
import {
  Box,
  Button,
  Typography,
  CircularProgress,
  Alert,
  Avatar,
  IconButton,
  Paper,
} from '@mui/material';
import {
  CloudUpload as UploadIcon,
  Delete as DeleteIcon,
  Image as ImageIcon,
} from '@mui/icons-material';

interface ImageUploadProps {
  value?: string;
  onChange: (url: string) => void;
  onError?: (error: string) => void;
  label?: string;
  accept?: string;
  maxSize?: number; // in MB
}

const ImageUpload: React.FC<ImageUploadProps> = ({
  value,
  onChange,
  onError,
  label = "آپلود تصویر",
  accept = "image/*",
  maxSize = 5,
}) => {
  const [uploading, setUploading] = useState(false);
  const [dragOver, setDragOver] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleFileSelect = useCallback(async (file: File) => {
    setError(null);
    
    // Validate file type
    if (!file.type.startsWith('image/')) {
      const errorMsg = 'فقط فایل‌های تصویری مجاز هستند';
      setError(errorMsg);
      onError?.(errorMsg);
      return;
    }

    // Validate file size
    if (file.size > maxSize * 1024 * 1024) {
      const errorMsg = `حجم فایل نباید بیشتر از ${maxSize} مگابایت باشد`;
      setError(errorMsg);
      onError?.(errorMsg);
      return;
    }

    setUploading(true);

    try {
      const formData = new FormData();
      formData.append('file', file);

      const response = await fetch('/api/upload-image', {
        method: 'POST',
        body: formData,
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('access_token')}`,
        },
      });

      if (!response.ok) {
        throw new Error('خطا در آپلود تصویر');
      }

      const result = await response.json();
      
      if (result.success && result.url) {
        onChange(result.url);
      } else {
        throw new Error(result.message || 'خطا در آپلود تصویر');
      }
    } catch (err) {
      const errorMsg = err instanceof Error ? err.message : 'خطا در آپلود تصویر';
      setError(errorMsg);
      onError?.(errorMsg);
    } finally {
      setUploading(false);
    }
  }, [maxSize, onChange, onError]);

  const handleFileInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      handleFileSelect(file);
    }
    // Reset input value to allow selecting the same file again
    event.target.value = '';
  };

  const handleDragOver = (event: React.DragEvent) => {
    event.preventDefault();
    setDragOver(true);
  };

  const handleDragLeave = (event: React.DragEvent) => {
    event.preventDefault();
    setDragOver(false);
  };

  const handleDrop = (event: React.DragEvent) => {
    event.preventDefault();
    setDragOver(false);
    
    const file = event.dataTransfer.files?.[0];
    if (file) {
      handleFileSelect(file);
    }
  };

  const handleRemoveImage = () => {
    onChange('');
    setError(null);
  };

  return (
    <Box>
      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {value ? (
        // Image Preview
        <Paper
          sx={{
            p: 2,
            borderRadius: 2,
            border: '1px solid #E2E8F0',
            position: 'relative',
          }}
        >
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <Avatar
              src={value}
              sx={{
                width: 80,
                height: 80,
                borderRadius: 2,
              }}
            >
              <ImageIcon />
            </Avatar>
            <Box sx={{ flex: 1 }}>
              <Typography variant="body1" fontWeight={600}>
                تصویر آپلود شده
              </Typography>
              <Typography variant="body2" color="textSecondary">
                تصویر با موفقیت آپلود شد
              </Typography>
            </Box>
            <IconButton
              onClick={handleRemoveImage}
              color="error"
              disabled={uploading}
            >
              <DeleteIcon />
            </IconButton>
          </Box>
        </Paper>
      ) : (
        // Upload Area
        <Paper
          sx={{
            p: 4,
            borderRadius: 2,
            border: dragOver ? '2px dashed #1976D2' : '2px dashed #E2E8F0',
            backgroundColor: dragOver ? 'rgba(25, 118, 210, 0.05)' : 'transparent',
            cursor: uploading ? 'not-allowed' : 'pointer',
            transition: 'all 0.3s ease',
            '&:hover': {
              borderColor: uploading ? '#E2E8F0' : '#1976D2',
              backgroundColor: uploading ? 'transparent' : 'rgba(25, 118, 210, 0.05)',
            },
          }}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
        >
          <Box
            sx={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              gap: 2,
              textAlign: 'center',
            }}
          >
            {uploading ? (
              <>
                <CircularProgress size={40} />
                <Typography variant="body1">
                  در حال آپلود...
                </Typography>
              </>
            ) : (
              <>
                <Avatar
                  sx={{
                    width: 60,
                    height: 60,
                    backgroundColor: 'primary.main',
                    background: 'linear-gradient(135deg, #1976D2 0%, #42A5F5 100%)',
                  }}
                >
                  <UploadIcon sx={{ fontSize: 30 }} />
                </Avatar>
                <Box>
                  <Typography variant="h6" fontWeight={600}>
                    {label}
                  </Typography>
                  <Typography variant="body2" color="textSecondary" sx={{ mb: 2 }}>
                    تصویر را اینجا بکشید و رها کنید یا کلیک کنید
                  </Typography>
                  <Typography variant="caption" color="textSecondary">
                    فرمت‌های مجاز: JPG, PNG, GIF - حداکثر {maxSize} مگابایت
                  </Typography>
                </Box>
                <Button
                  component="label"
                  variant="contained"
                  startIcon={<UploadIcon />}
                  disabled={uploading}
                  sx={{
                    borderRadius: 2,
                    background: 'linear-gradient(135deg, #1976D2 0%, #42A5F5 100%)',
                    '&:hover': {
                      background: 'linear-gradient(135deg, #1565C0 0%, #1976D2 100%)',
                    },
                  }}
                >
                  انتخاب فایل
                  <input
                    type="file"
                    hidden
                    accept={accept}
                    onChange={handleFileInputChange}
                    disabled={uploading}
                  />
                </Button>
              </>
            )}
          </Box>
        </Paper>
      )}
    </Box>
  );
};

export default ImageUpload; 