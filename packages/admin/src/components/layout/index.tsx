"use client";

import React, { useState, useEffect } from "react";
import { 
  Box, 
  Drawer, 
  AppBar, 
  Toolbar, 
  Typography, 
  List, 
  ListItemIcon, 
  ListItemText, 
  CssBaseline, 
  Divider, 
  IconButton, 
  useMediaQuery, 
  useTheme,
  Avatar,
  Menu,
  MenuItem,
  ListItemButton
} from "@mui/material";

import { 
  Dashboard as DashboardIcon, 
  Restaurant as RestaurantIcon, 
  MenuBook as MenuIcon, 
  People as PeopleIcon, 
  Star as ReviewIcon, 
  Receipt as OrderIcon, 
  EventSeat as ReservationIcon,
  Menu as MenuOpenIcon,
  Brightness4 as DarkModeIcon,
  Brightness7 as LightModeIcon,
  Logout as LogoutIcon,
  AccountCircle as AccountIcon,
  ChevronLeft as ChevronLeftIcon,
  Category as CategoryIcon,
  AccountBalanceWallet as AccountBalanceWalletIcon,
  Notifications as NotificationsIcon,
  Analytics as AnalyticsIcon,
  Favorite as FavoriteIcon,
  Campaign as CampaignIcon,
  Payment as PaymentIcon,
  Folder as FolderIcon
} from "@mui/icons-material";

import { useColorMode } from "../../contexts/colorMode";
import { useRouter, usePathname } from "next/navigation";

const drawerWidth = 240;

interface LayoutProps {
  children: React.ReactNode;
}

export const Layout: React.FC<LayoutProps> = ({ children }) => {
  const [isClient, setIsClient] = useState(false);
  const router = useRouter();
  const pathname = usePathname();
  const theme = useTheme();
  const { mode, setMode } = useColorMode();
  const isMobile = useMediaQuery(theme.breakpoints.down("md"));
  const [mobileOpen, setMobileOpen] = React.useState(false);
  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);

  useEffect(() => {
    setIsClient(true);
  }, []);

  // Simple function to get page title from pathname
  const getPageTitle = () => {
    const pathToUse = pathname;
    const pathParts = pathToUse.split('/').filter(Boolean);
    if (pathParts.length === 0) return 'Dashboard';
    return pathParts[0].charAt(0).toUpperCase() + pathParts[0].slice(1).replace('-', ' ');
  };

  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleMenu = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleLogout = () => {
    router.push('/login');
    handleClose();
  };

  const handleProfileClick = () => {
    router.push('/profile');
    handleClose();
  };

  const handleNavigate = (path: string) => {
    router.push(path);
    if (isMobile) setMobileOpen(false);
  };

  const menuItems = [
    { text: "Dashboard", icon: <DashboardIcon />, path: "/dashboard" },
    { text: "Restaurants", icon: <RestaurantIcon />, path: "/restaurants/list" },
    { text: "Menu Items", icon: <MenuIcon />, path: "/menu-items/list" },
    { text: "Users", icon: <PeopleIcon />, path: "/users/list" },
    { text: "Orders", icon: <OrderIcon />, path: "/orders/list" },
    { text: "Reservations", icon: <ReservationIcon />, path: "/reservations/list" },
    { text: "Reviews", icon: <ReviewIcon />, path: "/reviews/list" },
    { text: "Categories", icon: <CategoryIcon />, path: "/categories/list" },
    { text: "Credits", icon: <AccountBalanceWalletIcon />, path: "/credits/list" },
    { text: "Notifications", icon: <NotificationsIcon />, path: "/notifications/list" },
    { text: "Analytics", icon: <AnalyticsIcon />, path: "/analytics/dashboard" },
    { text: "Favorites", icon: <FavoriteIcon />, path: "/favorites/list" },
    { text: "Marketing", icon: <CampaignIcon />, path: "/marketing/list" },
    { text: "Payments", icon: <PaymentIcon />, path: "/payments/list" },
    { text: "Files", icon: <FolderIcon />, path: "/files/list" },
  ];

  const drawer = (
    <>
      <Toolbar 
        sx={{ 
          display: "flex", 
          alignItems: "center", 
          justifyContent: "center",
          px: [1],
        }}
      >
        <Typography variant="h6" fontWeight="bold" noWrap>
          از کُجا Admin
        </Typography>
        {isMobile && (
          <IconButton onClick={handleDrawerToggle} sx={{ ml: 'auto' }}>
            <ChevronLeftIcon />
          </IconButton>
        )}
      </Toolbar>
      <Divider />
      <List>
        {menuItems.map((item) => (
          <ListItemButton
            key={item.text}
            selected={pathname === item.path}
            onClick={() => handleNavigate(item.path)}
          >
            <ListItemIcon>{item.icon}</ListItemIcon>
            <ListItemText primary={item.text} />
          </ListItemButton>
        ))}
      </List>
    </>
  );

  return (
    <Box sx={{ display: "flex" }}>
      <CssBaseline />
      <AppBar
        position="fixed"
        sx={{
          width: { md: `calc(100% - ${drawerWidth}px)` },
          ml: { md: `${drawerWidth}px` },
          bgcolor: "background.paper",
          color: "text.primary",
          boxShadow: 1,
        }}
      >
        <Toolbar>
          <IconButton
            color="inherit"
            aria-label="open drawer"
            edge="start"
            onClick={handleDrawerToggle}
            sx={{ mr: 2, display: { md: "none" } }}
          >
            <MenuOpenIcon />
          </IconButton>
          <Typography variant="h6" noWrap component="div">
            {getPageTitle()}
          </Typography>
          <Box sx={{ flexGrow: 1 }} />
          <IconButton onClick={setMode} color="inherit">
            {mode === "dark" ? <LightModeIcon /> : <DarkModeIcon />}
          </IconButton>
          <IconButton
            color="inherit"
            onClick={handleMenu}
          >
            <Avatar sx={{ width: 32, height: 32, bgcolor: "primary.main" }}>
              <AccountIcon />
            </Avatar>
          </IconButton>
          <Menu
            id="menu-appbar"
            anchorEl={anchorEl}
            anchorOrigin={{
              vertical: 'bottom',
              horizontal: 'right',
            }}
            keepMounted
            transformOrigin={{
              vertical: 'top',
              horizontal: 'right',
            }}
            open={Boolean(anchorEl)}
            onClose={handleClose}
          >
            <MenuItem onClick={handleProfileClick}>Profile</MenuItem>
            <MenuItem onClick={handleLogout}>
              <ListItemIcon>
                <LogoutIcon fontSize="small" />
              </ListItemIcon>
              Logout
            </MenuItem>
          </Menu>
        </Toolbar>
      </AppBar>
      <Box
        component="nav"
        sx={{ width: { md: drawerWidth }, flexShrink: { md: 0 } }}
      >
        <Drawer
          variant={isMobile ? "temporary" : "permanent"}
          open={isMobile ? mobileOpen : true}
          onClose={handleDrawerToggle}
          ModalProps={{
            keepMounted: true, // Better open performance on mobile.
          }}
          sx={{
            display: { xs: "block" },
            "& .MuiDrawer-paper": {
              boxSizing: "border-box",
              width: drawerWidth,
            },
          }}
        >
          {drawer}
        </Drawer>
      </Box>
      <Box
        component="main"
        sx={{
          flexGrow: 1,
          p: 3,
          width: { md: `calc(100% - ${drawerWidth}px)` },
          mt: "64px", // AppBar height
        }}
      >
        {children}
      </Box>
    </Box>
  );
};