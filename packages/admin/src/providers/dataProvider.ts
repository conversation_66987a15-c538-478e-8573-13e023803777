import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "@refinedev/core";
import { authProvider } from "./authProvider";

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || "http://localhost:7000/api";

// Helper function to get auth headers
const getAuthHeaders = () => {
  const token = localStorage.getItem('access_token');
  const user = localStorage.getItem('user');
  
  const headers: Record<string, string> = {
    'Content-Type': 'application/json',
  };
  
  if (token) {
    headers['Authorization'] = `Bearer ${token}`;
  }
  
  // Add user context for role-based filtering
  if (user) {
    try {
      const userData = JSON.parse(user);
      headers['X-User-Role'] = userData.role;
      headers['X-User-ID'] = userData.id;
    } catch (error) {
      console.warn('Failed to parse user data:', error);
    }
  }
  
  return headers;
};

// Helper function to handle API responses
const handleResponse = async (response: Response) => {
  if (!response.ok) {
    if (response.status === 401) {
      // Token expired, redirect to login
      if (typeof window !== 'undefined') {
        localStorage.removeItem('access_token');
        localStorage.removeItem('user');
        window.location.href = '/login';
      }
      throw new Error('Unauthorized');
    }
    
    const errorData = await response.json().catch(() => ({ message: 'Unknown error' }));
    throw new Error(errorData.message || `HTTP ${response.status}`);
  }
  
  // Handle empty responses (like DELETE operations)
  const contentType = response.headers.get('content-type');
  if (!contentType || !contentType.includes('application/json')) {
    // Return empty object for non-JSON responses
    return {};
  }
  
  // Check if response has content
  const text = await response.text();
  if (!text || text.trim() === '') {
    return {};
  }
  
  try {
    return JSON.parse(text);
  } catch (error) {
    console.warn('Failed to parse JSON response:', text);
    return {};
  }
};

export const dataProvider: DataProvider = {
  getApiUrl: () => API_BASE_URL,

  // Get list of resources with pagination and filtering
  getList: async ({ resource, pagination, filters, sorters, meta }) => {
    const user = typeof window !== 'undefined' ? localStorage.getItem('user') : null;
    let userRole = null;
    let userId = null;
    
    if (user) {
      try {
        const userData = JSON.parse(user);
        userRole = userData.role;
        userId = userData.id;
      } catch (error) {
        console.warn('Failed to parse user data:', error);
      }
    }

    // Special handling for menu-items - fetch from all restaurants
    if (resource === 'menu-items') {
      try {
        // First get restaurants (filtered by ownership for restaurant owners)
        const restaurantsUrl = new URL(`${API_BASE_URL}/restaurants`);
        if (userRole === 'restaurant_owner' && userId) {
          restaurantsUrl.searchParams.append('owner_id', userId);
        }
        
        const restaurantsResponse = await fetch(restaurantsUrl.toString(), {
          headers: getAuthHeaders(),
        });
        const restaurantsData = await handleResponse(restaurantsResponse);
        const restaurants = Array.isArray(restaurantsData) ? restaurantsData : (restaurantsData.data || []);
        
        // Then fetch menu items from each restaurant
        const allMenuItems = [];
        for (const restaurant of restaurants) {
          try {
            const menuResponse = await fetch(`${API_BASE_URL}/restaurants/${restaurant.id}/menu`, {
              headers: getAuthHeaders(),
            });
            const menuData = await handleResponse(menuResponse);
            const menuItems = Array.isArray(menuData) ? menuData : [];
            
            // Add restaurant info to each menu item
            menuItems.forEach(item => {
              item.restaurant = restaurant;
              item.restaurant_id = restaurant.id;
            });
            
            allMenuItems.push(...menuItems);
          } catch (error) {
            console.warn(`Failed to fetch menu for restaurant ${restaurant.id}:`, error);
          }
        }
        
        // Apply client-side filtering for menu items
        let filteredItems = allMenuItems;
        if (filters && filters.length > 0) {
          filters.forEach((filter) => {
            if (filter.operator === "eq" && filter.value) {
              filteredItems = filteredItems.filter(item => 
                String(item[filter.field]) === String(filter.value)
              );
            }
            if (filter.operator === "contains" && filter.value) {
              const searchValue = filter.value.toLowerCase();
              filteredItems = filteredItems.filter(item =>
                item.name?.toLowerCase().includes(searchValue) ||
                item.description?.toLowerCase().includes(searchValue) ||
                item.category?.toLowerCase().includes(searchValue)
              );
            }
          });
        }
        
        // Apply client-side sorting for menu items
        if (sorters && sorters.length > 0) {
          const sorter = sorters[0];
          filteredItems.sort((a, b) => {
            const aVal = a[sorter.field];
            const bVal = b[sorter.field];
            if (sorter.order === "desc") {
              return bVal > aVal ? 1 : -1;
            }
            return aVal > bVal ? 1 : -1;
          });
        }
        
        // Apply client-side pagination for menu items
        const startIndex = ((pagination?.current || 1) - 1) * (pagination?.pageSize || 10);
        const endIndex = startIndex + (pagination?.pageSize || 10);
        const paginatedItems = filteredItems.slice(startIndex, endIndex);
        
        return {
          data: paginatedItems,
          total: filteredItems.length,
        };
      } catch (error) {
        console.error('Error fetching menu items:', error);
        return { data: [], total: 0 };
      }
    }
    
    // Default handling for other resources
    const url = new URL(`${API_BASE_URL}/${resource}`);
    
    // Add role-based filtering for restaurant owners
    if (userRole === 'restaurant_owner' && userId) {
      if (resource === 'restaurants') {
        url.searchParams.append('owner_id', userId);
      } else if (resource === 'orders') {
        url.searchParams.append('restaurant_owner_id', userId);
      } else if (resource === 'reviews') {
        url.searchParams.append('restaurant_owner_id', userId);
      } else if (resource === 'reservations') {
        url.searchParams.append('restaurant_owner_id', userId);
      }
    }
    
    // Add pagination
    if (pagination) {
      url.searchParams.append("page", String(pagination.current || 1));
      url.searchParams.append("limit", String(pagination.pageSize || 10));
    }
    
    // Add filters
    if (filters && filters.length > 0) {
      filters.forEach((filter) => {
        if (filter.operator === "eq" && filter.value) {
          url.searchParams.append(filter.field, String(filter.value));
        }
        if (filter.operator === "contains" && filter.value) {
          url.searchParams.append("search", String(filter.value));
        }
      });
    }
    
    // Add sorting
    if (sorters && sorters.length > 0) {
      const sorter = sorters[0];
      url.searchParams.append("sortBy", sorter.field);
      url.searchParams.append("sortOrder", sorter.order === "asc" ? "ASC" : "DESC");
    }

    const response = await fetch(url.toString(), {
      headers: getAuthHeaders(),
    });
    
    const data = await handleResponse(response);
    
    // Handle different response formats
    if (Array.isArray(data)) {
      return {
        data,
        total: data.length,
      };
    }
    
    // If backend returns paginated response
    if (data.data && Array.isArray(data.data)) {
      return {
        data: data.data,
        total: data.total || data.count || data.data.length,
      };
    }
    
    return {
      data: data.items || data.restaurants || data.users || [],
      total: data.total || data.count || 0,
    };
  },

  // Get single resource by ID
  getOne: async ({ resource, id }) => {
    const user = typeof window !== 'undefined' ? localStorage.getItem('user') : null;
    let userRole = null;
    let userId = null;
    
    if (user) {
      try {
        const userData = JSON.parse(user);
        userRole = userData.role;
        userId = userData.id;
      } catch (error) {
        console.warn('Failed to parse user data:', error);
      }
    }

    const url = new URL(`${API_BASE_URL}/${resource}/${id}`);
    
    // Add role-based filtering for restaurant owners
    if (userRole === 'restaurant_owner' && userId) {
      if (resource === 'restaurants') {
        url.searchParams.append('owner_id', userId);
      }
    }

    const response = await fetch(url.toString(), {
      headers: getAuthHeaders(),
    });
    
    const data = await handleResponse(response);
    
    // For restaurant owners, verify they own the resource
    if (userRole === 'restaurant_owner' && userId) {
      if (resource === 'restaurants' && data.owner?.id !== userId) {
        throw new Error('Access denied: You can only access your own restaurants');
      }
    }
    
    return { data };
  },

  // Create new resource
  create: async ({ resource, variables }) => {
    // Special handling for menu-items creation
    if (resource === 'menu-items') {
      const menuItemData = variables as any;
      if (!menuItemData.restaurant_id) {
        throw new Error('Restaurant ID is required for menu item creation');
      }
      
      const response = await fetch(`${API_BASE_URL}/restaurants/${menuItemData.restaurant_id}/menu`, {
        method: "POST",
        headers: getAuthHeaders(),
        body: JSON.stringify(variables),
      });
      
      const data = await handleResponse(response);
      return { data };
    }
    
    // Default handling for other resources
    const response = await fetch(`${API_BASE_URL}/${resource}`, {
      method: "POST",
      headers: getAuthHeaders(),
      body: JSON.stringify(variables),
    });
    
    const data = await handleResponse(response);
    return { data };
  },

  // Update resource
  update: async ({ resource, id, variables }) => {
    const user = typeof window !== 'undefined' ? localStorage.getItem('user') : null;
    let userRole = null;
    let userId = null;
    
    if (user) {
      try {
        const userData = JSON.parse(user);
        userRole = userData.role;
        userId = userData.id;
      } catch (error) {
        console.warn('Failed to parse user data:', error);
      }
    }

    // For restaurant owners, verify ownership before updating
    if (userRole === 'restaurant_owner' && userId) {
      if (resource === 'restaurants') {
        // First check if they own this restaurant
        try {
          const checkResponse = await fetch(`${API_BASE_URL}/${resource}/${id}`, {
            headers: getAuthHeaders(),
          });
          const existingData = await handleResponse(checkResponse);
          
          if (existingData.owner?.id !== userId) {
            throw new Error('Access denied: You can only update your own restaurants');
          }
        } catch (error) {
          console.error('Error checking resource ownership:', error);
          throw error;
        }
      }
    }

    const response = await fetch(`${API_BASE_URL}/${resource}/${id}`, {
      method: "PUT",
      headers: getAuthHeaders(),
      body: JSON.stringify(variables),
    });
    
    const data = await handleResponse(response);
    return { data };
  },

  // Delete resource
  deleteOne: async ({ resource, id }) => {
    const user = typeof window !== 'undefined' ? localStorage.getItem('user') : null;
    let userRole = null;
    let userId = null;
    
    if (user) {
      try {
        const userData = JSON.parse(user);
        userRole = userData.role;
        userId = userData.id;
      } catch (error) {
        console.warn('Failed to parse user data:', error);
      }
    }

    // For restaurant owners, verify ownership before deleting
    if (userRole === 'restaurant_owner' && userId) {
      if (resource === 'restaurants') {
        // First check if they own this restaurant
        try {
          const checkResponse = await fetch(`${API_BASE_URL}/${resource}/${id}`, {
            headers: getAuthHeaders(),
          });
          const existingData = await handleResponse(checkResponse);
          
          if (existingData.owner?.id !== userId) {
            throw new Error('Access denied: You can only delete your own restaurants');
          }
        } catch (error) {
          console.error('Error checking resource ownership:', error);
          throw error;
        }
      }
    }

    const response = await fetch(`${API_BASE_URL}/${resource}/${id}`, {
      method: "DELETE",
      headers: getAuthHeaders(),
    });
    
    // Handle the response properly for delete operations
    if (!response.ok) {
      if (response.status === 401) {
        // Token expired, redirect to login
        if (typeof window !== 'undefined') {
          localStorage.removeItem('access_token');
          localStorage.removeItem('user');
          window.location.href = '/login';
        }
        throw new Error('Unauthorized');
      }
      
      const errorData = await response.json().catch(() => ({ message: 'Unknown error' }));
      throw new Error(errorData.message || `HTTP ${response.status}`);
    }
    
    // For successful delete operations, just return the ID
    return { data: { id } as any };
  },

  // Get many resources by IDs
  getMany: async ({ resource, ids }) => {
    const promises = ids.map(id => 
      fetch(`${API_BASE_URL}/${resource}/${id}`, {
        headers: getAuthHeaders(),
      }).then(handleResponse)
    );
    
    const data = await Promise.all(promises);
    return { data };
  },

  // Custom method for dashboard analytics
  custom: async ({ url, method = "GET", payload, query, headers }) => {
    const requestUrl = `${API_BASE_URL}${url}`;
    const searchParams = new URLSearchParams();
    
    if (query) {
      Object.keys(query).forEach(key => {
        const value = (query as Record<string, any>)[key];
        searchParams.append(key, String(value));
      });
    }
    
    const finalUrl = searchParams.toString() 
      ? `${requestUrl}?${searchParams.toString()}`
      : requestUrl;

    const response = await fetch(finalUrl, {
      method,
      headers: {
        ...getAuthHeaders(),
        ...headers,
      },
      ...(payload && { body: JSON.stringify(payload) }),
    });
    
    const data = await handleResponse(response);
    return { data };
  },
}; 