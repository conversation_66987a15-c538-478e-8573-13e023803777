'use client';

import React from 'react';
import { useList, useDelete } from '@refinedev/core';
import {
  Box,
  Card,
  CardContent,
  <PERSON>pography,
  Button,
  Chip,
  IconButton,
  Stack,
  TextField,
  InputAdornment,
  Alert,
  Avatar,
  Grid,
  CardMedia,
  CardActions,
} from '@mui/material';
import {
  Add as AddIcon,
  Search as SearchIcon,
  Visibility as VisibilityIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Download as DownloadIcon,
  Image as ImageIcon,
  VideoFile as VideoIcon,
  AudioFile as AudioIcon,
  InsertDriveFile as FileIcon,
  PictureAsPdf as PdfIcon,
  Description as DocIcon,
} from '@mui/icons-material';
import { DataGrid, GridColDef, GridActionsCellItem } from '@mui/x-data-grid';
import { useRouter } from 'next/navigation';
import { useState } from 'react';

export const dynamic = 'force-dynamic';

interface FileRecord {
  id: number;
  filename: string;
  original_name: string;
  file_path: string;
  file_size: number;
  mime_type: string;
  file_type: 'image' | 'video' | 'audio' | 'document' | 'other';
  uploaded_by: number;
  is_public: boolean;
  description?: string;
  tags?: string[];
  user: {
    name: string;
    email: string;
  };
  created_at: string;
  updated_at: string;
}

export default function FilesListPage() {
  const router = useRouter();
  const [searchTerm, setSearchTerm] = useState('');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('list');
  const { mutate: deleteFile } = useDelete();

  const { data, isLoading, error } = useList<FileRecord>({
    resource: 'files',
    pagination: {
      pageSize: 25,
    },
    filters: searchTerm
      ? [
          {
            field: 'original_name',
            operator: 'contains',
            value: searchTerm,
          },
        ]
      : [],
  });

  const handleDelete = (id: number) => {
    if (window.confirm('Are you sure you want to delete this file?')) {
      deleteFile({
        resource: 'files',
        id,
      });
    }
  };

  const handleDownload = (file: FileRecord) => {
    // Create download link
    const link = document.createElement('a');
    link.href = file.file_path;
    link.download = file.original_name;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const getFileIcon = (mimeType: string, fileType: string) => {
    if (fileType === 'image') return <ImageIcon />;
    if (fileType === 'video') return <VideoIcon />;
    if (fileType === 'audio') return <AudioIcon />;
    if (mimeType.includes('pdf')) return <PdfIcon />;
    if (mimeType.includes('document') || mimeType.includes('text')) return <DocIcon />;
    return <FileIcon />;
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const columns: GridColDef[] = [
    {
      field: 'id',
      headerName: 'ID',
      width: 80,
    },
    {
      field: 'original_name',
      headerName: 'File Name',
      width: 250,
      renderCell: (params) => (
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          {getFileIcon(params.row.mime_type, params.row.file_type)}
          <Box>
            <Typography variant="body2" fontWeight="medium">
              {params.value}
            </Typography>
            <Typography variant="caption" color="text.secondary">
              {params.row.mime_type}
            </Typography>
          </Box>
        </Box>
      ),
    },
    {
      field: 'file_size',
      headerName: 'Size',
      width: 100,
      renderCell: (params) => (
        <Typography variant="body2">
          {formatFileSize(params.value || 0)}
        </Typography>
      ),
    },
    {
      field: 'file_type',
      headerName: 'Type',
      width: 100,
      renderCell: (params) => (
        <Chip
          label={params.value?.toUpperCase() || 'OTHER'}
          size="small"
          variant="outlined"
        />
      ),
    },
    {
      field: 'is_public',
      headerName: 'Visibility',
      width: 100,
      renderCell: (params) => (
        <Chip
          label={params.value ? 'Public' : 'Private'}
          color={params.value ? 'success' : 'default'}
          size="small"
        />
      ),
    },
    {
      field: 'user',
      headerName: 'Uploaded By',
      width: 180,
      renderCell: (params) => (
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Avatar sx={{ width: 32, height: 32, fontSize: '0.875rem' }}>
            {params.row.user?.name?.charAt(0) || 'U'}
          </Avatar>
          <Box>
            <Typography variant="body2" fontWeight="medium">
              {params.row.user?.name || 'Unknown'}
            </Typography>
            <Typography variant="caption" color="text.secondary">
              {params.row.user?.email || 'N/A'}
            </Typography>
          </Box>
        </Box>
      ),
    },
    {
      field: 'created_at',
      headerName: 'Upload Date',
      width: 120,
      renderCell: (params) => (
        <Typography variant="body2">
          {params.value ? new Date(params.value).toLocaleDateString() : 'N/A'}
        </Typography>
      ),
    },
    {
      field: 'actions',
      type: 'actions',
      headerName: 'Actions',
      width: 150,
      getActions: (params) => [
        <GridActionsCellItem
          key="view"
          icon={<VisibilityIcon />}
          label="View"
          onClick={() => router.push(`/files/show/${params.id}`)}
        />,
        <GridActionsCellItem
          key="download"
          icon={<DownloadIcon />}
          label="Download"
          onClick={() => handleDownload(params.row)}
        />,
        <GridActionsCellItem
          key="edit"
          icon={<EditIcon />}
          label="Edit"
          onClick={() => router.push(`/files/edit/${params.id}`)}
        />,
        <GridActionsCellItem
          key="delete"
          icon={<DeleteIcon />}
          label="Delete"
          onClick={() => handleDelete(params.id as number)}
        />,
      ],
    },
  ];

  if (error) {
    return (
      <Alert severity="error">
        Error loading files: {error.message}
      </Alert>
    );
  }

  const renderGridView = () => (
    <Grid container spacing={2}>
      {data?.data?.map((file) => (
        <Grid item xs={12} sm={6} md={4} lg={3} key={file.id}>
          <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
            {file.file_type === 'image' ? (
              <CardMedia
                component="img"
                height="140"
                image={file.file_path}
                alt={file.original_name}
                sx={{ objectFit: 'cover' }}
              />
            ) : (
              <Box
                sx={{
                  height: 140,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  bgcolor: 'grey.100',
                }}
              >
                {getFileIcon(file.mime_type, file.file_type)}
              </Box>
            )}
            <CardContent sx={{ flexGrow: 1 }}>
              <Typography variant="h6" component="div" noWrap>
                {file.original_name}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {formatFileSize(file.file_size || 0)}
              </Typography>
              <Typography variant="caption" color="text.secondary">
                {file.created_at ? new Date(file.created_at).toLocaleDateString() : 'N/A'}
              </Typography>
            </CardContent>
            <CardActions>
              <IconButton
                size="small"
                onClick={() => router.push(`/files/show/${file.id}`)}
              >
                <VisibilityIcon />
              </IconButton>
              <IconButton
                size="small"
                onClick={() => handleDownload(file)}
              >
                <DownloadIcon />
              </IconButton>
              <IconButton
                size="small"
                onClick={() => handleDelete(file.id)}
              >
                <DeleteIcon />
              </IconButton>
            </CardActions>
          </Card>
        </Grid>
      ))}
    </Grid>
  );

  return (
    <Stack spacing={3}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Typography variant="h4" component="h1" fontWeight="bold">
          File Management
        </Typography>
        <Box sx={{ display: 'flex', gap: 1 }}>
          <Button
            variant={viewMode === 'grid' ? 'contained' : 'outlined'}
            onClick={() => setViewMode('grid')}
            size="small"
          >
            Grid
          </Button>
          <Button
            variant={viewMode === 'list' ? 'contained' : 'outlined'}
            onClick={() => setViewMode('list')}
            size="small"
          >
            List
          </Button>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => router.push('/files/create')}
          >
            Upload File
          </Button>
        </Box>
      </Box>

      <Card>
        <CardContent>
          <Box sx={{ mb: 2 }}>
            <TextField
              placeholder="Search files..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                ),
              }}
              sx={{ width: 300 }}
            />
          </Box>

          {viewMode === 'grid' ? (
            renderGridView()
          ) : (
            <DataGrid
              rows={data?.data || []}
              columns={columns}
              loading={isLoading}
              pageSizeOptions={[10, 25, 50]}
              initialState={{
                pagination: {
                  paginationModel: { pageSize: 25 },
                },
              }}
              disableRowSelectionOnClick
              sx={{
                height: 600,
                '& .MuiDataGrid-cell': {
                  borderBottom: '1px solid #f0f0f0',
                },
              }}
            />
          )}
        </CardContent>
      </Card>
    </Stack>
  );
}