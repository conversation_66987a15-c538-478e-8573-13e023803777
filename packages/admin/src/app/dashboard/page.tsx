'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  Avatar,
  LinearProgress,
  IconButton,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Paper,
  Chip,
  Divider,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Tooltip,
  CircularProgress,
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  Restaurant as RestaurantIcon,
  People as PeopleIcon,
  Receipt as OrderIcon,
  EventSeat as ReservationIcon,
  Star as ReviewIcon,
  AttachMoney as RevenueIcon,
  MenuBook as MenuIcon,
  Refresh as RefreshIcon,
  Assessment as AnalyticsIcon,
  DateRange as DateRangeIcon,
  BarChart as ChartIcon,
  Dashboard as DashboardIcon,
  CheckCircle as SuccessIcon,
  Warning as WarningIcon,
  Language as WebsiteIcon,
  ArrowBack as BackIcon,
} from '@mui/icons-material';
import { useList, useInvalidate } from '@refinedev/core';
import { useColorMode } from '../../contexts/colorMode';

// Force dynamic rendering to prevent SSG issues
export const dynamic = 'force-dynamic';

interface DashboardStats {
  restaurants: {
    total: number;
    active: number;
    featured: number;
    growth: number;
  };
  users: {
    total: number;
    customers: number;
    owners: number;
    growth: number;
  };
  orders: {
    total: number;
    today: number;
    pending: number;
    revenue: number;
    growth: number;
  };
  reservations: {
    total: number;
    today: number;
    confirmed: number;
    growth: number;
  };
  reviews: {
    total: number;
    average: number;
    recent: number;
    growth: number;
  };
  menuItems: {
    total: number;
    available: number;
    discounted: number;
    growth: number;
  };
}

export default function AnalyticsDashboard() {
  const router = useRouter();
  const invalidate = useInvalidate();
  const { mode } = useColorMode();
  const [dateRange, setDateRange] = useState('7days');
  const [loading, setLoading] = useState(true);
  const [dashboardStats, setDashboardStats] = useState<DashboardStats | null>(null);

  // Fetch data from multiple resources
  const { data: restaurantsData, isLoading: restaurantsLoading } = useList({
    resource: 'restaurants',
    pagination: { mode: 'off' },
  });

  const { data: usersData, isLoading: usersLoading } = useList({
    resource: 'users',
    pagination: { mode: 'off' },
  });

  const { data: ordersData, isLoading: ordersLoading } = useList({
    resource: 'orders',
    pagination: { mode: 'off' },
  });

  const { data: reservationsData, isLoading: reservationsLoading } = useList({
    resource: 'reservations',
    pagination: { mode: 'off' },
  });

  const { data: menuItemsData, isLoading: menuItemsLoading } = useList({
    resource: 'menu-items',
    pagination: { mode: 'off' },
  });

  useEffect(() => {
    if (!restaurantsLoading && !usersLoading && !ordersLoading && !reservationsLoading && !menuItemsLoading) {
      calculateDashboardStats();
    }
  }, [restaurantsData, usersData, ordersData, reservationsData, menuItemsData, restaurantsLoading, usersLoading, ordersLoading, reservationsLoading, menuItemsLoading]);

  const calculateDashboardStats = () => {
    try {
      const restaurants = restaurantsData?.data || [];
      const users = usersData?.data || [];
      const orders = ordersData?.data || [];
      const reservations = reservationsData?.data || [];
      const menuItems = menuItemsData?.data || [];

      // Calculate today's date for filtering
      const today = new Date().toISOString().split('T')[0];

      const stats: DashboardStats = {
        restaurants: {
          total: restaurants.length,
          active: restaurants.filter((r: any) => r.status === 'active').length,
          featured: restaurants.filter((r: any) => r.is_featured).length,
          growth: 12.5, // Mock growth percentage
        },
        users: {
          total: users.length,
          customers: users.filter((u: any) => u.role === 'customer').length,
          owners: users.filter((u: any) => u.role === 'restaurant_owner').length,
          growth: 8.3, // Mock growth percentage
        },
        orders: {
          total: orders.length,
          today: orders.filter((o: any) => o.created_at?.startsWith(today)).length,
          pending: orders.filter((o: any) => o.status === 'pending').length,
          revenue: orders.reduce((sum: number, o: any) => sum + (Number(o.total) || 0), 0),
          growth: 15.7, // Mock growth percentage
        },
        reservations: {
          total: reservations.length,
          today: reservations.filter((r: any) => r.reservation_date === today).length,
          confirmed: reservations.filter((r: any) => r.status === 'confirmed').length,
          growth: 23.1, // Mock growth percentage
        },
        reviews: {
          total: 0, // Will be calculated when reviews API is available
          average: 4.6,
          recent: 0,
          growth: 18.9, // Mock growth percentage
        },
        menuItems: {
          total: menuItems.length,
          available: menuItems.filter((m: any) => m.is_available).length,
          discounted: menuItems.filter((m: any) => m.discount_type).length,
          growth: 7.2, // Mock growth percentage
        },
      };

      setDashboardStats(stats);
      setLoading(false);
    } catch (error) {
      console.error('Error calculating dashboard stats:', error);
      setLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return `؋${amount.toLocaleString()} افغانی`;
  };

  const formatGrowth = (growth: number) => {
    const isPositive = growth >= 0;
    return (
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
        {isPositive ? (
          <TrendingUpIcon sx={{ fontSize: 16, color: 'success.main' }} />
        ) : (
          <TrendingDownIcon sx={{ fontSize: 16, color: 'error.main' }} />
        )}
        <Typography
          variant="caption"
          sx={{
            color: isPositive ? 'success.main' : 'error.main',
            fontWeight: 600,
          }}
        >
          {isPositive ? '+' : ''}{growth.toFixed(1)}%
        </Typography>
      </Box>
    );
  };

  const getDateRangeLabel = (range: string) => {
    switch (range) {
      case '7days': return '7 روز گذشته';
      case '30days': return '30 روز گذشته';
      case '90days': return '90 روز گذشته';
      case '1year': return '1 سال گذشته';
      default: return '7 روز گذشته';
    }
  };

  const handleNavigation = (path: string) => {
    router.push(path);
  };

  const handleRefreshData = () => {
    // Invalidate all resources to refresh data
    invalidate({
      resource: 'restaurants',
      invalidates: ['list'],
    });
    invalidate({
      resource: 'users',
      invalidates: ['list'],
    });
    invalidate({
      resource: 'orders',
      invalidates: ['list'],
    });
    invalidate({
      resource: 'reservations',
      invalidates: ['list'],
    });
    invalidate({
      resource: 'menu-items',
      invalidates: ['list'],
    });
  };

  if (loading || !dashboardStats) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '60vh' }}>
        <Box sx={{ textAlign: 'center' }}>
          <CircularProgress size={60} sx={{ mb: 2 }} />
          <Typography variant="h6" color="textSecondary">
            در حال بارگذاری داشبورد...
          </Typography>
        </Box>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* Back to Website Button */}
      <Box sx={{ mb: 3 }}>
        <Button
          variant="outlined"
          startIcon={<WebsiteIcon />}
          onClick={() => window.open('http://localhost:3000', '_blank')}
          sx={{
            borderRadius: 3,
            px: 3,
            py: 1.5,
            borderColor: 'primary.main',
            color: 'primary.main',
            background: 'linear-gradient(135deg, rgba(25, 118, 210, 0.05) 0%, rgba(66, 165, 245, 0.05) 100%)',
            '&:hover': {
              borderColor: 'primary.dark',
              background: 'linear-gradient(135deg, rgba(25, 118, 210, 0.1) 0%, rgba(66, 165, 245, 0.1) 100%)',
              transform: 'translateY(-2px)',
              boxShadow: '0 4px 12px rgba(25, 118, 210, 0.2)',
            },
            transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
          }}
        >
          بازگشت به وب‌سایت اصلی
        </Button>
      </Box>

      {/* Header */}
      <Box sx={{ mb: 4 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Box>
            <Typography variant="h4" fontWeight={700} sx={{ mb: 0.5, display: 'flex', alignItems: 'center', gap: 2 }}>
              <Avatar
                sx={{
                  bgcolor: 'primary.main',
                  background: 'linear-gradient(135deg, #1976D2 0%, #42A5F5 100%)',
                  width: 48,
                  height: 48,
                }}
              >
                <DashboardIcon />
              </Avatar>
              داشبورد تحلیلی از کُجا
            </Typography>
            <Typography variant="body1" color="textSecondary">
              نمای کلی از عملکرد سیستم مدیریت رستوران‌ها
            </Typography>
          </Box>
          <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
            <FormControl size="small" sx={{ minWidth: 160 }}>
              <InputLabel>بازه زمانی</InputLabel>
              <Select
                value={dateRange}
                onChange={(e) => setDateRange(e.target.value)}
                label="بازه زمانی"
                startAdornment={<DateRangeIcon sx={{ mr: 1, fontSize: 20 }} />}
              >
                <MenuItem value="7days">7 روز گذشته</MenuItem>
                <MenuItem value="30days">30 روز گذشته</MenuItem>
                <MenuItem value="90days">90 روز گذشته</MenuItem>
                <MenuItem value="1year">1 سال گذشته</MenuItem>
              </Select>
            </FormControl>
            <Tooltip title="بروزرسانی داده‌ها">
              <IconButton 
                onClick={handleRefreshData}
                sx={{ 
                  borderRadius: 2,
                  bgcolor: 'action.hover',
                  '&:hover': { bgcolor: 'action.selected' }
                }}
              >
                <RefreshIcon />
              </IconButton>
            </Tooltip>
          </Box>
        </Box>

        {/* Quick Stats Alert */}
        <Paper sx={{ 
          p: 2, 
          borderRadius: 3, 
          background: 'linear-gradient(135deg, #666CFF 0%, #8589FF 100%)', 
          color: 'white', 
          mb: 3,
          boxShadow: mode === 'light' 
            ? '0 4px 20px rgba(102, 108, 255, 0.3)' 
            : '0 4px 20px rgba(102, 108, 255, 0.5)'
        }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <AnalyticsIcon sx={{ fontSize: 32 }} />
            <Box sx={{ flex: 1 }}>
              <Typography variant="h6" fontWeight={600}>
                خلاصه عملکرد سیستم در {getDateRangeLabel(dateRange)}
              </Typography>
              <Typography variant="body2" sx={{ opacity: 0.9 }}>
                {dashboardStats.restaurants.total} رستوران فعال • {dashboardStats.users.total} کاربر • {dashboardStats.orders.total} سفارش • {formatCurrency(dashboardStats.orders.revenue)} درآمد
              </Typography>
            </Box>
            <Chip
              label="✨ عملکرد عالی"
              sx={{ 
                bgcolor: 'rgba(255,255,255,0.2)', 
                color: 'white',
                fontWeight: 600 
              }}
            />
          </Box>
        </Paper>
      </Box>

      {/* Main Statistics Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        {/* Restaurants */}
        <Grid item xs={12} sm={6} md={4}>
          <Card sx={{ borderRadius: 4, border: '1px solid #E2E8F0', height: '100%' }}>
            <CardContent sx={{ p: 3 }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                <Avatar
                  sx={{
                    bgcolor: 'primary.main',
                    background: 'linear-gradient(135deg, #1976D2 0%, #42A5F5 100%)',
                    width: 56,
                    height: 56,
                  }}
                >
                  <RestaurantIcon sx={{ fontSize: 28 }} />
                </Avatar>
                {formatGrowth(dashboardStats.restaurants.growth)}
              </Box>
              <Typography variant="h3" fontWeight={700} sx={{ mb: 1 }}>
                {dashboardStats.restaurants.total}
              </Typography>
              <Typography variant="body1" color="textSecondary" sx={{ mb: 2 }}>
                کل رستوران‌ها
              </Typography>
              <Box sx={{ display: 'flex', gap: 2, mb: 2 }}>
                <Chip 
                  label={`${dashboardStats.restaurants.active} فعال`} 
                  size="small" 
                  color="success" 
                  variant="outlined" 
                />
                <Chip 
                  label={`${dashboardStats.restaurants.featured} ویژه`} 
                  size="small" 
                  color="warning" 
                  variant="outlined" 
                />
              </Box>
              <LinearProgress 
                variant="determinate" 
                value={(dashboardStats.restaurants.active / dashboardStats.restaurants.total) * 100} 
                sx={{ height: 6, borderRadius: 3 }}
              />
              <Typography variant="caption" color="textSecondary" sx={{ mt: 1, display: 'block' }}>
                {((dashboardStats.restaurants.active / dashboardStats.restaurants.total) * 100).toFixed(1)}% رستوران‌های فعال
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        {/* Users */}
        <Grid item xs={12} sm={6} md={4}>
          <Card sx={{ borderRadius: 4, border: '1px solid #E2E8F0', height: '100%' }}>
            <CardContent sx={{ p: 3 }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                <Avatar
                  sx={{
                    bgcolor: 'secondary.main',
                    background: 'linear-gradient(135deg, #9C27B0 0%, #E1BEE7 100%)',
                    width: 56,
                    height: 56,
                  }}
                >
                  <PeopleIcon sx={{ fontSize: 28 }} />
                </Avatar>
                {formatGrowth(dashboardStats.users.growth)}
              </Box>
              <Typography variant="h3" fontWeight={700} sx={{ mb: 1 }}>
                {dashboardStats.users.total}
              </Typography>
              <Typography variant="body1" color="textSecondary" sx={{ mb: 2 }}>
                کل کاربران
              </Typography>
              <Box sx={{ display: 'flex', gap: 2, mb: 2 }}>
                <Chip 
                  label={`${dashboardStats.users.customers} مشتری`} 
                  size="small" 
                  color="info" 
                  variant="outlined" 
                />
                <Chip 
                  label={`${dashboardStats.users.owners} مالک`} 
                  size="small" 
                  color="secondary" 
                  variant="outlined" 
                />
              </Box>
              <LinearProgress 
                variant="determinate" 
                value={(dashboardStats.users.customers / dashboardStats.users.total) * 100} 
                color="secondary"
                sx={{ height: 6, borderRadius: 3 }}
              />
              <Typography variant="caption" color="textSecondary" sx={{ mt: 1, display: 'block' }}>
                {((dashboardStats.users.customers / dashboardStats.users.total) * 100).toFixed(1)}% مشتریان
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        {/* Revenue */}
        <Grid item xs={12} sm={6} md={4}>
          <Card sx={{ borderRadius: 4, border: '1px solid #E2E8F0', height: '100%' }}>
            <CardContent sx={{ p: 3 }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                <Avatar
                  sx={{
                    bgcolor: 'success.main',
                    background: 'linear-gradient(135deg, #2E7D32 0%, #4CAF50 100%)',
                    width: 56,
                    height: 56,
                  }}
                >
                  <RevenueIcon sx={{ fontSize: 28 }} />
                </Avatar>
                {formatGrowth(dashboardStats.orders.growth)}
              </Box>
              <Typography variant="h3" fontWeight={700} sx={{ mb: 1, fontSize: '1.8rem' }}>
                ؋{(dashboardStats.orders.revenue / 1000).toFixed(0)}K
              </Typography>
              <Typography variant="body1" color="textSecondary" sx={{ mb: 2 }}>
                کل درآمد (افغانی)
              </Typography>
              <Box sx={{ display: 'flex', gap: 2, mb: 2 }}>
                <Chip 
                  label={`${dashboardStats.orders.total} سفارش`} 
                  size="small" 
                  color="success" 
                  variant="outlined" 
                />
                <Chip 
                  label={`${dashboardStats.orders.today} امروز`} 
                  size="small" 
                  color="warning" 
                  variant="outlined" 
                />
              </Box>
              <Typography variant="body2" color="success.main" sx={{ fontWeight: 600 }}>
                میانگین سفارش: {formatCurrency(dashboardStats.orders.total > 0 ? dashboardStats.orders.revenue / dashboardStats.orders.total : 0)}
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        {/* Orders */}
        <Grid item xs={12} sm={6} md={4}>
          <Card sx={{ borderRadius: 4, border: '1px solid #E2E8F0', height: '100%' }}>
            <CardContent sx={{ p: 3 }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                <Avatar
                  sx={{
                    bgcolor: 'warning.main',
                    background: 'linear-gradient(135deg, #ED6C02 0%, #FF9800 100%)',
                    width: 56,
                    height: 56,
                  }}
                >
                  <OrderIcon sx={{ fontSize: 28 }} />
                </Avatar>
                {formatGrowth(dashboardStats.orders.growth)}
              </Box>
              <Typography variant="h3" fontWeight={700} sx={{ mb: 1 }}>
                {dashboardStats.orders.total}
              </Typography>
              <Typography variant="body1" color="textSecondary" sx={{ mb: 2 }}>
                کل سفارشات
              </Typography>
              <Box sx={{ display: 'flex', gap: 2, mb: 2 }}>
                <Chip 
                  label={`${dashboardStats.orders.pending} در انتظار`} 
                  size="small" 
                  color="warning" 
                  variant="outlined" 
                />
                <Chip 
                  label={`${dashboardStats.orders.today} امروز`} 
                  size="small" 
                  color="info" 
                  variant="outlined" 
                />
              </Box>
              <LinearProgress 
                variant="determinate" 
                value={dashboardStats.orders.total > 0 ? ((dashboardStats.orders.total - dashboardStats.orders.pending) / dashboardStats.orders.total) * 100 : 0}
                color="warning"
                sx={{ height: 6, borderRadius: 3 }}
              />
              <Typography variant="caption" color="textSecondary" sx={{ mt: 1, display: 'block' }}>
                {dashboardStats.orders.total > 0 ? (((dashboardStats.orders.total - dashboardStats.orders.pending) / dashboardStats.orders.total) * 100).toFixed(1) : 0}% تکمیل شده
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        {/* Reservations */}
        <Grid item xs={12} sm={6} md={4}>
          <Card sx={{ borderRadius: 4, border: '1px solid #E2E8F0', height: '100%' }}>
            <CardContent sx={{ p: 3 }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                <Avatar
                  sx={{
                    bgcolor: 'info.main',
                    background: 'linear-gradient(135deg, #0288D1 0%, #03A9F4 100%)',
                    width: 56,
                    height: 56,
                  }}
                >
                  <ReservationIcon sx={{ fontSize: 28 }} />
                </Avatar>
                {formatGrowth(dashboardStats.reservations.growth)}
              </Box>
              <Typography variant="h3" fontWeight={700} sx={{ mb: 1 }}>
                {dashboardStats.reservations.total}
              </Typography>
              <Typography variant="body1" color="textSecondary" sx={{ mb: 2 }}>
                کل رزروها
              </Typography>
              <Box sx={{ display: 'flex', gap: 2, mb: 2 }}>
                <Chip 
                  label={`${dashboardStats.reservations.confirmed} تأیید شده`} 
                  size="small" 
                  color="success" 
                  variant="outlined" 
                />
                <Chip 
                  label={`${dashboardStats.reservations.today} امروز`} 
                  size="small" 
                  color="info" 
                  variant="outlined" 
                />
              </Box>
              <LinearProgress 
                variant="determinate" 
                value={dashboardStats.reservations.total > 0 ? (dashboardStats.reservations.confirmed / dashboardStats.reservations.total) * 100 : 0}
                color="info"
                sx={{ height: 6, borderRadius: 3 }}
              />
              <Typography variant="caption" color="textSecondary" sx={{ mt: 1, display: 'block' }}>
                {dashboardStats.reservations.total > 0 ? ((dashboardStats.reservations.confirmed / dashboardStats.reservations.total) * 100).toFixed(1) : 0}% تأیید شده
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        {/* Menu Items */}
        <Grid item xs={12} sm={6} md={4}>
          <Card sx={{ borderRadius: 4, border: '1px solid #E2E8F0', height: '100%' }}>
            <CardContent sx={{ p: 3 }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                <Avatar
                  sx={{
                    bgcolor: 'error.main',
                    background: 'linear-gradient(135deg, #D32F2F 0%, #F44336 100%)',
                    width: 56,
                    height: 56,
                  }}
                >
                  <MenuIcon sx={{ fontSize: 28 }} />
                </Avatar>
                {formatGrowth(dashboardStats.menuItems.growth)}
              </Box>
              <Typography variant="h3" fontWeight={700} sx={{ mb: 1 }}>
                {dashboardStats.menuItems.total}
              </Typography>
              <Typography variant="body1" color="textSecondary" sx={{ mb: 2 }}>
                آیتم‌های منو
              </Typography>
              <Box sx={{ display: 'flex', gap: 2, mb: 2 }}>
                <Chip 
                  label={`${dashboardStats.menuItems.available} موجود`} 
                  size="small" 
                  color="success" 
                  variant="outlined" 
                />
                <Chip 
                  label={`${dashboardStats.menuItems.discounted} تخفیف‌دار`} 
                  size="small" 
                  color="error" 
                  variant="outlined" 
                />
              </Box>
              <LinearProgress 
                variant="determinate" 
                value={dashboardStats.menuItems.total > 0 ? (dashboardStats.menuItems.available / dashboardStats.menuItems.total) * 100 : 0}
                color="error"
                sx={{ height: 6, borderRadius: 3 }}
              />
              <Typography variant="caption" color="textSecondary" sx={{ mt: 1, display: 'block' }}>
                {dashboardStats.menuItems.total > 0 ? ((dashboardStats.menuItems.available / dashboardStats.menuItems.total) * 100).toFixed(1) : 0}% موجود
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Quick Actions & System Status */}
      <Grid container spacing={3}>
        {/* Quick Actions */}
        <Grid item xs={12} md={6}>
          <Card sx={{ borderRadius: 4, border: '1px solid #E2E8F0', height: '100%' }}>
            <CardContent sx={{ p: 3 }}>
              <Typography variant="h6" fontWeight={600} sx={{ mb: 3, display: 'flex', alignItems: 'center', gap: 1 }}>
                <ChartIcon sx={{ color: 'primary.main' }} />
                عملیات سریع
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={6}>
                  <Button
                    fullWidth
                    variant="outlined"
                    startIcon={<RestaurantIcon />}
                    sx={{ py: 2, borderRadius: 3 }}
                    onClick={() => handleNavigation('/restaurants/list')}
                  >
                    مدیریت رستوران‌ها
                  </Button>
                </Grid>
                <Grid item xs={6}>
                  <Button
                    fullWidth
                    variant="outlined"
                    startIcon={<OrderIcon />}
                    sx={{ py: 2, borderRadius: 3 }}
                    onClick={() => handleNavigation('/orders/list')}
                  >
                    مشاهده سفارشات
                  </Button>
                </Grid>
                <Grid item xs={6}>
                  <Button
                    fullWidth
                    variant="outlined"
                    startIcon={<ReservationIcon />}
                    sx={{ py: 2, borderRadius: 3 }}
                    onClick={() => handleNavigation('/reservations/list')}
                  >
                    مدیریت رزروها
                  </Button>
                </Grid>
                <Grid item xs={6}>
                  <Button
                    fullWidth
                    variant="outlined"
                    startIcon={<PeopleIcon />}
                    sx={{ py: 2, borderRadius: 3 }}
                    onClick={() => handleNavigation('/users/list')}
                  >
                    مدیریت کاربران
                  </Button>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* System Status */}
        <Grid item xs={12} md={6}>
          <Card sx={{ borderRadius: 4, border: '1px solid #E2E8F0', height: '100%' }}>
            <CardContent sx={{ p: 3 }}>
              <Typography variant="h6" fontWeight={600} sx={{ mb: 3, display: 'flex', alignItems: 'center', gap: 1 }}>
                <AnalyticsIcon sx={{ color: 'success.main' }} />
                وضعیت سیستم
              </Typography>
              <List sx={{ p: 0 }}>
                <ListItem sx={{ px: 0, py: 1 }}>
                  <ListItemIcon sx={{ minWidth: 36 }}>
                    <SuccessIcon sx={{ color: 'success.main', fontSize: 20 }} />
                  </ListItemIcon>
                  <ListItemText 
                    primary="سرور پایگاه داده" 
                    secondary="آنلاین و عملکرد مطلوب"
                    primaryTypographyProps={{ fontSize: '0.9rem', fontWeight: 500 }}
                    secondaryTypographyProps={{ fontSize: '0.8rem' }}
                  />
                  <Chip label="99.9%" size="small" color="success" />
                </ListItem>
                <Divider />
                <ListItem sx={{ px: 0, py: 1 }}>
                  <ListItemIcon sx={{ minWidth: 36 }}>
                    <SuccessIcon sx={{ color: 'success.main', fontSize: 20 }} />
                  </ListItemIcon>
                  <ListItemText 
                    primary="سرور اپلیکیشن" 
                    secondary="عملکرد عالی"
                    primaryTypographyProps={{ fontSize: '0.9rem', fontWeight: 500 }}
                    secondaryTypographyProps={{ fontSize: '0.8rem' }}
                  />
                  <Chip label="100%" size="small" color="success" />
                </ListItem>
                <Divider />
                <ListItem sx={{ px: 0, py: 1 }}>
                  <ListItemIcon sx={{ minWidth: 36 }}>
                    <WarningIcon sx={{ color: 'warning.main', fontSize: 20 }} />
                  </ListItemIcon>
                  <ListItemText 
                    primary="سرویس پیامک" 
                    secondary="تأخیر جزئی در ارسال"
                    primaryTypographyProps={{ fontSize: '0.9rem', fontWeight: 500 }}
                    secondaryTypographyProps={{ fontSize: '0.8rem' }}
                  />
                  <Chip label="95%" size="small" color="warning" />
                </ListItem>
                <Divider />
                <ListItem sx={{ px: 0, py: 1 }}>
                  <ListItemIcon sx={{ minWidth: 36 }}>
                    <SuccessIcon sx={{ color: 'success.main', fontSize: 20 }} />
                  </ListItemIcon>
                  <ListItemText 
                    primary="سیستم پرداخت" 
                    secondary="فعال و امن"
                    primaryTypographyProps={{ fontSize: '0.9rem', fontWeight: 500 }}
                    secondaryTypographyProps={{ fontSize: '0.8rem' }}
                  />
                  <Chip label="100%" size="small" color="success" />
                </ListItem>
              </List>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
} 