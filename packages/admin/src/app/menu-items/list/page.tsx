'use client';

import React, { useState } from 'react';
import {
  useDataGrid,
  useAutocomplete,
} from '@refinedev/mui';
import TableActionButtons from '../../../components/TableActionButtons';
import { DataGrid, GridColDef } from '@mui/x-data-grid';
import { 
  Box, 
  Button, 
  Card, 
  CardContent, 
  Stack, 
  Typography,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  IconButton,
  Chip,
  Avatar,
  Tooltip,
  Badge,
  Paper,
  FormControlLabel,
  Switch,
  Autocomplete,
} from '@mui/material';
import { useCreate, useInvalidate, useMany } from '@refinedev/core';
import { 
  Add, 
  Close, 
  Restaurant as RestaurantIcon,
  MenuBook as MenuIcon,
  LocalOffer as DiscountIcon,
  AttachMoney as PriceIcon,
  Category as CategoryIcon,
  Image as ImageIcon,
  Visibility as AvailableIcon,
  VisibilityOff as UnavailableIcon,
  Search as SearchIcon,
  Refresh as RefreshIcon,
  FilterList as FilterIcon,
} from '@mui/icons-material';
import { useForm } from '@refinedev/react-hook-form';
import { Controller } from 'react-hook-form';
import ImageUpload from '../../../components/ImageUpload';

// Force dynamic rendering to prevent SSG issues
export const dynamic = 'force-dynamic';

interface MenuItemFormData {
  restaurant_id: string;
  name: string;
  description?: string;
  price: number;
  image_url?: string;
  is_available?: boolean;
  category?: string;
  discount_type?: 'percentage' | 'fixed_amount';
  discount_value?: number;
  discount_start_date?: string;
  discount_end_date?: string;
}

export default function MenuItemList() {
  const invalidate = useInvalidate();
  const [openCreateModal, setOpenCreateModal] = useState(false);
  const [filterCategory, setFilterCategory] = useState('');
  const [filterRestaurant, setFilterRestaurant] = useState('');
  const [searchQuery, setSearchQuery] = useState('');
  const { mutate: createMenuItem } = useCreate();

  const { dataGridProps } = useDataGrid({
    resource: 'menu-items',
    syncWithLocation: true,
    pagination: {
      mode: 'server',
    },
    filters: {
      mode: 'server',
      initial: [
        ...(filterCategory ? [{
          field: 'category',
          operator: 'eq',
          value: filterCategory,
        }] : []),
        ...(filterRestaurant ? [{
          field: 'restaurant_id',
          operator: 'eq',
          value: filterRestaurant,
        }] : []),
        ...(searchQuery ? [{
          field: 'search',
          operator: 'contains',
          value: searchQuery,
        }] : []),
      ],
    },
    sorters: {
      mode: 'server',
      initial: [
        {
          field: 'created_at',
          order: 'desc',
        },
      ],
    },
  });

  // Get restaurant data for the menu items
  const { data: restaurantData } = useMany({
    resource: 'restaurants',
    ids: dataGridProps?.rows?.map((item: any) => item.restaurant?.id) ?? [],
    queryOptions: {
      enabled: dataGridProps?.rows?.length > 0,
    },
  });

  // Create a lookup map for restaurants
  const restaurantMap: Record<string, string> = {};
  if (restaurantData?.data) {
    restaurantData.data.forEach((item: any) => {
      restaurantMap[item.id.toString()] = item.name;
    });
  }

  // Form for creating menu item
  const {
    register,
    control,
    formState: { errors },
    handleSubmit,
    reset,
    watch,
  } = useForm<MenuItemFormData>();

  // Watch discount type to conditionally show discount fields
  const discountType = watch('discount_type');

  // Autocomplete for restaurants
  const { autocompleteProps: restaurantAutocompleteProps } = useAutocomplete({
    resource: 'restaurants',
    onSearch: (value) => [
      {
        field: 'name',
        operator: 'contains',
        value,
      },
    ],
  });

  const handleCreateMenuItem = (data: MenuItemFormData) => {
    try {
      console.log('Creating menu item with data:', data);
      
      // Validate required fields
      if (!data.restaurant_id || !data.name || !data.price) {
        alert('لطفاً تمام فیلدهای الزامی را پر کنید');
        return;
      }

      // Clean the data to match backend DTO
      const cleanData = {
        restaurant_id: data.restaurant_id,
        name: data.name.trim(),
        description: data.description?.trim() || undefined,
        price: Number(data.price),
        image_url: data.image_url?.trim() || undefined,
        is_available: data.is_available ?? true,
        category: data.category?.trim() || undefined,
        discount_type: data.discount_type || undefined,
        discount_value: data.discount_value ? Number(data.discount_value) : undefined,
        discount_start_date: data.discount_start_date || undefined,
        discount_end_date: data.discount_end_date || undefined,
      };

      createMenuItem(
        {
          resource: 'menu-items',
          values: cleanData,
        },
        {
          onSuccess: (response) => {
            console.log('Menu item created successfully:', response);
            setOpenCreateModal(false);
            reset();
            // Invalidate the menu items list to refresh data
            invalidate({
              resource: 'menu-items',
              invalidates: ['list'],
            });
          },
          onError: (error) => {
            console.error('Error creating menu item:', error);
            alert('خطا در ایجاد آیتم منو: ' + error.message);
          },
        }
      );
    } catch (error) {
      console.error('Error in handleCreateMenuItem:', error);
    }
  };

  const getCategoryIcon = (category: string) => {
    const categoryIcons: Record<string, string> = {
      'pizza': '🍕',
      'burger': '🍔',
      'pasta': '🍝',
      'salad': '🥗',
      'dessert': '🍰',
      'drink': '🥤',
      'appetizer': '🥙',
      'main': '🍽️',
      'soup': '🍲',
      'seafood': '🦐',
      'meat': '🥩',
      'vegetarian': '🥬',
      'breakfast': '🍳',
      'lunch': '🍱',
      'dinner': '🍽️',
    };
    
    const lowerCategory = category?.toLowerCase() || '';
    for (const [key, emoji] of Object.entries(categoryIcons)) {
      if (lowerCategory.includes(key)) {
        return emoji;
      }
    }
    return '🍽️'; // Default food emoji
  };

  const getAvailabilityColor = (isAvailable: boolean) => {
    return isAvailable ? 'success' : 'error';
  };

  const getAvailabilityLabel = (isAvailable: boolean) => {
    return isAvailable ? 'موجود' : 'ناموجود';
  };

  const getAvailabilityIcon = (isAvailable: boolean) => {
    return isAvailable ? <AvailableIcon sx={{ fontSize: 16 }} /> : <UnavailableIcon sx={{ fontSize: 16 }} />;
  };

  // Calculate menu statistics
  const totalItems = dataGridProps?.rowCount || 0;
  const availableItems = dataGridProps?.rows?.filter((item: any) => item.is_available).length || 0;
  const unavailableItems = totalItems - availableItems;
  const discountedItems = dataGridProps?.rows?.filter((item: any) => item.discount_type).length || 0;

  // Get unique categories for statistics
  const categories = [...new Set(dataGridProps?.rows?.map((item: any) => item.category).filter(Boolean))] || [];

  const columns: GridColDef[] = [
    {
      field: 'id',
      headerName: 'شناسه',
      type: 'number',
      minWidth: 80,
    },
    {
      field: 'name',
      headerName: 'نام غذا',
      minWidth: 250,
      flex: 1,
      renderCell: ({ row }) => (
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1.5, py: 1 }}>
          <Avatar 
            sx={{ 
              bgcolor: 'primary.main',
              width: 40,
              height: 40,
              fontSize: '1.2rem',
            }}
            src={row.image_url}
          >
            {row.image_url ? undefined : getCategoryIcon(row.category)}
          </Avatar>
          <Box>
            <Typography variant="subtitle2" fontWeight={600}>
              {row.name}
            </Typography>
            <Typography variant="caption" color="textSecondary">
              {row.description ? row.description.substring(0, 50) + '...' : 'بدون توضیحات'}
            </Typography>
          </Box>
        </Box>
      ),
    },
    {
      field: 'restaurant',
      headerName: 'رستوران',
      minWidth: 200,
      renderCell: ({ row }) => (
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <RestaurantIcon sx={{ fontSize: 16, color: 'text.secondary' }} />
          <Typography variant="body2">
            {row.restaurant?.name || 'نامشخص'}
          </Typography>
        </Box>
      ),
    },
    {
      field: 'price',
      headerName: 'قیمت',
      minWidth: 120,
      renderCell: ({ row }) => (
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <PriceIcon sx={{ fontSize: 16, color: 'success.main' }} />
          <Typography variant="body2" fontWeight={600} color="success.main">
            ؋{row.price ? Number(row.price).toLocaleString() : '0'} افغانی
          </Typography>
        </Box>
      ),
    },
    {
      field: 'category',
      headerName: 'دسته‌بندی',
      minWidth: 150,
      renderCell: ({ row }) => (
        <Chip
          label={row.category || 'عمومی'}
          icon={<span style={{ fontSize: '16px' }}>{getCategoryIcon(row.category)}</span>}
          size="small"
          variant="outlined"
          sx={{ fontWeight: 500 }}
        />
      ),
    },
    {
      field: 'is_available',
      headerName: 'وضعیت',
      minWidth: 120,
      renderCell: ({ row }) => (
        <Chip
          icon={getAvailabilityIcon(row.is_available)}
          label={getAvailabilityLabel(row.is_available)}
          color={getAvailabilityColor(row.is_available) as any}
          size="small"
          sx={{ fontWeight: 500 }}
        />
      ),
    },
    {
      field: 'discount',
      headerName: 'تخفیف',
      minWidth: 120,
      renderCell: ({ row }) => (
        row.discount_type ? (
          <Chip
            icon={<DiscountIcon sx={{ fontSize: 16 }} />}
            label={`${row.discount_value}${row.discount_type === 'percentage' ? '%' : ' افغانی'}`}
            color="warning"
            size="small"
            sx={{ fontWeight: 500 }}
          />
        ) : (
          <Typography variant="body2" color="textSecondary">
            ندارد
          </Typography>
        )
      ),
    },
    {
      field: 'actions',
      headerName: 'عملیات',
      sortable: false,
      renderCell: function render({ row }) {
        return (
          <TableActionButtons 
            recordItemId={row.id} 
            resource="menu-items"
          />
        );
      },
      align: 'center',
      headerAlign: 'center',
      minWidth: 120, // Reduced width for icon-only buttons
    },
  ];

  return (
    <>
      {/* Header Section */}
      <Box sx={{ mb: 3 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Box>
            <Typography variant="h4" fontWeight={700} sx={{ mb: 0.5 }}>
              مدیریت منوها
            </Typography>
            <Typography variant="body1" color="textSecondary">
              مدیریت آیتم‌های منو، قیمت‌ها و موجودی
            </Typography>
          </Box>
          <Button
            variant="contained"
            size="large"
            startIcon={<Add />}
            onClick={() => setOpenCreateModal(true)}
            sx={{
              borderRadius: 3,
              px: 3,
              py: 1.5,
              background: 'linear-gradient(135deg, #7C3AED 0%, #A855F7 100%)',
              boxShadow: '0 4px 12px rgb(124 58 237 / 0.3)',
              '&:hover': {
                background: 'linear-gradient(135deg, #6D28D9 0%, #9333EA 100%)',
                boxShadow: '0 6px 16px rgb(124 58 237 / 0.4)',
                transform: 'translateY(-2px)',
              },
              transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
            }}
          >
            افزودن آیتم منو جدید
          </Button>
        </Box>

        {/* Statistics Cards */}
        <Grid container spacing={3} sx={{ mb: 3 }}>
          <Grid item xs={12} sm={6} md={3}>
            <Card sx={{ borderRadius: 3, border: '1px solid #E2E8F0' }}>
              <CardContent sx={{ textAlign: 'center', py: 2 }}>
                <Badge badgeContent={totalItems} color="primary" max={999}>
                  <MenuIcon sx={{ fontSize: 40, color: 'primary.main', mb: 1 }} />
                </Badge>
                <Typography variant="h6" fontWeight={600}>
                  کل آیتم‌ها
                </Typography>
                <Typography variant="body2" color="textSecondary">
                  تمام آیتم‌های منو
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card sx={{ borderRadius: 3, border: '1px solid #E2E8F0' }}>
              <CardContent sx={{ textAlign: 'center', py: 2 }}>
                <Badge badgeContent={availableItems} color="success" max={999}>
                  <AvailableIcon sx={{ fontSize: 40, color: 'success.main', mb: 1 }} />
                </Badge>
                <Typography variant="h6" fontWeight={600}>
                  موجود
                </Typography>
                <Typography variant="body2" color="textSecondary">
                  آیتم‌های موجود
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card sx={{ borderRadius: 3, border: '1px solid #E2E8F0' }}>
              <CardContent sx={{ textAlign: 'center', py: 2 }}>
                <Badge badgeContent={unavailableItems} color="error" max={999}>
                  <UnavailableIcon sx={{ fontSize: 40, color: 'error.main', mb: 1 }} />
                </Badge>
                <Typography variant="h6" fontWeight={600}>
                  ناموجود
                </Typography>
                <Typography variant="body2" color="textSecondary">
                  آیتم‌های ناموجود
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card sx={{ borderRadius: 3, border: '1px solid #E2E8F0' }}>
              <CardContent sx={{ textAlign: 'center', py: 2 }}>
                <Badge badgeContent={discountedItems} color="warning" max={999}>
                  <DiscountIcon sx={{ fontSize: 40, color: 'warning.main', mb: 1 }} />
                </Badge>
                <Typography variant="h6" fontWeight={600}>
                  تخفیف‌دار
                </Typography>
                <Typography variant="body2" color="textSecondary">
                  آیتم‌های تخفیف‌دار
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Filters Section */}
        <Paper sx={{ p: 2, borderRadius: 3, border: '1px solid #E2E8F0', mb: 3 }}>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                size="small"
                placeholder="جستجو در نام غذا..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                InputProps={{
                  startAdornment: <SearchIcon sx={{ color: 'text.secondary', mr: 1 }} />,
                }}
              />
            </Grid>
            <Grid item xs={12} md={3}>
              <FormControl fullWidth size="small">
                <InputLabel>فیلتر دسته‌بندی</InputLabel>
                <Select
                  value={filterCategory}
                  onChange={(e) => setFilterCategory(e.target.value)}
                  label="فیلتر دسته‌بندی"
                >
                  <MenuItem value="">همه دسته‌ها</MenuItem>
                  {categories.map((category) => (
                    <MenuItem key={category} value={category}>
                      {getCategoryIcon(category)} {category}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={5}>
              <Box sx={{ display: 'flex', gap: 1, justifyContent: 'flex-end' }}>
                <Tooltip title="بروزرسانی">
                  <IconButton 
                    onClick={() => invalidate({ resource: 'menu-items', invalidates: ['list'] })}
                    sx={{ borderRadius: 2 }}
                  >
                    <RefreshIcon />
                  </IconButton>
                </Tooltip>
              </Box>
            </Grid>
          </Grid>
        </Paper>
      </Box>

      {/* Data Grid */}
      <Card 
        sx={{ 
          borderRadius: 4,
          border: '1px solid #E2E8F0',
          boxShadow: '0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)',
          overflow: 'hidden',
        }}
      >
        <CardContent sx={{ p: 0 }}>
          <DataGrid
            {...dataGridProps}
            columns={columns}
            autoHeight
            pageSizeOptions={[10, 25, 50, 100]}
            density="comfortable"
            sx={{
              border: 'none',
              '& .MuiDataGrid-cell': {
                borderBottom: '1px solid #F1F5F9',
                py: 2,
              },
              '& .MuiDataGrid-columnHeaders': {
                backgroundColor: '#F8FAFC',
                borderBottom: '1px solid #E2E8F0',
                '& .MuiDataGrid-columnHeader': {
                  fontWeight: 600,
                  fontSize: '0.875rem',
                },
              },
              '& .MuiDataGrid-row': {
                '&:hover': {
                  backgroundColor: '#F8FAFC',
                },
                '&.Mui-selected': {
                  backgroundColor: '#EDE9FE',
                  '&:hover': {
                    backgroundColor: '#DDD6FE',
                  },
                },
              },
              '& .MuiDataGrid-footerContainer': {
                borderTop: '1px solid #E2E8F0',
                backgroundColor: '#F8FAFC',
              },
            }}
          />
        </CardContent>
      </Card>

      {/* Create Menu Item Modal */}
      <Dialog 
        open={openCreateModal} 
        onClose={() => setOpenCreateModal(false)}
        maxWidth="md"
        fullWidth
        PaperProps={{
          sx: {
            borderRadius: 4,
            boxShadow: '0 25px 50px -12px rgb(0 0 0 / 0.25)',
          },
        }}
      >
        <DialogTitle>
          <Box display="flex" justifyContent="space-between" alignItems="center">
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <Avatar
                sx={{
                  bgcolor: 'primary.main',
                  background: 'linear-gradient(135deg, #7C3AED 0%, #A855F7 100%)',
                }}
              >
                <MenuIcon />
              </Avatar>
              <Box>
                <Typography variant="h6" fontWeight={600}>
                  افزودن آیتم منو جدید
                </Typography>
                <Typography variant="body2" color="textSecondary">
                  اطلاعات آیتم منو را وارد کنید
                </Typography>
              </Box>
            </Box>
            <IconButton 
              onClick={() => setOpenCreateModal(false)}
              sx={{ 
                borderRadius: 2,
                '&:hover': { bgcolor: 'action.hover' }
              }}
            >
              <Close />
            </IconButton>
          </Box>
        </DialogTitle>
        
        <form onSubmit={handleSubmit((data) => handleCreateMenuItem(data as MenuItemFormData))}>
          <DialogContent sx={{ px: 3 }}>
            <Grid container spacing={3}>
              {/* Basic Information */}
              <Grid item xs={12}>
                <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                  🍽️ اطلاعات پایه
                </Typography>
                <Grid container spacing={2}>
                  <Grid item xs={12} md={6}>
                    <Controller
                      control={control}
                      name="restaurant_id"
                      rules={{ required: 'انتخاب رستوران الزامی است' }}
                      render={({ field }) => (
                        <Autocomplete
                          {...restaurantAutocompleteProps}
                          {...field}
                          onChange={(_, value) => field.onChange(value?.id)}
                          getOptionLabel={(item) => item.name || ''}
                          isOptionEqualToValue={(option, value) =>
                            value === undefined || option?.id?.toString() === value?.id?.toString()
                          }
                          renderInput={(params) => (
                            <TextField
                              {...params}
                              label="رستوران"
                              margin="normal"
                              variant="outlined"
                              error={!!(errors as any)?.restaurant_id}
                              helperText={(errors as any)?.restaurant_id?.message}
                              required
                            />
                          )}
                        />
                      )}
                    />
                  </Grid>
                  
                  <Grid item xs={12} md={6}>
                    <TextField
                      {...register('name', {
                        required: 'نام غذا الزامی است',
                      })}
                      error={!!(errors as any)?.name}
                      helperText={(errors as any)?.name?.message}
                      margin="normal"
                      fullWidth
                      label="نام غذا"
                      name="name"
                      placeholder="نام غذا را وارد کنید"
                    />
                  </Grid>
                  
                  <Grid item xs={12}>
                    <TextField
                      {...register('description')}
                      margin="normal"
                      fullWidth
                      multiline
                      rows={3}
                      label="توضیحات"
                      name="description"
                      placeholder="توضیحات کوتاهی درباره غذا..."
                    />
                  </Grid>
                </Grid>
              </Grid>

              {/* Pricing & Availability */}
              <Grid item xs={12}>
                <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                  💰 قیمت و موجودی
                </Typography>
                <Grid container spacing={2}>
                  <Grid item xs={12} md={4}>
                    <TextField
                      {...register('price', {
                        required: 'قیمت الزامی است',
                        min: { value: 0, message: 'قیمت نمی‌تواند منفی باشد' }
                      })}
                      error={!!(errors as any)?.price}
                      helperText={(errors as any)?.price?.message}
                      margin="normal"
                      fullWidth
                      type="number"
                      label="قیمت (افغانی)"
                      name="price"
                      placeholder="0"
                    />
                  </Grid>
                  
                  <Grid item xs={12} md={4}>
                    <TextField
                      {...register('category')}
                      margin="normal"
                      fullWidth
                      label="دسته‌بندی"
                      name="category"
                      placeholder="مثال: پیتزا، برگر، نوشیدنی"
                    />
                  </Grid>
                  
                  <Grid item xs={12} md={4}>
                    <FormControlLabel
                      control={
                        <Switch
                          {...register('is_available')}
                          defaultChecked={true}
                        />
                      }
                      label="موجود است"
                      sx={{ mt: 2 }}
                    />
                  </Grid>
                </Grid>
              </Grid>

              {/* Image */}
              <Grid item xs={12}>
                <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                  🖼️ تصویر
                </Typography>
                <Controller
                  control={control}
                  name="image_url"
                  render={({ field }) => (
                    <ImageUpload
                      value={field.value}
                      onChange={field.onChange}
                      error={!!(errors as any)?.image_url}
                      helperText={(errors as any)?.image_url?.message}
                    />
                  )}
                />
              </Grid>

              {/* Discount Section */}
              <Grid item xs={12}>
                <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                  🏷️ تخفیف (اختیاری)
                </Typography>
                <Grid container spacing={2}>
                  <Grid item xs={12} md={4}>
                    <Controller
                      control={control}
                      name="discount_type"
                      render={({ field }) => (
                        <FormControl fullWidth margin="normal">
                          <InputLabel>نوع تخفیف</InputLabel>
                          <Select
                            {...field}
                            label="نوع تخفیف"
                          >
                            <MenuItem value="">بدون تخفیف</MenuItem>
                            <MenuItem value="percentage">درصدی</MenuItem>
                            <MenuItem value="fixed_amount">مبلغ ثابت</MenuItem>
                          </Select>
                        </FormControl>
                      )}
                    />
                  </Grid>
                  
                  {discountType && (
                    <>
                      <Grid item xs={12} md={4}>
                        <TextField
                          {...register('discount_value', {
                            min: { value: 0, message: 'مقدار تخفیف نمی‌تواند منفی باشد' }
                          })}
                          error={!!(errors as any)?.discount_value}
                          helperText={(errors as any)?.discount_value?.message}
                          margin="normal"
                          fullWidth
                          type="number"
                          label={`مقدار تخفیف ${discountType === 'percentage' ? '(درصد)' : '(افغانی)'}`}
                          name="discount_value"
                          placeholder="0"
                        />
                      </Grid>
                      
                      <Grid item xs={12} md={2}>
                        <TextField
                          {...register('discount_start_date')}
                          margin="normal"
                          fullWidth
                          type="date"
                          label="شروع تخفیف"
                          name="discount_start_date"
                          InputLabelProps={{ shrink: true }}
                        />
                      </Grid>
                      
                      <Grid item xs={12} md={2}>
                        <TextField
                          {...register('discount_end_date')}
                          margin="normal"
                          fullWidth
                          type="date"
                          label="پایان تخفیف"
                          name="discount_end_date"
                          InputLabelProps={{ shrink: true }}
                        />
                      </Grid>
                    </>
                  )}
                </Grid>
              </Grid>
            </Grid>
          </DialogContent>
          
          <DialogActions sx={{ px: 3, pb: 3 }}>
            <Button 
              onClick={() => setOpenCreateModal(false)}
              variant="outlined"
              sx={{ borderRadius: 2 }}
            >
              انصراف
            </Button>
            <Button 
              type="submit"
              variant="contained"
              sx={{
                borderRadius: 2,
                background: 'linear-gradient(135deg, #7C3AED 0%, #A855F7 100%)',
                px: 3,
              }}
            >
              ایجاد آیتم منو
            </Button>
          </DialogActions>
        </form>
      </Dialog>
    </>
  );
}