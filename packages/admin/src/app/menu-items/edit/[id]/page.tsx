'use client';

import React from 'react';
import {
  useAutocomplete,
  SaveButton,
} from '@refinedev/mui';
import { 
  Box, 
  Card, 
  CardContent, 
  Stack, 
  Typography,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  Avatar,
  Paper,
  FormControlLabel,
  Switch,
  Autocomplete,
  Button,
  Chip,
  Alert,
} from '@mui/material';
import { useUpdate, useOne, useInvalidate, useGo } from '@refinedev/core';
import { 
  MenuBook as MenuIcon,
  Restaurant as RestaurantIcon,
  LocalOffer as DiscountIcon,
  AttachMoney as PriceIcon,
  Category as CategoryIcon,
  Image as ImageIcon,
  Visibility as AvailableIcon,
  VisibilityOff as UnavailableIcon,
  ArrowBack as BackIcon,
  Save as SaveIcon,
} from '@mui/icons-material';
import { useForm } from '@refinedev/react-hook-form';
import { Controller } from 'react-hook-form';
import { useParams } from 'next/navigation';
import ImageUpload from '../../../../components/ImageUpload';

// Force dynamic rendering to prevent SSG issues
export const dynamic = 'force-dynamic';

interface MenuItemFormData {
  restaurant_id: string;
  name: string;
  description?: string;
  price: number;
  image_url?: string;
  is_available?: boolean;
  category?: string;
  discount_type?: 'percentage' | 'fixed_amount';
  discount_value?: number;
  discount_start_date?: string;
  discount_end_date?: string;
}

export default function MenuItemEdit() {
  const params = useParams();
  const id = params?.id as string;
  const invalidate = useInvalidate();
  const go = useGo();
  const { mutate: updateMenuItem } = useUpdate();

  // Fetch menu item data
  const { data: menuItemData, isLoading, error } = useOne({
    resource: 'menu-items',
    id,
  });

  const menuItem = menuItemData?.data;

  // Form setup
  const {
    register,
    control,
    formState: { errors },
    handleSubmit,
    watch,
    setValue,
  } = useForm<MenuItemFormData>({
    defaultValues: {
      restaurant_id: menuItem?.restaurant?.id || '',
      name: menuItem?.name || '',
      description: menuItem?.description || '',
      price: menuItem?.price || 0,
      image_url: menuItem?.image_url || '',
      is_available: menuItem?.is_available ?? true,
      category: menuItem?.category || '',
      discount_type: menuItem?.discount_type || '',
      discount_value: menuItem?.discount_value || 0,
      discount_start_date: menuItem?.discount_start_date || '',
      discount_end_date: menuItem?.discount_end_date || '',
    },
  });

  // Watch discount type to conditionally show discount fields
  const discountType = watch('discount_type');

  // Autocomplete for restaurants
  const { autocompleteProps: restaurantAutocompleteProps } = useAutocomplete({
    resource: 'restaurants',
    onSearch: (value) => [
      {
        field: 'name',
        operator: 'contains',
        value,
      },
    ],
    defaultValue: menuItem?.restaurant,
  });

  // Update form values when data is loaded
  React.useEffect(() => {
    if (menuItem) {
      setValue('restaurant_id', menuItem.restaurant?.id || '');
      setValue('name', menuItem.name || '');
      setValue('description', menuItem.description || '');
      setValue('price', menuItem.price || 0);
      setValue('image_url', menuItem.image_url || '');
      setValue('is_available', menuItem.is_available ?? true);
      setValue('category', menuItem.category || '');
      setValue('discount_type', menuItem.discount_type || '');
      setValue('discount_value', menuItem.discount_value || 0);
      setValue('discount_start_date', menuItem.discount_start_date || '');
      setValue('discount_end_date', menuItem.discount_end_date || '');
    }
  }, [menuItem, setValue]);

  const handleUpdateMenuItem = (data: MenuItemFormData) => {
    try {
      console.log('Updating menu item with data:', data);
      
      // Validate required fields
      if (!data.restaurant_id || !data.name || !data.price) {
        alert('لطفاً تمام فیلدهای الزامی را پر کنید');
        return;
      }

      // Clean the data to match backend DTO
      const cleanData = {
        restaurant_id: data.restaurant_id,
        name: data.name.trim(),
        description: data.description?.trim() || undefined,
        price: Number(data.price),
        image_url: data.image_url?.trim() || undefined,
        is_available: data.is_available ?? true,
        category: data.category?.trim() || undefined,
        discount_type: data.discount_type || undefined,
        discount_value: data.discount_value ? Number(data.discount_value) : undefined,
        discount_start_date: data.discount_start_date || undefined,
        discount_end_date: data.discount_end_date || undefined,
      };

      updateMenuItem(
        {
          resource: 'menu-items',
          id,
          values: cleanData,
        },
        {
          onSuccess: (response) => {
            console.log('Menu item updated successfully:', response);
            // Invalidate the menu items list to refresh data
            invalidate({
              resource: 'menu-items',
              invalidates: ['list', 'detail'],
            });
            // Navigate back to list
            go({ to: '/menu-items/list' });
          },
          onError: (error) => {
            console.error('Error updating menu item:', error);
            alert('خطا در بروزرسانی آیتم منو: ' + error.message);
          },
        }
      );
    } catch (error) {
      console.error('Error in handleUpdateMenuItem:', error);
    }
  };

  const getCategoryIcon = (category: string) => {
    const categoryIcons: Record<string, string> = {
      'pizza': '🍕',
      'burger': '🍔',
      'pasta': '🍝',
      'salad': '🥗',
      'dessert': '🍰',
      'drink': '🥤',
      'appetizer': '🥙',
      'main': '🍽️',
      'soup': '🍲',
      'seafood': '🦐',
      'meat': '🥩',
      'vegetarian': '🥬',
      'breakfast': '🍳',
      'lunch': '🍱',
      'dinner': '🍽️',
    };
    
    const lowerCategory = category?.toLowerCase() || '';
    for (const [key, emoji] of Object.entries(categoryIcons)) {
      if (lowerCategory.includes(key)) {
        return emoji;
      }
    }
    return '🍽️'; // Default food emoji
  };

  if (isLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '400px' }}>
        <Typography>در حال بارگذاری...</Typography>
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error">
          خطا در بارگذاری اطلاعات آیتم منو
        </Alert>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* Header Section */}
      <Box sx={{ mb: 3 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <Button
              variant="outlined"
              startIcon={<BackIcon />}
              onClick={() => go({ to: '/menu-items/list' })}
              sx={{ borderRadius: 2 }}
            >
              بازگشت
            </Button>
            <Box>
              <Typography variant="h4" fontWeight={700} sx={{ mb: 0.5 }}>
                ویرایش آیتم منو
              </Typography>
              <Typography variant="body1" color="textSecondary">
                ویرایش اطلاعات آیتم منو: {menuItem?.name}
              </Typography>
            </Box>
          </Box>
        </Box>

        {/* Menu Item Info Card */}
        <Paper sx={{ p: 3, borderRadius: 3, border: '1px solid #E2E8F0', mb: 3 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <Avatar 
              sx={{ 
                bgcolor: 'primary.main',
                width: 60,
                height: 60,
                fontSize: '1.5rem',
                background: 'linear-gradient(135deg, #7C3AED 0%, #A855F7 100%)',
              }}
              src={menuItem?.image_url}
            >
              {menuItem?.image_url ? undefined : getCategoryIcon(menuItem?.category)}
            </Avatar>
            <Box sx={{ flex: 1 }}>
              <Typography variant="h6" fontWeight={600}>
                {menuItem?.name}
              </Typography>
              <Typography variant="body2" color="textSecondary" sx={{ mb: 1 }}>
                {menuItem?.description || 'بدون توضیحات'}
              </Typography>
              <Box sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
                <Chip
                  icon={<RestaurantIcon sx={{ fontSize: 16 }} />}
                  label={menuItem?.restaurant?.name || 'نامشخص'}
                  size="small"
                  variant="outlined"
                />
                <Chip
                  icon={<PriceIcon sx={{ fontSize: 16 }} />}
                  label={`؋${menuItem?.price ? Number(menuItem.price).toLocaleString() : '0'} افغانی`}
                  size="small"
                  color="success"
                />
                {menuItem?.category && (
                  <Chip
                    label={menuItem.category}
                    icon={<span style={{ fontSize: '14px' }}>{getCategoryIcon(menuItem.category)}</span>}
                    size="small"
                    variant="outlined"
                  />
                )}
                <Chip
                  icon={menuItem?.is_available ? <AvailableIcon sx={{ fontSize: 16 }} /> : <UnavailableIcon sx={{ fontSize: 16 }} />}
                  label={menuItem?.is_available ? 'موجود' : 'ناموجود'}
                  size="small"
                  color={menuItem?.is_available ? 'success' : 'error'}
                />
              </Box>
            </Box>
          </Box>
        </Paper>
      </Box>

      {/* Edit Form */}
      <Card sx={{ borderRadius: 4, border: '1px solid #E2E8F0', boxShadow: '0 1px 3px 0 rgb(0 0 0 / 0.1)' }}>
        <CardContent sx={{ p: 4 }}>
          <form onSubmit={handleSubmit(handleUpdateMenuItem as any)}>
            <Grid container spacing={4}>
              {/* Basic Information */}
              <Grid item xs={12}>
                <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 3 }}>
                  🍽️ اطلاعات پایه
                </Typography>
                <Grid container spacing={3}>
                  <Grid item xs={12} md={6}>
                    <Controller
                      control={control}
                      name="restaurant_id"
                      rules={{ required: 'انتخاب رستوران الزامی است' }}
                      render={({ field }) => (
                        <Autocomplete
                          {...restaurantAutocompleteProps}
                          {...field}
                          onChange={(_, value) => field.onChange(value?.id)}
                          getOptionLabel={(item) => item.name || ''}
                          isOptionEqualToValue={(option, value) =>
                            value === undefined || option?.id?.toString() === value?.id?.toString()
                          }
                          renderInput={(params) => (
                            <TextField
                              {...params}
                              label="رستوران"
                              margin="normal"
                              variant="outlined"
                              error={!!(errors as any)?.restaurant_id}
                              helperText={(errors as any)?.restaurant_id?.message}
                              required
                            />
                          )}
                        />
                      )}
                    />
                  </Grid>
                  
                  <Grid item xs={12} md={6}>
                    <TextField
                      {...register('name', {
                        required: 'نام غذا الزامی است',
                      })}
                      error={!!(errors as any)?.name}
                      helperText={(errors as any)?.name?.message}
                      margin="normal"
                      fullWidth
                      label="نام غذا"
                      name="name"
                      placeholder="نام غذا را وارد کنید"
                    />
                  </Grid>
                  
                  <Grid item xs={12}>
                    <TextField
                      {...register('description')}
                      margin="normal"
                      fullWidth
                      multiline
                      rows={3}
                      label="توضیحات"
                      name="description"
                      placeholder="توضیحات کوتاهی درباره غذا..."
                    />
                  </Grid>
                </Grid>
              </Grid>

              {/* Pricing & Availability */}
              <Grid item xs={12}>
                <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 3 }}>
                  💰 قیمت و موجودی
                </Typography>
                <Grid container spacing={3}>
                  <Grid item xs={12} md={4}>
                    <TextField
                      {...register('price', {
                        required: 'قیمت الزامی است',
                        min: { value: 0, message: 'قیمت نمی‌تواند منفی باشد' }
                      })}
                      error={!!(errors as any)?.price}
                      helperText={(errors as any)?.price?.message}
                      margin="normal"
                      fullWidth
                      type="number"
                      label="قیمت (افغانی)"
                      name="price"
                      placeholder="0"
                    />
                  </Grid>
                  
                  <Grid item xs={12} md={4}>
                    <TextField
                      {...register('category')}
                      margin="normal"
                      fullWidth
                      label="دسته‌بندی"
                      name="category"
                      placeholder="مثال: پیتزا، برگر، نوشیدنی"
                    />
                  </Grid>
                  
                  <Grid item xs={12} md={4}>
                    <FormControlLabel
                      control={
                        <Switch
                          {...register('is_available')}
                          defaultChecked={menuItem?.is_available ?? true}
                        />
                      }
                      label="موجود است"
                      sx={{ mt: 2 }}
                    />
                  </Grid>
                </Grid>
              </Grid>

              {/* Image */}
              <Grid item xs={12}>
                <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 3 }}>
                  🖼️ تصویر
                </Typography>
                <ImageUpload
                  value={menuItem?.image_url || ''}
                  onChange={(url: string) => setValue('image_url', url)}
                />
              </Grid>

              {/* Discount Section */}
              <Grid item xs={12}>
                <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 3 }}>
                  🏷️ تخفیف (اختیاری)
                </Typography>
                <Grid container spacing={3}>
                  <Grid item xs={12} md={4}>
                    <Controller
                      control={control}
                      name="discount_type"
                      render={({ field }) => (
                        <FormControl fullWidth margin="normal">
                          <InputLabel>نوع تخفیف</InputLabel>
                          <Select
                            {...field}
                            label="نوع تخفیف"
                          >
                            <MenuItem value="">بدون تخفیف</MenuItem>
                            <MenuItem value="percentage">درصدی</MenuItem>
                            <MenuItem value="fixed_amount">مبلغ ثابت</MenuItem>
                          </Select>
                        </FormControl>
                      )}
                    />
                  </Grid>
                  
                  {discountType && (
                    <>
                      <Grid item xs={12} md={4}>
                        <TextField
                          {...register('discount_value', {
                            min: { value: 0, message: 'مقدار تخفیف نمی‌تواند منفی باشد' }
                          })}
                          error={!!(errors as any)?.discount_value}
                          helperText={(errors as any)?.discount_value?.message}
                          margin="normal"
                          fullWidth
                          type="number"
                          label={`مقدار تخفیف ${discountType === 'percentage' ? '(درصد)' : '(افغانی)'}`}
                          name="discount_value"
                          placeholder="0"
                        />
                      </Grid>
                      
                      <Grid item xs={12} md={4}>
                        <TextField
                          {...register('discount_start_date')}
                          margin="normal"
                          fullWidth
                          type="date"
                          label="تاریخ شروع تخفیف"
                          name="discount_start_date"
                          InputLabelProps={{
                            shrink: true,
                          }}
                        />
                      </Grid>
                      
                      <Grid item xs={12} md={4}>
                        <TextField
                          {...register('discount_end_date')}
                          margin="normal"
                          fullWidth
                          type="date"
                          label="تاریخ پایان تخفیف"
                          name="discount_end_date"
                          InputLabelProps={{
                            shrink: true,
                          }}
                        />
                      </Grid>
                    </>
                  )}
                </Grid>
              </Grid>
            </Grid>

            {/* Action Buttons */}
            <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 2, mt: 4, pt: 3, borderTop: '1px solid #E2E8F0' }}>
              <Button 
                variant="outlined"
                onClick={() => go({ to: '/menu-items/list' })}
                sx={{ borderRadius: 2, px: 3 }}
              >
                انصراف
              </Button>
              <Button 
                type="submit"
                variant="contained"
                startIcon={<SaveIcon />}
                sx={{
                  borderRadius: 2,
                  px: 4,
                  background: 'linear-gradient(135deg, #7C3AED 0%, #A855F7 100%)',
                  '&:hover': {
                    background: 'linear-gradient(135deg, #6D28D9 0%, #9333EA 100%)',
                  },
                }}
              >
                ذخیره تغییرات
              </Button>
            </Box>
          </form>
        </CardContent>
      </Card>
    </Box>
  );
}
