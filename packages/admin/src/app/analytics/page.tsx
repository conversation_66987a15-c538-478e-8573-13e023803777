"use client";

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';

// Force dynamic rendering
export const dynamic = 'force-dynamic';

export default function AnalyticsPage() {
  const router = useRouter();

  useEffect(() => {
    // Redirect to the analytics dashboard
    router.replace('/analytics/dashboard');
  }, [router]);

  return (
    <div style={{ 
      display: 'flex', 
      justifyContent: 'center', 
      alignItems: 'center', 
      minHeight: '200px' 
    }}>
      Redirecting to analytics dashboard...
    </div>
  );
} 