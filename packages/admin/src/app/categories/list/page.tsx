'use client';

import React, { useState } from 'react';
import {
  useDataGrid,
} from '@refinedev/mui';
import TableActionButtons from '../../../components/TableActionButtons';
import { DataGrid, GridColDef } from '@mui/x-data-grid';
import { 
  Box, 
  Button, 
  Card, 
  CardContent, 
  Stack, 
  Typography,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Grid,
  IconButton,
  Chip,
  Avatar,
  Tooltip,
  Badge,
  Paper,
} from '@mui/material';
import { useCreate, useInvalidate } from '@refinedev/core';
import { 
  Add, 
  Close, 
  Category as CategoryIcon,
  Image as ImageIcon,
  Search as SearchIcon,
  Refresh as RefreshIcon,
  Description as DescriptionIcon,
  Folder as FolderIcon,
} from '@mui/icons-material';
import { useForm } from '@refinedev/react-hook-form';

export const dynamic = 'force-dynamic';

interface CategoryFormData {
  name: string;
  description?: string;
  image_url?: string;
}

export default function CategoryList() {
  const invalidate = useInvalidate();
  const [openCreateModal, setOpenCreateModal] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const { mutate: createCategory } = useCreate();

  const { dataGridProps } = useDataGrid({
    resource: 'categories',
    syncWithLocation: true,
    pagination: {
      mode: 'server',
    },
    filters: {
      mode: 'server',
      initial: [
        ...(searchQuery ? [{
          field: 'search',
          operator: 'contains' as any,
          value: searchQuery,
        }] : []),
      ],
    },
    sorters: {
      mode: 'server',
      initial: [
        {
          field: 'name',
          order: 'asc',
        },
      ],
    },
  });

  const {
    register,
    formState: { errors },
    handleSubmit,
    reset,
  } = useForm<CategoryFormData>();

  const handleCreateCategory = (data: CategoryFormData) => {
    try {
      if (!data.name) {
        alert('لطفاً نام دسته‌بندی را وارد کنید');
        return;
      }

      const cleanData = {
        name: data.name.trim(),
        description: data.description?.trim() || undefined,
        image_url: data.image_url?.trim() || undefined,
      };

      createCategory(
        {
          resource: 'categories',
          values: cleanData,
        },
        {
          onSuccess: () => {
            setOpenCreateModal(false);
            reset();
            invalidate({
              resource: 'categories',
              invalidates: ['list'],
            });
          },
          onError: (error) => {
            alert('خطا در ایجاد دسته‌بندی: ' + error.message);
          },
        }
      );
    } catch (error) {
      console.error('Error in handleCreateCategory:', error);
    }
  };

  const getCategoryIcon = (name: string) => {
    const categoryIcons: Record<string, string> = {
      'italian': '🍝', 'japanese': '🍣', 'american': '🍔', 'chinese': '🥢',
      'mexican': '🌮', 'indian': '🍛', 'mediterranean': '🫒', 'thai': '🌶️',
      'french': '🥐', 'korean': '🍜', 'pizza': '🍕', 'burger': '🍔',
      'pasta': '🍝', 'salad': '🥗', 'dessert': '🍰', 'drink': '🥤',
    };
    
    const lowerName = name?.toLowerCase() || '';
    for (const [key, emoji] of Object.entries(categoryIcons)) {
      if (lowerName.includes(key)) {
        return emoji;
      }
    }
    return '🏷️';
  };

  const formatDate = (dateString: string) => {
    if (!dateString) return 'نامشخص';
    try {
      const date = new Date(dateString);
      return new Intl.DateTimeFormat('fa-IR', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
      }).format(date);
    } catch (error) {
      return 'نامشخص';
    }
  };

  const totalCategories = dataGridProps?.rowCount || 0;
  const categoriesWithImages = dataGridProps?.rows?.filter((item: any) => item.image_url).length || 0;
  const categoriesWithDescriptions = dataGridProps?.rows?.filter((item: any) => item.description).length || 0;

  const columns: GridColDef[] = [
    {
      field: 'id',
      headerName: 'شناسه',
      type: 'number',
      minWidth: 80,
    },
    {
      field: 'name',
      headerName: 'نام دسته‌بندی',
      minWidth: 250,
      flex: 1,
      renderCell: ({ row }) => (
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1.5, py: 1 }}>
          <Avatar 
            sx={{ 
              bgcolor: 'primary.main',
              width: 40,
              height: 40,
              fontSize: '1.2rem',
              background: 'linear-gradient(135deg, #10B981 0%, #059669 100%)',
            }}
            src={row.image_url}
          >
            {row.image_url ? undefined : getCategoryIcon(row.name)}
          </Avatar>
          <Box>
            <Typography variant="subtitle2" fontWeight={600}>
              {row.name}
            </Typography>
            <Typography variant="caption" color="textSecondary">
              {row.description ? row.description.substring(0, 50) + '...' : 'بدون توضیحات'}
            </Typography>
          </Box>
        </Box>
      ),
    },
    {
      field: 'description',
      headerName: 'توضیحات',
      minWidth: 300,
      flex: 1,
      renderCell: ({ row }) => (
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <DescriptionIcon sx={{ fontSize: 16, color: 'text.secondary' }} />
          <Typography variant="body2" sx={{ 
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            whiteSpace: 'nowrap',
            maxWidth: 250
          }}>
            {row.description || 'بدون توضیحات'}
          </Typography>
        </Box>
      ),
    },
    {
      field: 'image_url',
      headerName: 'تصویر',
      minWidth: 120,
      renderCell: ({ row }) => (
        row.image_url ? (
          <Chip
            icon={<ImageIcon sx={{ fontSize: 16 }} />}
            label="دارد"
            color="success"
            size="small"
            sx={{ fontWeight: 500 }}
          />
        ) : (
          <Chip
            label="ندارد"
            color="default"
            size="small"
            variant="outlined"
            sx={{ fontWeight: 500 }}
          />
        )
      ),
    },
    {
      field: 'created_at',
      headerName: 'تاریخ ایجاد',
      minWidth: 150,
      renderCell: ({ row }) => (
        <Typography variant="body2" color="textSecondary">
          {formatDate(row.created_at)}
        </Typography>
      ),
    },
    {
      field: 'actions',
      headerName: 'عملیات',
      sortable: false,
      renderCell: function render({ row }) {
        return (
          <TableActionButtons 
            recordItemId={row.id} 
            resource="categories"
          />
        );
      },
      align: 'center',
      headerAlign: 'center',
      minWidth: 120, // Reduced width for icon-only buttons
    },
  ];

  return (
    <>
      <Box sx={{ mb: 3 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Box>
            <Typography variant="h4" fontWeight={700} sx={{ mb: 0.5 }}>
              مدیریت دسته‌بندی‌ها
            </Typography>
            <Typography variant="body1" color="textSecondary">
              مدیریت دسته‌بندی‌های رستوران‌ها و منوها
            </Typography>
          </Box>
          <Button
            variant="contained"
            size="large"
            startIcon={<Add />}
            onClick={() => setOpenCreateModal(true)}
            sx={{
              borderRadius: 3,
              px: 3,
              py: 1.5,
              background: 'linear-gradient(135deg, #10B981 0%, #059669 100%)',
              boxShadow: '0 4px 12px rgb(16 185 129 / 0.3)',
              '&:hover': {
                background: 'linear-gradient(135deg, #059669 0%, #047857 100%)',
                boxShadow: '0 6px 16px rgb(16 185 129 / 0.4)',
                transform: 'translateY(-2px)',
              },
              transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
            }}
          >
            افزودن دسته‌بندی جدید
          </Button>
        </Box>

        <Grid container spacing={3} sx={{ mb: 3 }}>
          <Grid item xs={12} sm={6} md={3}>
            <Card sx={{ borderRadius: 3, border: '1px solid #E2E8F0' }}>
              <CardContent sx={{ textAlign: 'center', py: 2 }}>
                <Badge badgeContent={totalCategories} color="primary" max={999}>
                  <CategoryIcon sx={{ fontSize: 40, color: 'primary.main', mb: 1 }} />
                </Badge>
                <Typography variant="h6" fontWeight={600}>
                  کل دسته‌بندی‌ها
                </Typography>
                <Typography variant="body2" color="textSecondary">
                  تمام دسته‌بندی‌های موجود
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card sx={{ borderRadius: 3, border: '1px solid #E2E8F0' }}>
              <CardContent sx={{ textAlign: 'center', py: 2 }}>
                <Badge badgeContent={categoriesWithImages} color="success" max={999}>
                  <ImageIcon sx={{ fontSize: 40, color: 'success.main', mb: 1 }} />
                </Badge>
                <Typography variant="h6" fontWeight={600}>
                  دارای تصویر
                </Typography>
                <Typography variant="body2" color="textSecondary">
                  دسته‌بندی‌های دارای تصویر
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card sx={{ borderRadius: 3, border: '1px solid #E2E8F0' }}>
              <CardContent sx={{ textAlign: 'center', py: 2 }}>
                <Badge badgeContent={categoriesWithDescriptions} color="info" max={999}>
                  <DescriptionIcon sx={{ fontSize: 40, color: 'info.main', mb: 1 }} />
                </Badge>
                <Typography variant="h6" fontWeight={600}>
                  دارای توضیحات
                </Typography>
                <Typography variant="body2" color="textSecondary">
                  دسته‌بندی‌های دارای توضیحات
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card sx={{ borderRadius: 3, border: '1px solid #E2E8F0' }}>
              <CardContent sx={{ textAlign: 'center', py: 2 }}>
                <Badge badgeContent={totalCategories - categoriesWithImages} color="warning" max={999}>
                  <FolderIcon sx={{ fontSize: 40, color: 'warning.main', mb: 1 }} />
                </Badge>
                <Typography variant="h6" fontWeight={600}>
                  بدون تصویر
                </Typography>
                <Typography variant="body2" color="textSecondary">
                  دسته‌بندی‌های بدون تصویر
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        <Paper sx={{ p: 2, borderRadius: 3, border: '1px solid #E2E8F0', mb: 3 }}>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={8}>
              <TextField
                fullWidth
                size="small"
                placeholder="جستجو در نام دسته‌بندی..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                InputProps={{
                  startAdornment: <SearchIcon sx={{ color: 'text.secondary', mr: 1 }} />,
                }}
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <Box sx={{ display: 'flex', gap: 1, justifyContent: 'flex-end' }}>
                <Tooltip title="بروزرسانی">
                  <IconButton 
                    onClick={() => invalidate({ resource: 'categories', invalidates: ['list'] })}
                    sx={{ borderRadius: 2 }}
                  >
                    <RefreshIcon />
                  </IconButton>
                </Tooltip>
              </Box>
            </Grid>
          </Grid>
        </Paper>
      </Box>

      <Card 
        sx={{ 
          borderRadius: 4,
          border: '1px solid #E2E8F0',
          boxShadow: '0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)',
          overflow: 'hidden',
        }}
      >
        <CardContent sx={{ p: 0 }}>
          <DataGrid
            {...dataGridProps}
            columns={columns}
            autoHeight
            pageSizeOptions={[10, 25, 50, 100]}
            density="comfortable"
            sx={{
              border: 'none',
              '& .MuiDataGrid-cell': {
                borderBottom: '1px solid #F1F5F9',
                py: 2,
              },
              '& .MuiDataGrid-columnHeaders': {
                backgroundColor: '#F8FAFC',
                borderBottom: '1px solid #E2E8F0',
                '& .MuiDataGrid-columnHeader': {
                  fontWeight: 600,
                  fontSize: '0.875rem',
                },
              },
              '& .MuiDataGrid-row': {
                '&:hover': {
                  backgroundColor: '#F8FAFC',
                },
                '&.Mui-selected': {
                  backgroundColor: '#ECFDF5',
                  '&:hover': {
                    backgroundColor: '#D1FAE5',
                  },
                },
              },
              '& .MuiDataGrid-footerContainer': {
                borderTop: '1px solid #E2E8F0',
                backgroundColor: '#F8FAFC',
              },
            }}
          />
        </CardContent>
      </Card>

      <Dialog 
        open={openCreateModal} 
        onClose={() => setOpenCreateModal(false)}
        maxWidth="md"
        fullWidth
        PaperProps={{
          sx: {
            borderRadius: 4,
            boxShadow: '0 25px 50px -12px rgb(0 0 0 / 0.25)',
          },
        }}
      >
        <DialogTitle>
          <Box display="flex" justifyContent="space-between" alignItems="center">
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <Avatar
                sx={{
                  bgcolor: 'primary.main',
                  background: 'linear-gradient(135deg, #10B981 0%, #059669 100%)',
                }}
              >
                <CategoryIcon />
              </Avatar>
              <Box>
                <Typography variant="h6" fontWeight={600}>
                  افزودن دسته‌بندی جدید
                </Typography>
                <Typography variant="body2" color="textSecondary">
                  اطلاعات دسته‌بندی را وارد کنید
                </Typography>
              </Box>
            </Box>
            <IconButton 
              onClick={() => setOpenCreateModal(false)}
              sx={{ 
                borderRadius: 2,
                '&:hover': { bgcolor: 'action.hover' }
              }}
            >
              <Close />
            </IconButton>
          </Box>
        </DialogTitle>
        
        <form onSubmit={handleSubmit(handleCreateCategory as any)}>
          <DialogContent sx={{ px: 3 }}>
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                  🏷️ اطلاعات پایه
                </Typography>
                <Grid container spacing={2}>
                  <Grid item xs={12}>
                    <TextField
                      {...register('name', {
                        required: 'نام دسته‌بندی الزامی است',
                      })}
                      error={!!(errors as any)?.name}
                      helperText={(errors as any)?.name?.message}
                      margin="normal"
                      fullWidth
                      label="نام دسته‌بندی"
                      name="name"
                      placeholder="نام دسته‌بندی را وارد کنید"
                    />
                  </Grid>
                  
                  <Grid item xs={12}>
                    <TextField
                      {...register('description')}
                      margin="normal"
                      fullWidth
                      multiline
                      rows={3}
                      label="توضیحات"
                      name="description"
                      placeholder="توضیحات کوتاهی درباره دسته‌بندی..."
                    />
                  </Grid>
                </Grid>
              </Grid>

              <Grid item xs={12}>
                <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                  🖼️ تصویر (اختیاری)
                </Typography>
                <TextField
                  {...register('image_url')}
                  margin="normal"
                  fullWidth
                  label="لینک تصویر"
                  name="image_url"
                  placeholder="https://example.com/image.jpg"
                />
              </Grid>
            </Grid>
          </DialogContent>
          
          <DialogActions sx={{ px: 3, pb: 3 }}>
            <Button 
              onClick={() => setOpenCreateModal(false)}
              variant="outlined"
              sx={{ borderRadius: 2 }}
            >
              انصراف
            </Button>
            <Button 
              type="submit"
              variant="contained"
              sx={{
                borderRadius: 2,
                background: 'linear-gradient(135deg, #10B981 0%, #059669 100%)',
                px: 3,
              }}
            >
              ایجاد دسته‌بندی
            </Button>
          </DialogActions>
        </form>
      </Dialog>
    </>
  );
}
