'use client';

import React from 'react';
import { 
  Box, 
  Card, 
  CardContent, 
  Typography,
  TextField,
  Grid,
  Avatar,
  Paper,
  Button,
  Alert,
} from '@mui/material';
import { useUpdate, useOne, useInvalidate, useGo } from '@refinedev/core';
import { 
  Category as CategoryIcon,
  ArrowBack as BackIcon,
  Save as SaveIcon,
} from '@mui/icons-material';
import { useForm } from '@refinedev/react-hook-form';
import { useParams } from 'next/navigation';

// Force dynamic rendering to prevent SSG issues
export const dynamic = 'force-dynamic';

interface CategoryFormData {
  name: string;
  description?: string;
  image_url?: string;
}

export default function CategoryEdit() {
  const params = useParams();
  const id = params?.id as string;
  const invalidate = useInvalidate();
  const go = useGo();
  const { mutate: updateCategory } = useUpdate();

  // Fetch category data
  const { data: categoryData, isLoading, error } = useOne({
    resource: 'categories',
    id,
  });

  const category = categoryData?.data;

  // Form setup
  const {
    register,
    formState: { errors },
    handleSubmit,
    setValue,
  } = useForm<CategoryFormData>({
    defaultValues: {
      name: category?.name || '',
      description: category?.description || '',
      image_url: category?.image_url || '',
    },
    mode: 'onChange',
  });

  // Update form values when data is loaded
  React.useEffect(() => {
    if (category) {
      setValue('name', category.name || '');
      setValue('description', category.description || '');
      setValue('image_url', category.image_url || '');
    }
  }, [category, setValue]);

  const handleUpdateCategory = (data: CategoryFormData, event?: React.BaseSyntheticEvent) => {
    try {
      if (!data.name) {
        alert('لطفاً نام دسته‌بندی را وارد کنید');
        return;
      }

      const cleanData = {
        name: data.name.trim(),
        description: data.description?.trim() || undefined,
        image_url: data.image_url?.trim() || undefined,
      };

      updateCategory(
        {
          resource: 'categories',
          id,
          values: cleanData,
        },
        {
          onSuccess: () => {
            invalidate({
              resource: 'categories',
              invalidates: ['list', 'detail'],
            });
            go({ to: '/categories/list' });
          },
          onError: (error) => {
            alert('خطا در بروزرسانی دسته‌بندی: ' + error.message);
          },
        }
      );
    } catch (error) {
      console.error('Error in handleUpdateCategory:', error);
    }
  };

  const getCategoryIcon = (name: string) => {
    const categoryIcons: Record<string, string> = {
      'italian': '🍝', 'japanese': '🍣', 'american': '🍔', 'chinese': '🥢',
      'mexican': '🌮', 'indian': '🍛', 'mediterranean': '🫒', 'thai': '🌶️',
      'french': '🥐', 'korean': '🍜', 'pizza': '🍕', 'burger': '🍔',
      'pasta': '🍝', 'salad': '🥗', 'dessert': '🍰', 'drink': '🥤',
    };
    
    const lowerName = name?.toLowerCase() || '';
    for (const [key, emoji] of Object.entries(categoryIcons)) {
      if (lowerName.includes(key)) {
        return emoji;
      }
    }
    return '🏷️';
  };

  if (isLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '400px' }}>
        <Typography>در حال بارگذاری...</Typography>
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error">
          خطا در بارگذاری اطلاعات دسته‌بندی
        </Alert>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* Header Section */}
      <Box sx={{ mb: 3 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <Button
              variant="outlined"
              startIcon={<BackIcon />}
              onClick={() => go({ to: '/categories/list' })}
              sx={{ borderRadius: 2 }}
            >
              بازگشت
            </Button>
            <Box>
              <Typography variant="h4" fontWeight={700} sx={{ mb: 0.5 }}>
                ویرایش دسته‌بندی
              </Typography>
              <Typography variant="body1" color="textSecondary">
                ویرایش اطلاعات دسته‌بندی: {category?.name}
              </Typography>
            </Box>
          </Box>
        </Box>

        {/* Category Info Card */}
        <Paper sx={{ p: 3, borderRadius: 3, border: '1px solid #E2E8F0', mb: 3 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <Avatar 
              sx={{ 
                bgcolor: 'primary.main',
                width: 60,
                height: 60,
                fontSize: '1.5rem',
                background: 'linear-gradient(135deg, #10B981 0%, #059669 100%)',
              }}
              src={category?.image_url}
            >
              {category?.image_url ? undefined : getCategoryIcon(category?.name)}
            </Avatar>
            <Box sx={{ flex: 1 }}>
              <Typography variant="h6" fontWeight={600}>
                {category?.name}
              </Typography>
              <Typography variant="body2" color="textSecondary" sx={{ mb: 1 }}>
                {category?.description || 'بدون توضیحات'}
              </Typography>
            </Box>
          </Box>
        </Paper>
      </Box>

      {/* Edit Form */}
      <Card sx={{ borderRadius: 4, border: '1px solid #E2E8F0', boxShadow: '0 1px 3px 0 rgb(0 0 0 / 0.1)' }}>
        <CardContent sx={{ p: 4 }}>
          <form onSubmit={handleSubmit(handleUpdateCategory as any)}>
            <Grid container spacing={4}>
              {/* Basic Information */}
              <Grid item xs={12}>
                <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 3 }}>
                  🏷️ اطلاعات پایه
                </Typography>
                <Grid container spacing={3}>
                  <Grid item xs={12}>
                    <TextField
                      {...register('name', {
                        required: 'نام دسته‌بندی الزامی است',
                      })}
                      error={!!(errors as any)?.name}
                      helperText={(errors as any)?.name?.message}
                      margin="normal"
                      fullWidth
                      label="نام دسته‌بندی"
                      name="name"
                      placeholder="نام دسته‌بندی را وارد کنید"
                    />
                  </Grid>
                  
                  <Grid item xs={12}>
                    <TextField
                      {...register('description')}
                      margin="normal"
                      fullWidth
                      multiline
                      rows={4}
                      label="توضیحات"
                      name="description"
                      placeholder="توضیحات کوتاهی درباره دسته‌بندی..."
                    />
                  </Grid>
                </Grid>
              </Grid>

              {/* Image */}
              <Grid item xs={12}>
                <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 3 }}>
                  🖼️ تصویر (اختیاری)
                </Typography>
                <TextField
                  {...register('image_url')}
                  margin="normal"
                  fullWidth
                  label="لینک تصویر"
                  name="image_url"
                  placeholder="https://example.com/image.jpg"
                />
              </Grid>
            </Grid>

            {/* Action Buttons */}
            <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 2, mt: 4, pt: 3, borderTop: '1px solid #E2E8F0' }}>
              <Button 
                variant="outlined"
                onClick={() => go({ to: '/categories/list' })}
                sx={{ borderRadius: 2, px: 3 }}
              >
                انصراف
              </Button>
              <Button 
                type="submit"
                variant="contained"
                startIcon={<SaveIcon />}
                sx={{
                  borderRadius: 2,
                  px: 4,
                  background: 'linear-gradient(135deg, #10B981 0%, #059669 100%)',
                  '&:hover': {
                    background: 'linear-gradient(135deg, #059669 0%, #047857 100%)',
                  },
                }}
              >
                ذخیره تغییرات
              </Button>
            </Box>
          </form>
        </CardContent>
      </Card>
    </Box>
  );
}