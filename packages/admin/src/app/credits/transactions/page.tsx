'use client';

import React from 'react';
import { useList } from '@refinedev/core';
import { Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper } from '@mui/material';

interface CreditTransaction {
  id: string;
  user: { id: string; name: string };
  amount: number;
  type: string;
  created_at: string;
}

export default function CreditTransactionsListPage() {
  // This should be replaced with the correct resource and fields from backend
  const { data, isLoading } = useList<CreditTransaction>({
    resource: 'credit-transactions',
    // params: { ... } // Add params if needed
  });

  return (
    <TableContainer component={Paper}>
      <Table>
        <TableHead>
          <TableRow>
            <TableCell>Transaction ID</TableCell>
            <TableCell>User ID</TableCell>
            <TableCell>User Name</TableCell>
            <TableCell>Amount</TableCell>
            <TableCell>Type</TableCell>
            <TableCell>Date</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {data?.data?.map((tx) => (
            <TableRow key={tx.id}>
              <TableCell>{tx.id}</TableCell>
              <TableCell>{tx.user?.id}</TableCell>
              <TableCell>{tx.user?.name}</TableCell>
              <TableCell>{tx.amount}</TableCell>
              <TableCell>{tx.type}</TableCell>
              <TableCell>{new Date(tx.created_at).toLocaleString()}</TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </TableContainer>
  );
} 