'use client';

import React from 'react';
import { useDataGrid } from '@refinedev/mui';
import { useGo } from '@refinedev/core';
import { DataGrid, GridColDef } from '@mui/x-data-grid';
import { Stack, TextField, Button } from '@mui/material';

export default function CreditsListPage() {
  const { dataGridProps, filters, setFilters } = useDataGrid({
    resource: 'credits',
    pagination: { mode: 'server' },
    filters: { mode: 'server' },
    sorters: { mode: 'server' },
  });
  const go = useGo();

  const columns: GridColDef[] = [
    { field: 'user.id', headerName: 'User ID', width: 200, valueGetter: (params) => params.row.user?.id },
    { field: 'user.name', headerName: 'User Name', width: 200, valueGetter: (params) => params.row.user?.name },
    { field: 'balance', headerName: 'Balance', width: 120 },
    {
      field: 'actions',
      headerName: 'Actions',
      width: 120,
      renderCell: (params) => (
        <Button size="small" onClick={() => go({ to: `/credits/show/${params.row.id}` })}>
          Show
        </Button>
      ),
      sortable: false,
      filterable: false,
    },
  ];

  // Search handler
  const handleSearch = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setFilters([{ field: 'search', operator: 'contains', value: search }]);
  };

  return (
    <Stack spacing={2}>
      <form onSubmit={handleSearch} style={{ display: 'flex', gap: 8 }}>
        <TextField
          size="small"
          placeholder="Search by user name or id"
          value={search || ''}
          onChange={e => setSearch(e.target.value)}
        />
        <Button type="submit" variant="contained">Search</Button>
      </form>
      <DataGrid
        {...dataGridProps}
        columns={columns}
        autoHeight
        pageSizeOptions={[10, 20, 50]}
      />
    </Stack>
  );
}