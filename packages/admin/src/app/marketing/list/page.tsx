'use client';

import React from 'react';
import { useList, useDelete } from '@refinedev/core';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  Chip,
  IconButton,
  Stack,
  TextField,
  InputAdornment,
  Alert,
} from '@mui/material';
import {
  Add as AddIcon,
  Search as SearchIcon,
  Visibility as VisibilityIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Campaign as CampaignIcon,
  TrendingUp as TrendingUpIcon,
  Email as EmailIcon,
  Sms as SmsIcon,
} from '@mui/icons-material';
import { DataGrid, GridColDef, GridActionsCellItem } from '@mui/x-data-grid';
import { useRouter } from 'next/navigation';
import { useState } from 'react';

export const dynamic = 'force-dynamic';

interface Campaign {
  id: number;
  name: string;
  type: 'email' | 'sms' | 'push' | 'banner';
  status: 'draft' | 'active' | 'paused' | 'completed';
  target_audience: string;
  start_date: string;
  end_date: string;
  budget: number;
  impressions: number;
  clicks: number;
  conversions: number;
  created_at: string;
  updated_at: string;
}

export default function MarketingListPage() {
  const router = useRouter();
  const [searchTerm, setSearchTerm] = useState('');
  const { mutate: deleteCampaign } = useDelete();

  const { data, isLoading, error } = useList<Campaign>({
    resource: 'marketing/campaigns',
    pagination: {
      pageSize: 25,
    },
    filters: searchTerm
      ? [
          {
            field: 'name',
            operator: 'contains',
            value: searchTerm,
          },
        ]
      : [],
  });

  const handleDelete = (id: number) => {
    if (window.confirm('Are you sure you want to delete this campaign?')) {
      deleteCampaign({
        resource: 'marketing/campaigns',
        id,
      });
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'success';
      case 'paused':
        return 'warning';
      case 'completed':
        return 'info';
      case 'draft':
        return 'default';
      default:
        return 'default';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'email':
        return <EmailIcon fontSize="small" />;
      case 'sms':
        return <SmsIcon fontSize="small" />;
      case 'push':
        return <CampaignIcon fontSize="small" />;
      case 'banner':
        return <TrendingUpIcon fontSize="small" />;
      default:
        return <CampaignIcon fontSize="small" />;
    }
  };

  const columns: GridColDef[] = [
    {
      field: 'id',
      headerName: 'ID',
      width: 80,
    },
    {
      field: 'name',
      headerName: 'Campaign Name',
      width: 200,
      renderCell: (params) => (
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          {getTypeIcon(params.row.type)}
          <Typography variant="body2" fontWeight="medium">
            {params.value}
          </Typography>
        </Box>
      ),
    },
    {
      field: 'type',
      headerName: 'Type',
      width: 100,
      renderCell: (params) => (
        <Chip
          label={params.value.toUpperCase()}
          size="small"
          variant="outlined"
        />
      ),
    },
    {
      field: 'status',
      headerName: 'Status',
      width: 120,
      renderCell: (params) => (
        <Chip
          label={params.value.charAt(0).toUpperCase() + params.value.slice(1)}
          color={getStatusColor(params.value) as any}
          size="small"
        />
      ),
    },
    {
      field: 'target_audience',
      headerName: 'Target Audience',
      width: 150,
    },
    {
      field: 'budget',
      headerName: 'Budget',
      width: 100,
      renderCell: (params) => (
        <Typography variant="body2">
          ${params.value?.toLocaleString() || '0'}
        </Typography>
      ),
    },
    {
      field: 'impressions',
      headerName: 'Impressions',
      width: 120,
      renderCell: (params) => (
        <Typography variant="body2">
          {params.value?.toLocaleString() || '0'}
        </Typography>
      ),
    },
    {
      field: 'clicks',
      headerName: 'Clicks',
      width: 100,
      renderCell: (params) => (
        <Typography variant="body2">
          {params.value?.toLocaleString() || '0'}
        </Typography>
      ),
    },
    {
      field: 'conversions',
      headerName: 'Conversions',
      width: 120,
      renderCell: (params) => (
        <Typography variant="body2">
          {params.value?.toLocaleString() || '0'}
        </Typography>
      ),
    },
    {
      field: 'start_date',
      headerName: 'Start Date',
      width: 120,
      renderCell: (params) => (
        <Typography variant="body2">
          {params.value ? new Date(params.value).toLocaleDateString() : 'N/A'}
        </Typography>
      ),
    },
    {
      field: 'end_date',
      headerName: 'End Date',
      width: 120,
      renderCell: (params) => (
        <Typography variant="body2">
          {params.value ? new Date(params.value).toLocaleDateString() : 'N/A'}
        </Typography>
      ),
    },
    {
      field: 'actions',
      type: 'actions',
      headerName: 'Actions',
      width: 120,
      getActions: (params) => [
        <GridActionsCellItem
          key="view"
          icon={<VisibilityIcon />}
          label="View"
          onClick={() => router.push(`/marketing/show/${params.id}`)}
        />,
        <GridActionsCellItem
          key="edit"
          icon={<EditIcon />}
          label="Edit"
          onClick={() => router.push(`/marketing/edit/${params.id}`)}
        />,
        <GridActionsCellItem
          key="delete"
          icon={<DeleteIcon />}
          label="Delete"
          onClick={() => handleDelete(params.id as number)}
        />,
      ],
    },
  ];

  if (error) {
    return (
      <Alert severity="error">
        Error loading campaigns: {error.message}
      </Alert>
    );
  }

  return (
    <Stack spacing={3}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Typography variant="h4" component="h1" fontWeight="bold">
          Marketing Campaigns
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => router.push('/marketing/create')}
        >
          Create Campaign
        </Button>
      </Box>

      <Card>
        <CardContent>
          <Box sx={{ mb: 2 }}>
            <TextField
              placeholder="Search campaigns..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                ),
              }}
              sx={{ width: 300 }}
            />
          </Box>

          <DataGrid
            rows={data?.data || []}
            columns={columns}
            loading={isLoading}
            pageSizeOptions={[10, 25, 50]}
            initialState={{
              pagination: {
                paginationModel: { pageSize: 25 },
              },
            }}
            disableRowSelectionOnClick
            sx={{
              height: 600,
              '& .MuiDataGrid-cell': {
                borderBottom: '1px solid #f0f0f0',
              },
            }}
          />
        </CardContent>
      </Card>
    </Stack>
  );
}