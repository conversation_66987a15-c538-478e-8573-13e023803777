'use client'

import { useEffect, useState } from 'react'

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || "http://localhost:7000/api"

// Helper function to get auth headers
const getAuthHeaders = () => {
  const token = localStorage.getItem('access_token')
  
  const headers: Record<string, string> = {
    'Content-Type': 'application/json',
  }
  
  if (token) {
    headers['Authorization'] = `Bearer ${token}`
  }
  
  return headers
}
import { 
  Card,
  CardContent,
  Typography,
  Button,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Grid,
  Box,
  Badge,
  IconButton,
  Tabs,
  Tab
} from '@mui/material'
import {
  Visibility as ViewIcon,
  CheckCircle as ApproveIcon,
  Cancel as RejectIcon,
  Phone as PhoneIcon,
  Email as EmailIcon,
  LocationOn as LocationIcon,
  Business as BusinessIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material'

interface RegistrationRequest {
  id: string
  managerName: string
  managerPhone: string
  managerEmail: string
  nationalIdNumber: string
  restaurantName: string
  restaurantPhone: string
  description: string
  address: string
  city: string
  province: string
  status: 'pending' | 'under_review' | 'approved' | 'rejected'
  submittedAt: string
  nationalIdFrontPath?: string
  nationalIdBackPath?: string
  businessLicensePath?: string
  reviewedBy?: {
    name: string
    email: string
  }
  reviewedAt?: string
  rejectionReason?: string
  adminNotes?: string
}

interface RegistrationStats {
  total: number
  pending: number
  underReview: number
  approved: number
  rejected: number
}

export default function RestaurantRegistrationsList() {
  const [requests, setRequests] = useState<RegistrationRequest[]>([])
  const [stats, setStats] = useState<RegistrationStats>({
    total: 0,
    pending: 0,
    underReview: 0,
    approved: 0,
    rejected: 0
  })
  const [loading, setLoading] = useState(false)
  const [selectedRequest, setSelectedRequest] = useState<RegistrationRequest | null>(null)
  const [viewDialogOpen, setViewDialogOpen] = useState(false)
  const [reviewDialogOpen, setReviewDialogOpen] = useState(false)
  const [reviewAction, setReviewAction] = useState<'approve' | 'reject' | null>(null)
  const [reviewNotes, setReviewNotes] = useState('')
  const [rejectionReason, setRejectionReason] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('all')

  const statusColors = {
    pending: 'warning',
    under_review: 'info',
    approved: 'success',
    rejected: 'error'
  } as const

  const statusLabels = {
    pending: 'در انتظار بررسی',
    under_review: 'در حال بررسی',
    approved: 'تأیید شده',
    rejected: 'رد شده'
  }

  useEffect(() => {
    fetchRequests()
    fetchStats()
  }, [statusFilter])

  const fetchRequests = async () => {
    setLoading(true)
    try {
      const response = await fetch(`${API_BASE_URL}/restaurant-registration/list`, {
        method: 'GET',
        headers: getAuthHeaders(),
      })

      if (!response.ok) {
        throw new Error('Failed to fetch registration requests')
      }

      const data = await response.json()
      
      const filteredData = statusFilter === 'all' 
        ? data 
        : data.filter((req: RegistrationRequest) => req.status === statusFilter)
        
      setRequests(filteredData)
    } catch (error) {
      console.error('Error fetching requests:', error)
      // Show empty array on error
      setRequests([])
    } finally {
      setLoading(false)
    }
  }

  const fetchStats = async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/restaurant-registration/stats`, {
        method: 'GET',
        headers: getAuthHeaders(),
      })

      if (!response.ok) {
        throw new Error('Failed to fetch registration stats')
      }

      const statsData = await response.json()
      setStats(statsData)
    } catch (error) {
      console.error('Error fetching stats:', error)
      // Set default empty stats on error
      setStats({
        total: 0,
        pending: 0,
        underReview: 0,
        approved: 0,
        rejected: 0
      })
    }
  }

  const handleViewRequest = (request: RegistrationRequest) => {
    setSelectedRequest(request)
    setViewDialogOpen(true)
  }

  const handleReviewRequest = (request: RegistrationRequest, action: 'approve' | 'reject') => {
    setSelectedRequest(request)
    setReviewAction(action)
    setReviewDialogOpen(true)
    setReviewNotes('')
    setRejectionReason('')
  }

  const submitReview = async () => {
    if (!selectedRequest || !reviewAction) return

    try {
      const endpoint = reviewAction === 'approve' 
        ? `${API_BASE_URL}/restaurant-registration/${selectedRequest.id}/approve`
        : `${API_BASE_URL}/restaurant-registration/${selectedRequest.id}/reject`

      const response = await fetch(endpoint, {
        method: 'PATCH',
        headers: getAuthHeaders(),
        body: JSON.stringify({
          adminNotes: reviewNotes,
          rejectionReason: reviewAction === 'reject' ? rejectionReason : undefined
        })
      })

      if (!response.ok) {
        throw new Error(`Failed to ${reviewAction} registration request`)
      }

      // Refresh data from server
      await fetchRequests()
      await fetchStats()
      
      setReviewDialogOpen(false)
      setReviewNotes('')
      setRejectionReason('')
    } catch (error) {
      console.error('Error submitting review:', error)
      alert(`خطا در ${reviewAction === 'approve' ? 'تأیید' : 'رد'} درخواست. لطفاً دوباره تلاش کنید.`)
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('fa-IR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" component="h1">
          درخواست‌های ثبت رستوران
        </Typography>
        <Button
          variant="outlined"
          startIcon={<RefreshIcon />}
          onClick={() => {
            fetchRequests()
            fetchStats()
          }}
          disabled={loading}
        >
          بروزرسانی
        </Button>
      </Box>

      {/* Stats Cards */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={2.4}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom variant="body2">
                کل درخواست‌ها
              </Typography>
              <Typography variant="h4">
                {stats.total}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={2.4}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom variant="body2">
                در انتظار بررسی
              </Typography>
              <Typography variant="h4" color="warning.main">
                {stats.pending}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={2.4}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom variant="body2">
                در حال بررسی
              </Typography>
              <Typography variant="h4" color="info.main">
                {stats.underReview}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={2.4}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom variant="body2">
                تأیید شده
              </Typography>
              <Typography variant="h4" color="success.main">
                {stats.approved}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={2.4}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom variant="body2">
                رد شده
              </Typography>
              <Typography variant="h4" color="error.main">
                {stats.rejected}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Filter Tabs */}
      <Tabs 
        value={statusFilter} 
        onChange={(_, newValue) => setStatusFilter(newValue)}
        sx={{ mb: 3 }}
      >
        <Tab label="همه" value="all" />
        <Tab 
          label={
            <Badge badgeContent={stats.pending} color="warning">
              در انتظار بررسی
            </Badge>
          } 
          value="pending" 
        />
        <Tab 
          label={
            <Badge badgeContent={stats.underReview} color="info">
              در حال بررسی
            </Badge>
          } 
          value="under_review" 
        />
        <Tab label="تأیید شده" value="approved" />
        <Tab label="رد شده" value="rejected" />
      </Tabs>

      {/* Requests List */}
      <Grid container spacing={3}>
        {requests.map((request) => (
          <Grid item xs={12} key={request.id}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                  <Box sx={{ flex: 1 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                      <Typography variant="h6" component="h3">
                        {request.restaurantName}
                      </Typography>
                      <Chip 
                        label={statusLabels[request.status]}
                        color={statusColors[request.status]}
                        size="small"
                      />
                    </Box>
                    
                    <Grid container spacing={2}>
                      <Grid item xs={12} md={6}>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                          <BusinessIcon fontSize="small" color="action" />
                          <Typography variant="body2">
                            مدیر: {request.managerName}
                          </Typography>
                        </Box>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                          <PhoneIcon fontSize="small" color="action" />
                          <Typography variant="body2">
                            {request.managerPhone}
                          </Typography>
                        </Box>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                          <EmailIcon fontSize="small" color="action" />
                          <Typography variant="body2">
                            {request.managerEmail}
                          </Typography>
                        </Box>
                      </Grid>
                      <Grid item xs={12} md={6}>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                          <LocationIcon fontSize="small" color="action" />
                          <Typography variant="body2">
                            {request.city}, {request.province}
                          </Typography>
                        </Box>
                        <Typography variant="body2" color="textSecondary">
                          تاریخ ثبت: {formatDate(request.submittedAt)}
                        </Typography>
                      </Grid>
                    </Grid>
                  </Box>
                  
                  <Box sx={{ display: 'flex', gap: 1 }}>
                    <IconButton
                      color="primary"
                      onClick={() => handleViewRequest(request)}
                      title="مشاهده جزئیات"
                    >
                      <ViewIcon />
                    </IconButton>
                    
                    {request.status === 'pending' && (
                      <>
                        <IconButton
                          color="success"
                          onClick={() => handleReviewRequest(request, 'approve')}
                          title="تأیید"
                        >
                          <ApproveIcon />
                        </IconButton>
                        <IconButton
                          color="error"
                          onClick={() => handleReviewRequest(request, 'reject')}
                          title="رد"
                        >
                          <RejectIcon />
                        </IconButton>
                      </>
                    )}
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* View Details Dialog */}
      <Dialog open={viewDialogOpen} onClose={() => setViewDialogOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle>جزئیات درخواست ثبت رستوران</DialogTitle>
        <DialogContent>
          {selectedRequest && (
            <Box sx={{ pt: 2 }}>
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <Typography variant="h6" gutterBottom>اطلاعات مدیر</Typography>
                  <Typography variant="body2"><strong>نام:</strong> {selectedRequest.managerName}</Typography>
                  <Typography variant="body2"><strong>تلفن:</strong> {selectedRequest.managerPhone}</Typography>
                  <Typography variant="body2"><strong>ایمیل:</strong> {selectedRequest.managerEmail}</Typography>
                  <Typography variant="body2"><strong>نمبر تذکره:</strong> {selectedRequest.nationalIdNumber}</Typography>
                </Grid>
                <Grid item xs={12} md={6}>
                  <Typography variant="h6" gutterBottom>اطلاعات رستوران</Typography>
                  <Typography variant="body2"><strong>نام رستوران:</strong> {selectedRequest.restaurantName}</Typography>
                  <Typography variant="body2"><strong>تلفن رستوران:</strong> {selectedRequest.restaurantPhone}</Typography>
                  <Typography variant="body2"><strong>آدرس:</strong> {selectedRequest.address}</Typography>
                </Grid>
                <Grid item xs={12}>
                  <Typography variant="h6" gutterBottom>توضیحات</Typography>
                  <Typography variant="body2">{selectedRequest.description}</Typography>
                </Grid>
                {selectedRequest.nationalIdFrontPath && (
                  <Grid item xs={12}>
                    <Typography variant="h6" gutterBottom>مدارک</Typography>
                    <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
                      {selectedRequest.nationalIdFrontPath && (
                        <Button variant="outlined" size="small">
                          مشاهده روی تذکره
                        </Button>
                      )}
                      {selectedRequest.nationalIdBackPath && (
                        <Button variant="outlined" size="small">
                          مشاهده پشت تذکره
                        </Button>
                      )}
                      {selectedRequest.businessLicensePath && (
                        <Button variant="outlined" size="small">
                          مشاهده جواز کسب‌وکار
                        </Button>
                      )}
                    </Box>
                  </Grid>
                )}
              </Grid>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setViewDialogOpen(false)}>بستن</Button>
        </DialogActions>
      </Dialog>

      {/* Review Dialog */}
      <Dialog open={reviewDialogOpen} onClose={() => setReviewDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>
          {reviewAction === 'approve' ? 'تأیید درخواست' : 'رد درخواست'}
        </DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 2 }}>
            {reviewAction === 'reject' && (
              <TextField
                fullWidth
                label="دلیل رد درخواست"
                multiline
                rows={3}
                value={rejectionReason}
                onChange={(e) => setRejectionReason(e.target.value)}
                required
                sx={{ mb: 2 }}
              />
            )}
            <TextField
              fullWidth
              label="یادداشت ادمین (اختیاری)"
              multiline
              rows={3}
              value={reviewNotes}
              onChange={(e) => setReviewNotes(e.target.value)}
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setReviewDialogOpen(false)}>لغو</Button>
          <Button 
            onClick={submitReview}
            color={reviewAction === 'approve' ? 'success' : 'error'}
            variant="contained"
            disabled={reviewAction === 'reject' && !rejectionReason.trim()}
          >
            {reviewAction === 'approve' ? 'تأیید' : 'رد درخواست'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  )
} 