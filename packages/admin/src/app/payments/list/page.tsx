'use client';

import React from 'react';
import { useList, useDelete } from '@refinedev/core';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  Chip,
  IconButton,
  Stack,
  TextField,
  InputAdornment,
  Alert,
  Avatar,
} from '@mui/material';
import {
  Add as AddIcon,
  Search as SearchIcon,
  Visibility as VisibilityIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Payment as PaymentIcon,
  CreditCard as CreditCardIcon,
  AccountBalanceWallet as WalletIcon,
  MonetizationOn as MoneyIcon,
} from '@mui/icons-material';
import { DataGrid, GridColDef, GridActionsCellItem } from '@mui/x-data-grid';
import { useRouter } from 'next/navigation';
import { useState } from 'react';

export const dynamic = 'force-dynamic';

interface Payment {
  id: number;
  transaction_id: string;
  user_id: number;
  order_id: number;
  amount: number;
  currency: string;
  payment_method: 'credit_card' | 'debit_card' | 'paypal' | 'stripe' | 'cash' | 'bank_transfer';
  status: 'pending' | 'completed' | 'failed' | 'refunded' | 'cancelled';
  gateway: string;
  gateway_transaction_id: string;
  user: {
    name: string;
    email: string;
  };
  order: {
    id: number;
    total_amount: number;
  };
  created_at: string;
  updated_at: string;
}

export default function PaymentsListPage() {
  const router = useRouter();
  const [searchTerm, setSearchTerm] = useState('');
  const { mutate: deletePayment } = useDelete();

  const { data, isLoading, error } = useList<Payment>({
    resource: 'payments',
    pagination: {
      pageSize: 25,
    },
    filters: searchTerm
      ? [
          {
            field: 'transaction_id',
            operator: 'contains',
            value: searchTerm,
          },
        ]
      : [],
  });

  const handleDelete = (id: number) => {
    if (window.confirm('Are you sure you want to delete this payment record?')) {
      deletePayment({
        resource: 'payments',
        id,
      });
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'success';
      case 'pending':
        return 'warning';
      case 'failed':
        return 'error';
      case 'refunded':
        return 'info';
      case 'cancelled':
        return 'default';
      default:
        return 'default';
    }
  };

  const getPaymentMethodIcon = (method: string) => {
    switch (method) {
      case 'credit_card':
      case 'debit_card':
        return <CreditCardIcon fontSize="small" />;
      case 'paypal':
      case 'stripe':
        return <WalletIcon fontSize="small" />;
      case 'cash':
        return <MoneyIcon fontSize="small" />;
      case 'bank_transfer':
        return <PaymentIcon fontSize="small" />;
      default:
        return <PaymentIcon fontSize="small" />;
    }
  };

  const columns: GridColDef[] = [
    {
      field: 'id',
      headerName: 'ID',
      width: 80,
    },
    {
      field: 'transaction_id',
      headerName: 'Transaction ID',
      width: 150,
      renderCell: (params) => (
        <Typography variant="body2" fontFamily="monospace">
          {params.value}
        </Typography>
      ),
    },
    {
      field: 'user',
      headerName: 'Customer',
      width: 200,
      renderCell: (params) => (
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Avatar sx={{ width: 32, height: 32, fontSize: '0.875rem' }}>
            {params.row.user?.name?.charAt(0) || 'U'}
          </Avatar>
          <Box>
            <Typography variant="body2" fontWeight="medium">
              {params.row.user?.name || 'Unknown'}
            </Typography>
            <Typography variant="caption" color="text.secondary">
              {params.row.user?.email || 'N/A'}
            </Typography>
          </Box>
        </Box>
      ),
    },
    {
      field: 'order_id',
      headerName: 'Order ID',
      width: 100,
      renderCell: (params) => (
        <Typography variant="body2">
          #{params.value}
        </Typography>
      ),
    },
    {
      field: 'amount',
      headerName: 'Amount',
      width: 120,
      renderCell: (params) => (
        <Typography variant="body2" fontWeight="medium">
          {params.row.currency} {params.value?.toFixed(2) || '0.00'}
        </Typography>
      ),
    },
    {
      field: 'payment_method',
      headerName: 'Payment Method',
      width: 150,
      renderCell: (params) => (
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          {getPaymentMethodIcon(params.value)}
          <Typography variant="body2">
            {params.value?.replace('_', ' ').toUpperCase() || 'N/A'}
          </Typography>
        </Box>
      ),
    },
    {
      field: 'status',
      headerName: 'Status',
      width: 120,
      renderCell: (params) => (
        <Chip
          label={params.value?.charAt(0).toUpperCase() + params.value?.slice(1) || 'Unknown'}
          color={getStatusColor(params.value) as any}
          size="small"
        />
      ),
    },
    {
      field: 'gateway',
      headerName: 'Gateway',
      width: 100,
      renderCell: (params) => (
        <Typography variant="body2">
          {params.value || 'N/A'}
        </Typography>
      ),
    },
    {
      field: 'gateway_transaction_id',
      headerName: 'Gateway TX ID',
      width: 150,
      renderCell: (params) => (
        <Typography variant="body2" fontFamily="monospace" fontSize="0.75rem">
          {params.value || 'N/A'}
        </Typography>
      ),
    },
    {
      field: 'created_at',
      headerName: 'Date',
      width: 120,
      renderCell: (params) => (
        <Typography variant="body2">
          {params.value ? new Date(params.value).toLocaleDateString() : 'N/A'}
        </Typography>
      ),
    },
    {
      field: 'actions',
      type: 'actions',
      headerName: 'Actions',
      width: 120,
      getActions: (params) => [
        <GridActionsCellItem
          key="view"
          icon={<VisibilityIcon />}
          label="View"
          onClick={() => router.push(`/payments/show/${params.id}`)}
        />,
        <GridActionsCellItem
          key="edit"
          icon={<EditIcon />}
          label="Edit"
          onClick={() => router.push(`/payments/edit/${params.id}`)}
        />,
        <GridActionsCellItem
          key="delete"
          icon={<DeleteIcon />}
          label="Delete"
          onClick={() => handleDelete(params.id as number)}
        />,
      ],
    },
  ];

  if (error) {
    return (
      <Alert severity="error">
        Error loading payments: {error.message}
      </Alert>
    );
  }

  return (
    <Stack spacing={3}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Typography variant="h4" component="h1" fontWeight="bold">
          Payment Management
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => router.push('/payments/create')}
        >
          Add Payment
        </Button>
      </Box>

      <Card>
        <CardContent>
          <Box sx={{ mb: 2 }}>
            <TextField
              placeholder="Search by transaction ID..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                ),
              }}
              sx={{ width: 300 }}
            />
          </Box>

          <DataGrid
            rows={data?.data || []}
            columns={columns}
            loading={isLoading}
            pageSizeOptions={[10, 25, 50]}
            initialState={{
              pagination: {
                paginationModel: { pageSize: 25 },
              },
            }}
            disableRowSelectionOnClick
            sx={{
              height: 600,
              '& .MuiDataGrid-cell': {
                borderBottom: '1px solid #f0f0f0',
              },
            }}
          />
        </CardContent>
      </Card>
    </Stack>
  );
}