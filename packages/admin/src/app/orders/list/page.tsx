'use client';

import React, { useState } from 'react';
import {
  useDataGrid,
} from '@refinedev/mui';
import { DataGrid, GridColDef, GridRowSelectionModel } from '@mui/x-data-grid';
import { 
  Box, 
  Button, 
  Card, 
  CardContent, 
  Stack, 
  Typography,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Avatar,
  IconButton,
  Tooltip,
  Alert,
  Badge,
  Divider,
  LinearProgress,
  Paper,
  Grid,
} from '@mui/material';
import { 
  useGo, 
  useMany, 
  useInvalidate,
  useNotification,
  useUpdate,
  useUpdateMany,
} from '@refinedev/core';
// Temporarily commenting out shared imports to fix build
// import { 
//   ORDER_STATUS, 
//   PAYMENT_STATUS, 
//   STATUS_COLORS, 
//   STATUS_LABELS
// } from '@azkuja/shared';

// Local constants to replace the shared ones
const ORDER_STATUS = {
  PENDING: 'pending',
  CONFIRMED: 'confirmed',
  PREPARING: 'preparing',
  READY: 'ready',
  DELIVERED: 'delivered',
  CANCELLED: 'cancelled',
} as const;

const PAYMENT_STATUS = {
  PENDING: 'pending',
  PAID: 'paid',
  FAILED: 'failed',
  REFUNDED: 'refunded',
} as const;

const STATUS_COLORS = {
  'order_pending': '#ff9800',
  'order_confirmed': '#2196f3',
  'order_preparing': '#9c27b0',
  'order_ready': '#4caf50',
  'order_delivered': '#8bc34a',
  'order_cancelled': '#f44336',
  'payment_pending': '#ff9800',
  'payment_paid': '#4caf50',
  'payment_failed': '#f44336',
  'payment_refunded': '#9e9e9e',
} as const;

const STATUS_LABELS = {
  'order_pending': 'در انتظار',
  'order_confirmed': 'تایید شده',
  'order_preparing': 'در حال آماده‌سازی',
  'order_ready': 'آماده',
  'order_delivered': 'تحویل داده شده',
  'order_cancelled': 'لغو شده',
  'payment_pending': 'در انتظار پرداخت',
  'payment_paid': 'پرداخت شده',
  'payment_failed': 'پرداخت ناموفق',
  'payment_refunded': 'بازگردانده شده',
} as const;
import { 
  Visibility as ViewIcon,
  Edit as EditIcon,
  LocalShipping as DeliveryIcon,
  Restaurant as RestaurantIcon,
  Person as PersonIcon,
  Payment as PaymentIcon,
  Schedule as ScheduleIcon,
  CheckCircle as CompleteIcon,
  Cancel as CancelIcon,
  Warning as WarningIcon,
  TrendingUp as TrendingUpIcon,
  Assignment as OrderIcon,
  FilterList as FilterIcon,
} from '@mui/icons-material';

// Force dynamic rendering
export const dynamic = 'force-dynamic';

interface Order {
  id: string;
  status: 'pending' | 'confirmed' | 'preparing' | 'ready' | 'delivered' | 'cancelled';
  total: number;
  delivery_fee: number;
  tax: number;
  payment_status: 'pending' | 'paid' | 'failed' | 'refunded';
  payment_method: string;
  delivery_address: string;
  special_instructions?: string;
  estimated_delivery_time?: string;
  created_at: string;
  updated_at: string;
  user: {
    id: string;
    name: string;
    phone: string;
    email: string;
  };
  restaurant: {
    id: string;
    name: string;
    city: string;
    phone: string;
  };
  order_items: Array<{
    id: string;
    quantity: number;
    unit_price: number;
    menu_item: {
      name: string;
      description: string;
    };
  }>;
}

export default function OrdersList() {
  const go = useGo();
  const invalidate = useInvalidate();
  const { open } = useNotification();
  
  const [selectedRows, setSelectedRows] = useState<GridRowSelectionModel>([]);
  const [statusDialog, setStatusDialog] = useState<{
    open: boolean;
    orderId?: string;
    currentStatus?: string;
    newStatus?: string;
    reason?: string;
  }>({ open: false });
  const [bulkStatusDialog, setBulkStatusDialog] = useState<{
    open: boolean;
    newStatus?: string;
  }>({ open: false });
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const [filterPaymentStatus, setFilterPaymentStatus] = useState<string>('all');
  const [filterDateRange, setFilterDateRange] = useState<string>('all');

  const { mutate: updateOrder } = useUpdate();
  const { mutate: updateManyOrders } = useUpdateMany();
  
  // Simple status update functions
  const updateOrderStatus = async (orderId: string, status: string) => {
    try {
      // Implementation would go here - for now just log
      console.log(`Updating order ${orderId} to status ${status}`);
    } catch (error) {
      console.error('Error updating order status:', error);
    }
  };

  const updateMultipleOrderStatuses = async (orderIds: string[], status: string) => {
    try {
      // Implementation would go here - for now just log
      console.log(`Updating orders ${orderIds.join(', ')} to status ${status}`);
    } catch (error) {
      console.error('Error updating multiple order statuses:', error);
    }
  };

  // Build filters based on state
  const filters = [
    ...(filterStatus !== 'all' ? [{ field: 'status', operator: 'eq', value: filterStatus }] : []),
    ...(filterPaymentStatus !== 'all' ? [{ field: 'payment_status', operator: 'eq', value: filterPaymentStatus }] : []),
    ...(filterDateRange !== 'all' ? [{ field: 'created_at', operator: 'gte', value: getDateRangeStart(filterDateRange) }] : []),
  ];

  const { dataGridProps } = useDataGrid({
    resource: 'orders',
    syncWithLocation: true,
    pagination: {
      mode: 'server',
    },
    filters: {
      mode: 'server',
      initial: filters,
    },
    sorters: {
      mode: 'server',
      initial: [{ field: 'created_at', order: 'desc' }],
    },
  });

  // Get related data
  const { data: restaurantData } = useMany({
    resource: 'restaurants',
    ids: dataGridProps?.rows?.map((item: any) => item.restaurant_id) ?? [],
    queryOptions: {
      enabled: dataGridProps?.rows?.length > 0,
    },
  });

  const { data: userData } = useMany({
    resource: 'users',
    ids: dataGridProps?.rows?.map((item: any) => item.user_id) ?? [],
    queryOptions: {
      enabled: dataGridProps?.rows?.length > 0,
    },
  });

  // Create lookup maps
  const restaurantMap: Record<string, any> = {};
  const userMap: Record<string, any> = {};

  if (restaurantData?.data) {
    restaurantData.data.forEach((item: any) => {
      restaurantMap[item.id.toString()] = item;
    });
  }

  if (userData?.data) {
    userData.data.forEach((item: any) => {
      userMap[item.id.toString()] = item;
    });
  }

  function getDateRangeStart(range: string): string {
    const now = new Date();
    switch (range) {
      case 'today':
        return new Date(now.getFullYear(), now.getMonth(), now.getDate()).toISOString();
      case 'week':
        return new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000).toISOString();
      case 'month':
        return new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000).toISOString();
      default:
        return new Date(0).toISOString();
    }
  }

  const handleStatusUpdate = async (orderId: string, newStatus: string, reason?: string) => {
    try {
      await updateOrderStatus(orderId, newStatus as any);
    } catch (error) {
      console.error('Error updating order status:', error);
    }
  };

  const handleBulkStatusUpdate = async (newStatus: string) => {
    try {
      await updateMultipleOrderStatuses(selectedRows.map(id => id.toString()), newStatus as any);
      setSelectedRows([]);
      setBulkStatusDialog({ open: false });
    } catch (error) {
      console.error('Error updating multiple order statuses:', error);
    }
  };

  const getStatusColor = (status: string) => {
    return STATUS_COLORS[status as keyof typeof STATUS_COLORS] || 'default';
  };

  const getStatusLabel = (status: string) => {
    return STATUS_LABELS[status as keyof typeof STATUS_LABELS] || status;
  };

  const getPaymentStatusColor = (status: string) => {
    switch (status) {
      case 'paid': return 'success';
      case 'pending': return 'warning';
      case 'failed': return 'error';
      case 'refunded': return 'info';
      default: return 'default';
    }
  };

  const getPaymentStatusLabel = (status: string) => {
    switch (status) {
      case 'paid': return 'پرداخت شده';
      case 'pending': return 'در انتظار پرداخت';
      case 'failed': return 'پرداخت ناموفق';
      case 'refunded': return 'بازگشت وجه';
      default: return status;
    }
  };

  const formatCurrency = (amount: number) => {
    return `${amount.toLocaleString('fa-IR')} ؋`;
  };

  const getStatusActions = (currentStatus: string) => {
    const actions = [];
    
    switch (currentStatus) {
      case 'pending':
        actions.push('confirmed', 'cancelled');
        break;
      case 'confirmed':
        actions.push('preparing', 'cancelled');
        break;
      case 'preparing':
        actions.push('ready', 'cancelled');
        break;
      case 'ready':
        actions.push('delivered');
        break;
      default:
        break;
    }
    
    return actions;
  };

  // Calculate summary statistics
  const totalOrders = dataGridProps.rowCount || 0;
  const pendingOrders = dataGridProps.rows?.filter((r: any) => r.status === 'pending').length || 0;
  const todayOrders = dataGridProps.rows?.filter((r: any) => 
    new Date(r.created_at).toDateString() === new Date().toDateString()
  ).length || 0;
  const totalRevenue = dataGridProps.rows?.reduce((sum: number, r: any) => sum + (Number(r.total) || 0), 0) || 0;

  const columns: GridColDef[] = [
    {
      field: 'id',
      headerName: 'شناسه',
      width: 100,
      renderCell: ({ value }) => (
        <Typography variant="body2" fontWeight="medium">
          #{value?.toString().slice(-6)}
            </Typography>
      ),
    },
    {
      field: 'user_id',
      headerName: 'مشتری',
      width: 180,
      renderCell: ({ row }) => {
        const user = userMap[row.user_id?.toString()];
        return (
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Avatar sx={{ width: 32, height: 32 }}>
              <PersonIcon />
            </Avatar>
          <Box>
              <Typography variant="body2" fontWeight="medium">
                {user?.name || 'نامشخص'}
            </Typography>
              <Typography variant="caption" color="text.secondary">
                {user?.phone || ''}
            </Typography>
          </Box>
        </Box>
        );
      },
    },
    {
      field: 'restaurant_id',
      headerName: 'رستوران',
      width: 180,
      renderCell: ({ row }) => {
        const restaurant = restaurantMap[row.restaurant_id?.toString()];
        return (
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Avatar sx={{ width: 32, height: 32 }}>
              <RestaurantIcon />
            </Avatar>
            <Box>
              <Typography variant="body2" fontWeight="medium">
                {restaurant?.name || 'نامشخص'}
              </Typography>
              <Typography variant="caption" color="text.secondary">
                {restaurant?.city || ''}
              </Typography>
            </Box>
          </Box>
        );
      },
    },
    {
      field: 'status',
      headerName: 'وضعیت سفارش',
      width: 150,
      renderCell: ({ value, row }) => (
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 0.5 }}>
          <Chip
            label={getStatusLabel(value)}
            color={getStatusColor(value) as any}
            size="small"
          />
          {row.estimated_delivery_time && (
            <Typography variant="caption" color="text.secondary">
              تحویل: {new Date(row.estimated_delivery_time).toLocaleTimeString('fa-IR', {
                hour: '2-digit',
                minute: '2-digit',
              })}
            </Typography>
          )}
        </Box>
      ),
    },
    {
      field: 'payment_status',
      headerName: 'وضعیت پرداخت',
      width: 130,
      renderCell: ({ value, row }) => (
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 0.5 }}>
          <Chip
            label={getPaymentStatusLabel(value)}
            color={getPaymentStatusColor(value) as any}
            size="small"
            variant="outlined"
          />
          <Typography variant="caption" color="text.secondary">
            {row.payment_method || 'نامشخص'}
          </Typography>
        </Box>
      ),
    },
    {
      field: 'total',
      headerName: 'مبلغ کل',
      width: 120,
      renderCell: ({ value, row }) => (
        <Box>
          <Typography variant="body2" fontWeight="medium">
            {formatCurrency(value)}
          </Typography>
          {row.delivery_fee > 0 && (
            <Typography variant="caption" color="text.secondary">
              حمل: {formatCurrency(row.delivery_fee)}
            </Typography>
          )}
        </Box>
      ),
    },
    {
      field: 'order_items',
      headerName: 'آیتم‌ها',
      width: 100,
      renderCell: ({ value }) => (
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
          <OrderIcon fontSize="small" color="primary" />
          <Typography variant="body2">
            {Array.isArray(value) ? value.length : 0}
          </Typography>
        </Box>
      ),
    },
    {
      field: 'created_at',
      headerName: 'تاریخ سفارش',
      width: 150,
      renderCell: ({ value }) => (
          <Box>
            <Typography variant="body2">
            {new Date(value).toLocaleDateString('fa-IR')}
            </Typography>
          <Typography variant="caption" color="text.secondary">
            {new Date(value).toLocaleTimeString('fa-IR', {
                hour: '2-digit', 
              minute: '2-digit',
              })}
            </Typography>
        </Box>
      ),
    },
    {
      field: 'actions',
      headerName: 'عملیات',
      width: 200,
      sortable: false,
      renderCell: ({ row }) => (
        <Box sx={{ display: 'flex', gap: 0.5 }}>
          <Tooltip title="مشاهده جزئیات">
            <IconButton
              size="small"
              onClick={() => go({ to: `/orders/show/${row.id}` })}
            >
              <ViewIcon />
            </IconButton>
          </Tooltip>
          
          {getStatusActions(row.status).map((action) => (
            <Tooltip key={action} title={`تغییر به ${getStatusLabel(action)}`}>
              <IconButton
                size="small"
                color={action === 'cancelled' ? 'error' : 'primary'}
                onClick={() => setStatusDialog({ 
                  open: true, 
                  orderId: row.id, 
                  currentStatus: row.status,
                  newStatus: action 
                })}
              >
                {action === 'confirmed' && <CompleteIcon />}
                {action === 'preparing' && <ScheduleIcon />}
                {action === 'ready' && <CompleteIcon />}
                {action === 'delivered' && <DeliveryIcon />}
                {action === 'cancelled' && <CancelIcon />}
              </IconButton>
            </Tooltip>
          ))}
        </Box>
      ),
    },
  ];

  return (
          <Box>
      {/* Summary Cards */}
      <Grid container spacing={2} mb={3}>
        <Grid item xs={12} sm={6} md={3}>
          <Paper sx={{ p: 2 }}>
            <Stack direction="row" alignItems="center" spacing={2}>
              <Box sx={{ p: 1, backgroundColor: 'primary.light', borderRadius: 1 }}>
                <OrderIcon sx={{ color: 'primary.main' }} />
          </Box>
              <Box>
                <Typography variant="h5" fontWeight="bold">
                  {totalOrders.toLocaleString('fa-IR')}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  کل سفارش‌ها
                </Typography>
              </Box>
            </Stack>
          </Paper>
          </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Paper sx={{ p: 2 }}>
            <Stack direction="row" alignItems="center" spacing={2}>
              <Box sx={{ p: 1, backgroundColor: 'warning.light', borderRadius: 1 }}>
                <WarningIcon sx={{ color: 'warning.main' }} />
              </Box>
              <Box>
                <Typography variant="h5" fontWeight="bold">
                  {pendingOrders.toLocaleString('fa-IR')}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  در انتظار
                </Typography>
              </Box>
            </Stack>
          </Paper>
          </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Paper sx={{ p: 2 }}>
            <Stack direction="row" alignItems="center" spacing={2}>
              <Box sx={{ p: 1, backgroundColor: 'info.light', borderRadius: 1 }}>
                <ScheduleIcon sx={{ color: 'info.main' }} />
              </Box>
              <Box>
                <Typography variant="h5" fontWeight="bold">
                  {todayOrders.toLocaleString('fa-IR')}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  امروز
                </Typography>
              </Box>
            </Stack>
          </Paper>
          </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Paper sx={{ p: 2 }}>
            <Stack direction="row" alignItems="center" spacing={2}>
              <Box sx={{ p: 1, backgroundColor: 'success.light', borderRadius: 1 }}>
                <TrendingUpIcon sx={{ color: 'success.main' }} />
              </Box>
              <Box>
                <Typography variant="h5" fontWeight="bold">
                  {formatCurrency(totalRevenue)}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  کل درآمد
                </Typography>
              </Box>
            </Stack>
          </Paper>
        </Grid>
            </Grid>

      <Card sx={{ mb: 2 }}>
        <CardContent>
          <Stack direction="row" justifyContent="space-between" alignItems="center" mb={2}>
            <Typography variant="h5" component="h1">
              مدیریت سفارش‌ها
            </Typography>
            <Box sx={{ display: 'flex', gap: 1 }}>
              {selectedRows.length > 0 && (
                <Button
                  variant="contained"
                  onClick={() => setBulkStatusDialog({ open: true })}
                >
                  بروزرسانی گروهی ({selectedRows.length})
                </Button>
              )}
            </Box>
          </Stack>

          {/* Filters */}
          <Stack direction="row" spacing={2} mb={2}>
            <FormControl size="small" sx={{ minWidth: 150 }}>
              <InputLabel>وضعیت سفارش</InputLabel>
                <Select
                  value={filterStatus}
                label="وضعیت سفارش"
                  onChange={(e) => setFilterStatus(e.target.value)}
              >
                <MenuItem value="all">همه</MenuItem>
                <MenuItem value={ORDER_STATUS.PENDING}>{STATUS_LABELS[ORDER_STATUS.PENDING]}</MenuItem>
                <MenuItem value={ORDER_STATUS.CONFIRMED}>{STATUS_LABELS[ORDER_STATUS.CONFIRMED]}</MenuItem>
                <MenuItem value={ORDER_STATUS.PREPARING}>{STATUS_LABELS[ORDER_STATUS.PREPARING]}</MenuItem>
                <MenuItem value={ORDER_STATUS.READY}>{STATUS_LABELS[ORDER_STATUS.READY]}</MenuItem>
                <MenuItem value={ORDER_STATUS.DELIVERED}>{STATUS_LABELS[ORDER_STATUS.DELIVERED]}</MenuItem>
                <MenuItem value={ORDER_STATUS.CANCELLED}>{STATUS_LABELS[ORDER_STATUS.CANCELLED]}</MenuItem>
                </Select>
              </FormControl>

            <FormControl size="small" sx={{ minWidth: 150 }}>
              <InputLabel>وضعیت پرداخت</InputLabel>
                <Select
                value={filterPaymentStatus}
                label="وضعیت پرداخت"
                onChange={(e) => setFilterPaymentStatus(e.target.value)}
              >
                <MenuItem value="all">همه</MenuItem>
                <MenuItem value={PAYMENT_STATUS.PENDING}>{STATUS_LABELS[PAYMENT_STATUS.PENDING]}</MenuItem>
                <MenuItem value={PAYMENT_STATUS.PAID}>{STATUS_LABELS[PAYMENT_STATUS.PAID]}</MenuItem>
                <MenuItem value={PAYMENT_STATUS.FAILED}>{STATUS_LABELS[PAYMENT_STATUS.FAILED]}</MenuItem>
                <MenuItem value={PAYMENT_STATUS.REFUNDED}>{STATUS_LABELS[PAYMENT_STATUS.REFUNDED]}</MenuItem>
                </Select>
              </FormControl>

            <FormControl size="small" sx={{ minWidth: 120 }}>
              <InputLabel>بازه زمانی</InputLabel>
              <Select
                value={filterDateRange}
                label="بازه زمانی"
                onChange={(e) => setFilterDateRange(e.target.value)}
              >
                <MenuItem value="all">همه</MenuItem>
                <MenuItem value="today">امروز</MenuItem>
                <MenuItem value="week">هفته گذشته</MenuItem>
                <MenuItem value="month">ماه گذشته</MenuItem>
              </Select>
            </FormControl>
          </Stack>
        </CardContent>
      </Card>

      <Card>
          <DataGrid
            {...dataGridProps}
            columns={columns}
          checkboxSelection
          disableRowSelectionOnClick
          onRowSelectionModelChange={setSelectedRows}
          rowSelectionModel={selectedRows}
            autoHeight
            sx={{
              '& .MuiDataGrid-row': {
              cursor: 'pointer',
              },
            }}
          />
      </Card>

      {/* Status Update Dialog */}
      <Dialog
        open={statusDialog.open}
        onClose={() => setStatusDialog({ open: false })}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          تغییر وضعیت سفارش
        </DialogTitle>
        <DialogContent>
          <Typography sx={{ mb: 2 }}>
            وضعیت سفارش از "{getStatusLabel(statusDialog.currentStatus || '')}" به "{getStatusLabel(statusDialog.newStatus || '')}" تغییر خواهد کرد.
          </Typography>
          <TextField
            fullWidth
            multiline
            rows={3}
            label="دلیل تغییر (اختیاری)"
            value={statusDialog.reason || ''}
            onChange={(e) => setStatusDialog(prev => ({ ...prev, reason: e.target.value }))}
            sx={{ mt: 2 }}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setStatusDialog({ open: false })}>
            انصراف
          </Button>
          <Button
            variant="contained"
            onClick={() => {
              if (statusDialog.orderId && statusDialog.newStatus) {
                handleStatusUpdate(statusDialog.orderId, statusDialog.newStatus, statusDialog.reason);
              }
              setStatusDialog({ open: false });
            }}
          >
            تایید
          </Button>
        </DialogActions>
      </Dialog>

      {/* Bulk Status Update Dialog */}
      <Dialog
        open={bulkStatusDialog.open}
        onClose={() => setBulkStatusDialog({ open: false })}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          بروزرسانی گروهی وضعیت
        </DialogTitle>
        <DialogContent>
          <Typography sx={{ mb: 2 }}>
            وضعیت {selectedRows.length} سفارش انتخاب شده تغییر خواهد کرد.
          </Typography>
          <FormControl fullWidth sx={{ mt: 2 }}>
            <InputLabel>وضعیت جدید</InputLabel>
            <Select
              value={bulkStatusDialog.newStatus || ''}
              label="وضعیت جدید"
              onChange={(e) => setBulkStatusDialog(prev => ({ ...prev, newStatus: e.target.value }))}
            >
              <MenuItem value={ORDER_STATUS.CONFIRMED}>{STATUS_LABELS[ORDER_STATUS.CONFIRMED]}</MenuItem>
              <MenuItem value={ORDER_STATUS.PREPARING}>{STATUS_LABELS[ORDER_STATUS.PREPARING]}</MenuItem>
              <MenuItem value={ORDER_STATUS.READY}>{STATUS_LABELS[ORDER_STATUS.READY]}</MenuItem>
              <MenuItem value={ORDER_STATUS.DELIVERED}>{STATUS_LABELS[ORDER_STATUS.DELIVERED]}</MenuItem>
              <MenuItem value={ORDER_STATUS.CANCELLED}>{STATUS_LABELS[ORDER_STATUS.CANCELLED]}</MenuItem>
            </Select>
          </FormControl>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setBulkStatusDialog({ open: false })}>
            انصراف
          </Button>
          <Button
            variant="contained"
            onClick={() => {
              if (bulkStatusDialog.newStatus) {
                handleBulkStatusUpdate(bulkStatusDialog.newStatus);
              }
            }}
            disabled={!bulkStatusDialog.newStatus}
          >
            بروزرسانی
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
}