// API Configuration
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:7000/api';

// Types
export interface Restaurant {
  id: string;
  name: string;
  description?: string;
  address: string;
  city: string;
  state: string;
  zip_code: string;
  phone: string;
  email?: string;
  website?: string;
  hours_of_operation?: any;
  latitude?: number;
  longitude?: number;
  avg_rating: number;
  price_range: number;
  is_featured: boolean;
  status: 'active' | 'pending' | 'suspended';
  created_at: string;
  updated_at: string;
  owner?: User;
  photos?: Photo[];
  menuItems?: MenuItem[];
  categories?: Category[];
  reviews?: Review[];
}

export interface User {
  id: string;
  phone_number: string;
  email?: string;
  name: string;
  role: 'admin' | 'restaurant_owner' | 'customer';
  profile_picture?: string;
  is_verified: boolean;
  created_at: string;
  updated_at: string;
}

export interface Photo {
  id: string;
  url: string;
  caption?: string;
  alt_text?: string;
  created_at: string;
  restaurant?: Restaurant;
}

export interface MenuItem {
  id: string;
  name: string;
  description: string;
  price: number;
  category: string;
  imageUrl?: string;
  isAvailable: boolean;
  isVegetarian: boolean;
  isVegan: boolean;
  dietaryInfo?: string;
  created_at: string;
  updated_at: string;
  restaurant?: Restaurant;
}

export interface Category {
  id: string;
  name: string;
  description?: string;
  icon?: string;
  created_at: string;
  updated_at: string;
}

export interface Review {
  id: string;
  rating: number;
  comment: string;
  images?: string[];
  helpfulCount: number;
  unhelpfulCount: number;
  createdAt: string;
  updatedAt: string;
  user?: User;
  restaurant?: Restaurant;
}

export interface Order {
  id: string;
  status: 'pending' | 'confirmed' | 'preparing' | 'ready' | 'delivered' | 'cancelled';
  total_amount: number;
  delivery_fee?: number;
  tax_amount?: number;
  delivery_address?: string;
  special_instructions?: string;
  estimated_delivery_time?: string;
  created_at: string;
  updated_at: string;
  customer?: User;
  restaurant?: Restaurant;
  items?: OrderItem[];
}

export interface OrderItem {
  id: string;
  quantity: number;
  price: number;
  special_instructions?: string;
  menuItem?: MenuItem;
}

export interface RegisterDto {
  phone_number: string;
  name: string;
  email?: string;
}

export interface LoginDto {
  phone_number: string;
}

export interface VerifyOtpDto {
  phone_number: string;
  otp_code: string;
}

export interface AuthResponse {
  accessToken: string;
  user: User;
}

export interface FilterRestaurantParams {
  search?: string;
  city?: string;
  categories?: string[];
  rating?: number;
  price_range?: number[];
  is_featured?: boolean;
  status?: string;
  latitude?: number;
  longitude?: number;
  radius?: number;
  sort_by?: 'name' | 'rating' | 'distance' | 'created_at';
  sort_order?: 'asc' | 'desc';
  page?: number;
  limit?: number;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface UserProfile {
  id: string;
  userId: string;
  avatar_url?: string;
  bio?: string;
  birth_date?: string;
  gender?: 'male' | 'female' | 'other' | 'prefer_not_to_say';
  location?: string;
  language?: string;
  food_preferences?: string;
  website?: string;
  social_links?: Record<string, string>;
  notification_preferences?: Record<string, boolean>;
  user?: User;
  // Legacy fields for backward compatibility
  name?: string;
  phone_number?: string;
  email?: string;
  profile_picture?: string;
  address?: string;
  date_of_birth?: string;
  preferences?: any;
  created_at?: string;
  updated_at?: string;
}

export interface UserSettings {
  id: string;
  email_notifications: boolean;
  sms_notifications: boolean;
  push_notifications: boolean;
  marketing_emails: boolean;
  language: string;
  timezone: string;
  privacy_level: 'public' | 'private';
  created_at: string;
  updated_at: string;
}

export interface Favorite {
  id: string;
  note?: string;
  created_at: string;
  restaurant: Restaurant;
}

export interface UserStats {
  total_orders: number;
  total_spent: number;
  favorite_restaurants: number;
  reviews_written: number;
  avg_rating_given: number;
  loyalty_points: number;
}

export interface UpdateProfileDto {
  avatar_url?: string;
  bio?: string;
  birth_date?: string;
  gender?: 'male' | 'female' | 'other' | 'prefer_not_to_say';
  location?: string;
  language?: string;
  food_preferences?: string;
  website?: string;
  social_links?: Record<string, string>;
  notification_preferences?: Record<string, boolean>;
  // Legacy fields for backward compatibility
  name?: string;
  email?: string;
  profile_picture?: string;
  address?: string;
  date_of_birth?: string;
}

export interface UpdateSettingsDto {
  email_notifications?: boolean;
  sms_notifications?: boolean;
  push_notifications?: boolean;
  marketing_emails?: boolean;
  language?: string;
  timezone?: string;
  privacy_level?: 'public' | 'private';
}

export interface CreateReviewDto {
  restaurantId: string;
  rating: number;
  comment: string;
  images?: string[];
}

export interface CreateFavoriteDto {
  restaurantId: string;
  note?: string;
}

// Loyalty Points interfaces
export interface LoyaltyPoints {
  id: string
  userId: string
  points: number
  totalEarned: number
  totalRedeemed: number
  tier: 'bronze' | 'silver' | 'gold' | 'platinum'
  nextTierPoints: number
  createdAt: string
  updatedAt: string
}

export interface PointsTransaction {
  id: string
  userId: string
  type: 'earned' | 'redeemed'
  points: number
  reason: string
  orderId?: string
  reviewId?: string
  createdAt: string
}

// Address Book interfaces
export interface Address {
  id: string
  userId: string
  type: 'home' | 'work' | 'other'
  label: string
  street: string
  city: string
  state: string
  postalCode: string
  country: string
  isDefault: boolean
  coordinates?: {
    lat: number
    lng: number
  }
  instructions?: string
  createdAt: string
  updatedAt: string
}

// Notification interfaces
export interface Notification {
  id: string
  userId: string
  type: 'order' | 'restaurant' | 'promotion' | 'system'
  title: string
  message: string
  data?: any
  isRead: boolean
  createdAt: string
}

// Enhanced Order interface with tracking
export interface OrderTracking {
  id: string
  orderId: string
  status: 'confirmed' | 'preparing' | 'ready' | 'picked_up' | 'on_the_way' | 'delivered' | 'cancelled'
  estimatedTime: string
  actualTime?: string
  location?: {
    lat: number
    lng: number
  }
  driverInfo?: {
    name: string
    phone: string
    photo?: string
  }
  createdAt: string
  updatedAt: string
}

class ApiService {
  private baseURL: string;
  private token: string | null = null;

  constructor() {
    this.baseURL = API_BASE_URL;
    // Initialize token from localStorage if available
    if (typeof window !== 'undefined') {
      this.token = localStorage.getItem('accessToken');
    }
  }

  private getAuthHeaders(): Record<string, string> {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
    };

    if (this.token) {
      headers['Authorization'] = `Bearer ${this.token}`;
    }

    return headers;
  }

  private async request<T>(endpoint: string, options?: RequestInit): Promise<T> {
    const url = `${this.baseURL}${endpoint}`;
    
    const config: RequestInit = {
      headers: this.getAuthHeaders(),
      ...options,
    };

    try {
      const response = await fetch(url, config);
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ message: response.statusText }));
        console.error('API Error Response:', {
          status: response.status,
          statusText: response.statusText,
          url: url,
          errorData: errorData
        });
        throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
      }

      const contentType = response.headers.get('content-type');
      if (contentType && contentType.includes('application/json')) {
        return await response.json();
      }
      
      return {} as T;
    } catch (error) {
      console.error('API request failed:', error);
      throw error;
    }
  }

  // Authentication methods
  async register(data: RegisterDto): Promise<{ message: string }> {
    return this.request<{ message: string }>('/auth/register', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async login(data: LoginDto): Promise<{ message: string }> {
    return this.request<{ message: string }>('/auth/login', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async verifyOtp(data: VerifyOtpDto): Promise<AuthResponse> {
    const response = await this.request<AuthResponse>('/auth/verify', {
      method: 'POST',
      body: JSON.stringify(data),
    });
    
    // Store token in localStorage and update instance
    if (response.accessToken) {
      this.token = response.accessToken;
      if (typeof window !== 'undefined') {
        localStorage.setItem('accessToken', response.accessToken);
        localStorage.setItem('user', JSON.stringify(response.user));
      }
    }
    
    return response;
  }

  async logout(): Promise<void> {
    try {
      if (this.token) {
        await this.request('/auth/logout', {
          method: 'POST',
        });
      }
    } finally {
      // Clear token regardless of API call success
      this.token = null;
      if (typeof window !== 'undefined') {
        localStorage.removeItem('accessToken');
        localStorage.removeItem('user');
      }
    }
  }

  getCurrentUser(): User | null {
    if (typeof window !== 'undefined') {
      const userStr = localStorage.getItem('auth_user');
      return userStr ? JSON.parse(userStr) : null;
    }
    return null;
  }

  async getCurrentUserFromAPI(): Promise<User | null> {
    try {
      const response = await this.request<User>('/auth/me', {
        method: 'GET',
      });
      return response;
    } catch (error) {
      console.error('Error fetching current user:', error);
      return null;
    }
  }

  isAuthenticated(): boolean {
    return !!this.token;
  }

  getToken(): string | null {
    return this.token;
  }

  // Restaurant endpoints
  async getRestaurants(params?: FilterRestaurantParams): Promise<PaginatedResponse<Restaurant>> {
    const queryParams = new URLSearchParams();
    
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          if (Array.isArray(value)) {
            value.forEach(v => queryParams.append(key, v.toString()));
          } else {
            queryParams.append(key, value.toString());
          }
        }
      });
    }

    const endpoint = `/restaurants${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    return this.request<PaginatedResponse<Restaurant>>(endpoint);
  }

  async getRestaurant(id: string): Promise<Restaurant> {
    return this.request<Restaurant>(`/restaurants/${id}`);
  }

  async getFeaturedRestaurants(limit = 8): Promise<Restaurant[]> {
    const response = await this.getRestaurants({ is_featured: true, limit });
    return response.data;
  }

  async getRestaurantsByCategory(categoryId: string, params?: FilterRestaurantParams): Promise<PaginatedResponse<Restaurant>> {
    const queryParams = new URLSearchParams();
    
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          if (Array.isArray(value)) {
            value.forEach(v => queryParams.append(key, v.toString()));
          } else {
            queryParams.append(key, value.toString());
          }
        }
      });
    }

    const endpoint = `/restaurants/category/${categoryId}${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    return this.request<PaginatedResponse<Restaurant>>(endpoint);
  }

  // Category endpoints
  async getCategories(): Promise<Category[]> {
    return this.request<Category[]>('/categories');
  }

  // Menu endpoints
  async getRestaurantMenu(restaurantId: string): Promise<MenuItem[]> {
    return this.request<MenuItem[]>(`/restaurants/${restaurantId}/menu`);
  }

  // Review endpoints
  async getRestaurantReviews(restaurantId: string): Promise<PaginatedResponse<Review>> {
    return this.request<PaginatedResponse<Review>>(`/reviews/restaurant/${restaurantId}`);
  }

  async getRestaurantReviewStats(restaurantId: string): Promise<any> {
    return this.request<any>(`/reviews/restaurant/${restaurantId}/stats`);
  }

  async createReview(reviewData: {
    restaurantId: string;
    rating: number;
    comment: string;
    images?: string[];
  }): Promise<Review> {
    const formData = new FormData();
    formData.append('restaurantId', reviewData.restaurantId);
    formData.append('rating', reviewData.rating.toString());
    formData.append('comment', reviewData.comment);
    
    if (reviewData.images) {
      reviewData.images.forEach((image, index) => {
        formData.append(`images`, image);
      });
    }

    return this.request<Review>('/reviews', {
      method: 'POST',
      body: formData,
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  }

  async updateReview(reviewId: string, reviewData: {
    rating?: number;
    comment?: string;
    images?: File[];
  }): Promise<Review> {
    const formData = new FormData();
    if (reviewData.rating) formData.append('rating', reviewData.rating.toString());
    if (reviewData.comment) formData.append('comment', reviewData.comment);
    
    if (reviewData.images) {
      reviewData.images.forEach((image, index) => {
        formData.append(`images`, image);
      });
    }

    return this.request<Review>(`/reviews/${reviewId}`, {
      method: 'PATCH',
      body: formData,
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  }

  async deleteReview(reviewId: string): Promise<void> {
    return this.request<void>(`/reviews/${reviewId}`, {
      method: 'DELETE',
    });
  }

  async markReviewHelpful(reviewId: string): Promise<void> {
    return this.request<void>(`/reviews/${reviewId}/helpful`, {
      method: 'POST',
    });
  }

  async reportReview(reviewId: string): Promise<void> {
    return this.request<void>(`/reviews/${reviewId}/report`, {
      method: 'POST',
    });
  }

  async getUserReviews(userId?: string): Promise<PaginatedResponse<Review>> {
    const endpoint = userId ? `/reviews/user/${userId}` : '/reviews/my-reviews';
    return this.request<PaginatedResponse<Review>>(endpoint);
  }

  // Menu item reviews
  async getMenuItemReviews(menuItemId: string): Promise<PaginatedResponse<Review>> {
    return this.request<PaginatedResponse<Review>>(`/reviews/menu-item/${menuItemId}`);
  }

  async createMenuItemReview(reviewData: {
    menuItemId: string;
    rating: number;
    comment: string;
    images?: File[];
  }): Promise<Review> {
    const formData = new FormData();
    formData.append('menuItemId', reviewData.menuItemId);
    formData.append('rating', reviewData.rating.toString());
    formData.append('comment', reviewData.comment);
    
    if (reviewData.images) {
      reviewData.images.forEach((image, index) => {
        formData.append(`images`, image);
      });
    }

    return this.request<Review>('/reviews/menu-item', {
      method: 'POST',
      body: formData,
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  }

  // User Profile endpoints
  async getUserProfile(): Promise<UserProfile> {
    return this.request<UserProfile>('/users/profiles/me');
  }

  async updateUserProfile(data: UpdateProfileDto): Promise<UserProfile> {
    return this.request<UserProfile>('/users/profiles/me', {
      method: 'PATCH',
      body: JSON.stringify(data),
    });
  }

  async getUserSettings(): Promise<UserSettings> {
    return this.request<UserSettings>('/users/settings');
  }

  async updateUserSettings(data: UpdateSettingsDto): Promise<UserSettings> {
    return this.request<UserSettings>('/users/settings', {
      method: 'PATCH',
      body: JSON.stringify(data),
    });
  }

  async getUserStats(): Promise<UserStats> {
    try {
      // Since the stats endpoint doesn't exist, calculate from existing data
      const [orders, reviews, favorites] = await Promise.all([
        this.getUserOrders(1, 1000).catch(() => ({ data: [], total: 0 })),
        this.getUserReviews().catch(() => ({ data: [], total: 0 })),
        this.getUserFavorites(1, 1000).catch(() => ({ data: [], total: 0 }))
      ]);

      const totalSpent = orders.data.reduce((sum, order) => sum + order.total_amount, 0);
      const avgRating = reviews.data.length > 0 
        ? reviews.data.reduce((sum, review) => sum + review.rating, 0) / reviews.data.length 
        : 0;

      return {
        total_orders: orders.data.length,
        total_spent: totalSpent,
        favorite_restaurants: favorites.data.length,
        reviews_written: reviews.data.length,
        avg_rating_given: Math.round(avgRating * 10) / 10,
        loyalty_points: Math.floor(totalSpent / 1000) // 1 point per 1000 units spent
      };
    } catch (error) {
      console.error('Error calculating user stats:', error);
      // Return default stats if calculation fails
      return {
        total_orders: 0,
        total_spent: 0,
        favorite_restaurants: 0,
        reviews_written: 0,
        avg_rating_given: 0,
        loyalty_points: 0
      };
    }
  }

  async uploadProfilePicture(file: File): Promise<{ url: string }> {
    const formData = new FormData();
    formData.append('file', file);

    return this.request<{ url: string }>('/files/upload', {
      method: 'POST',
      body: formData,
      headers: {
        'Authorization': `Bearer ${this.token}`,
        // Don't set Content-Type for FormData
      },
    });
  }

  // Favorites endpoints
  async getUserFavorites(page = 1, limit = 10): Promise<PaginatedResponse<Favorite>> {
    return this.request<PaginatedResponse<Favorite>>(`/favorites?page=${page}&limit=${limit}`);
  }

  async addToFavorites(data: CreateFavoriteDto): Promise<Favorite> {
    return this.request<Favorite>('/favorites', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async removeFromFavorites(favoriteId: string): Promise<void> {
    return this.request<void>(`/favorites/${favoriteId}`, {
      method: 'DELETE',
    });
  }

  async checkFavoriteStatus(restaurantId: string): Promise<{ isFavorited: boolean }> {
    return this.request<{ isFavorited: boolean }>(`/favorites/restaurant/${restaurantId}/status`);
  }

  // Orders endpoints
  async getUserOrders(page = 1, limit = 10): Promise<PaginatedResponse<Order>> {
    const backendOrders = await this.request<any[]>(`/orders/user/me`);
    
    // Transform backend response to match frontend Order interface
    const transformedOrders: Order[] = backendOrders.map(order => ({
      id: order.id,
      status: order.status,
      total_amount: Number(order.total),
      delivery_fee: Number(order.delivery_fee) || 0,
      tax_amount: Number(order.tax) || 0,
      delivery_address: order.delivery_address,
      special_instructions: order.special_instructions,
      estimated_delivery_time: order.estimated_delivery_time,
      created_at: order.created_at,
      updated_at: order.updated_at,
      customer: order.user,
      restaurant: order.restaurant,
      // Transform order_items to items with proper structure
      items: order.order_items?.map((item: any) => ({
        id: item.id,
        quantity: item.quantity,
        price: Number(item.unit_price),
        special_instructions: item.notes,
        menuItem: {
          id: item.menu_item?.id,
          name: item.menu_item?.name,
          description: item.menu_item?.description,
          price: Number(item.menu_item?.price),
          category: item.menu_item?.category,
          imageUrl: item.menu_item?.image_url,
          isAvailable: item.menu_item?.is_available !== false,
          isVegetarian: item.menu_item?.is_vegetarian || false,
          isVegan: item.menu_item?.is_vegan || false,
          dietaryInfo: item.menu_item?.dietary_info,
          created_at: item.menu_item?.created_at,
          updated_at: item.menu_item?.updated_at,
          restaurant: order.restaurant
        }
      })) || []
    }));
    
    // Apply pagination to transformed data
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedData = transformedOrders.slice(startIndex, endIndex);
    
    return {
      data: paginatedData,
      total: transformedOrders.length,
      page: page,
      limit: limit,
      totalPages: Math.ceil(transformedOrders.length / limit)
    };
  }

  async getOrder(orderId: string): Promise<Order> {
    return this.request<Order>(`/orders/${orderId}`);
  }

  async createOrder(data: {
    restaurantId: string;
    items: { menuItemId: string; quantity: number; special_instructions?: string }[];
    delivery_address?: string;
    special_instructions?: string;
    customer_phone?: string;
    customer_name?: string;
  }): Promise<Order> {
    // Transform to backend format
    const backendData = {
      restaurantId: data.restaurantId, // Keep camelCase for now, backend will transform
      items: data.items.map(item => ({
        menuItemId: item.menuItemId,
        quantity: item.quantity,
        special_instructions: item.special_instructions
      })),
      delivery_address: data.delivery_address,
      special_instructions: data.special_instructions,
      customer_phone: data.customer_phone,
      customer_name: data.customer_name,
      userId: this.getCurrentUser()?.id || null // Include user ID if authenticated
    };

    return this.request<Order>('/orders', {
      method: 'POST',
      body: JSON.stringify(backendData),
    });
  }

  async cancelOrder(orderId: string): Promise<void> {
    return this.request<void>(`/orders/${orderId}/cancel`, {
      method: 'PATCH',
    });
  }

  // Reservations endpoints
  async getUserReservations(page = 1, limit = 10): Promise<PaginatedResponse<any>> {
    return this.request<PaginatedResponse<any>>(`/reservations/my-reservations?page=${page}&limit=${limit}`);
  }

  async createReservation(data: {
    restaurantId: string;
    date: string;
    time: string;
    party_size: number;
    special_requests?: string;
  }): Promise<any> {
    return this.request<any>('/reservations', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async cancelReservation(reservationId: string): Promise<void> {
    return this.request<void>(`/reservations/${reservationId}`, {
      method: 'DELETE',
    });
  }

  // Loyalty Points API methods
  async getPoints(): Promise<LoyaltyPoints> {
    const response = await this.request<{ data: LoyaltyPoints }>('/loyalty/points');
    return response.data;
  }

  async getTransactions(page = 1, limit = 20): Promise<{
    transactions: PointsTransaction[]
    total: number
    hasMore: boolean
  }> {
    const response = await this.request<{
      data: {
        transactions: PointsTransaction[]
        total: number
        hasMore: boolean
      }
    }>(`/loyalty/transactions?page=${page}&limit=${limit}`);
    return response.data;
  }

  async redeemPoints(points: number, reason: string): Promise<PointsTransaction> {
    const response = await this.request<{ data: PointsTransaction }>('/loyalty/redeem', {
      method: 'POST',
      body: JSON.stringify({ points, reason })
    });
    return response.data;
  }

  // Address Book API methods
  async getAddresses(): Promise<Address[]> {
    const response = await this.request<{ data: Address[] }>('/addresses');
    return response.data;
  }

  async createAddress(address: Omit<Address, 'id' | 'userId' | 'createdAt' | 'updatedAt'>): Promise<Address> {
    const response = await this.request<{ data: Address }>('/addresses', {
      method: 'POST',
      body: JSON.stringify(address)
    });
    return response.data;
  }

  async updateAddress(id: string, address: Partial<Address>): Promise<Address> {
    const response = await this.request<{ data: Address }>(`/addresses/${id}`, {
      method: 'PUT',
      body: JSON.stringify(address)
    });
    return response.data;
  }

  async deleteAddress(id: string): Promise<void> {
    await this.request<void>(`/addresses/${id}`, {
      method: 'DELETE'
    });
  }

  async setDefaultAddress(id: string): Promise<void> {
    await this.request<void>(`/addresses/${id}/default`, {
      method: 'PUT'
    });
  }

  // Notifications API methods
  async getNotifications(page = 1, limit = 20): Promise<{
    notifications: Notification[]
    total: number
    unreadCount: number
    hasMore: boolean
  }> {
    const response = await this.request<{
      data: {
        notifications: Notification[]
        total: number
        unreadCount: number
        hasMore: boolean
      }
    }>(`/notifications?page=${page}&limit=${limit}`);
    return response.data;
  }

  async markAsRead(id: string): Promise<void> {
    await this.request<void>(`/notifications/${id}/read`, {
      method: 'PUT'
    });
  }

  async markAllAsRead(): Promise<void> {
    await this.request<void>('/notifications/read-all', {
      method: 'PUT'
    });
  }

  async deleteNotification(id: string): Promise<void> {
    await this.request<void>(`/notifications/${id}`, {
      method: 'DELETE'
    });
  }

  async updatePreferences(preferences: {
    orderUpdates: boolean
    promotions: boolean
    newRestaurants: boolean
    reviewReminders: boolean
    emailNotifications: boolean
    smsNotifications: boolean
  }): Promise<void> {
    await this.request<void>('/notifications/preferences', {
      method: 'PUT',
      body: JSON.stringify(preferences)
    });
  }

  // Order Tracking API methods
  async getOrderTracking(orderId: string): Promise<OrderTracking[]> {
    const response = await this.request<{ data: OrderTracking[] }>(`/orders/${orderId}/tracking`);
    return response.data;
  }

  subscribeToOrderUpdates(orderId: string, callback: (tracking: OrderTracking) => void) {
    // This would typically use WebSocket or Server-Sent Events
    // For now, we'll use polling
    const interval = setInterval(async () => {
      try {
        const tracking = await this.getOrderTracking(orderId);
        if (tracking.length > 0) {
          callback(tracking[tracking.length - 1]);
        }
      } catch (error) {
        console.error('Error fetching order updates:', error);
      }
    }, 30000); // Poll every 30 seconds

    return () => clearInterval(interval);
  }

  // Enhanced Reviews API methods
  async uploadReviewPhotos(files: File[]): Promise<string[]> {
    const formData = new FormData();
    files.forEach(file => formData.append('photos', file));

    const response = await this.request<{ urls: string[] }>('/reviews/photos', {
      method: 'POST',
      body: formData
    });

    if (!response.urls) {
      throw new Error('Failed to upload photos');
    }

    return response.urls;
  }

  async createReviewWithPhotos(review: {
    restaurantId: string
    rating: number
    comment: string
    photos?: string[]
    visitDate?: string
    orderItems?: string[]
  }): Promise<Review> {
    const response = await this.request<{ data: Review }>('/reviews', {
      method: 'POST',
      body: JSON.stringify(review)
    });
    return response.data;
  }

  async getUserReviewStats(): Promise<{
    totalReviews: number
    averageRating: number
    helpfulVotes: number
    photosShared: number
    reviewsThisMonth: number
    topCategories: string[]
  }> {
    const response = await this.request<{
      data: {
        totalReviews: number
        averageRating: number
        helpfulVotes: number
        photosShared: number
        reviewsThisMonth: number
        topCategories: string[]
      }
    }>('/reviews/stats');
    return response.data;
  }

  // Restaurant Owner Dashboard APIs
  async getOwnerRestaurant(): Promise<any> {
    return this.request<any>('/restaurants/owner');
  }

  async getRestaurantStats(): Promise<any> {
    return this.request<any>('/restaurants/stats');
  }

  async getRestaurantAnalytics(timeframe: string): Promise<any> {
    return this.request<any>(`/restaurants/analytics?timeframe=${timeframe}`);
  }

  async getRestaurantOrders(params: {
    restaurantId?: string
    status?: string
    page?: number
    limit?: number
  }): Promise<any> {
    // If no restaurantId provided, get it from the owner restaurant
    let restaurantId = params.restaurantId;
    if (!restaurantId) {
      try {
        const ownerRestaurant = await this.getOwnerRestaurant();
        restaurantId = ownerRestaurant.id;
      } catch (error) {
        console.error('Error getting owner restaurant:', error);
        throw new Error('Restaurant ID is required');
      }
    }

    return this.request<any[]>(`/orders/restaurant/${restaurantId}`);
  }

  async updateOrderStatus(orderId: string, status: string): Promise<any> {
    return this.request<any>(`/orders/${orderId}/status`, {
      method: 'PATCH',
      body: JSON.stringify({ status }),
    })
  }

  async updateOrderStatusWithDeliveryTime(orderId: string, status: string, estimatedDeliveryTime: string): Promise<any> {
    return this.request<any>(`/orders/${orderId}/status`, {
      method: 'PATCH',
      body: JSON.stringify({ 
        status,
        estimated_delivery_time: estimatedDeliveryTime
      }),
    })
  }

  async getMenuCategories(restaurantId: string): Promise<any> {
    return this.request<any>(`/restaurants/${restaurantId}/categories`)
  }

  async createMenuItem(restaurantId: string, itemData: any): Promise<any> {
    return this.request<any>(`/restaurants/${restaurantId}/menu`, {
      method: 'POST',
      body: JSON.stringify(itemData),
    })
  }

  async updateMenuItem(restaurantId: string, itemId: string, itemData: any): Promise<any> {
    return this.request<any>(`/restaurants/${restaurantId}/menu/${itemId}`, {
      method: 'PATCH',
      body: JSON.stringify(itemData),
    })
  }

  async deleteMenuItem(restaurantId: string, itemId: string): Promise<any> {
    return this.request<any>(`/restaurants/${restaurantId}/menu/${itemId}`, {
      method: 'DELETE',
    })
  }

  async createMenuCategory(restaurantId: string, categoryData: any): Promise<any> {
    return this.request<any>(`/restaurants/${restaurantId}/categories`, {
      method: 'POST',
      body: JSON.stringify(categoryData),
    })
  }

  async updateMenuCategory(categoryId: string, categoryData: any): Promise<any> {
    return this.request<any>(`/categories/${categoryId}`, {
      method: 'PUT',
      body: JSON.stringify(categoryData),
    })
  }

  async deleteMenuCategory(categoryId: string): Promise<any> {
    return this.request<any>(`/categories/${categoryId}`, {
      method: 'DELETE',
    })
  }

  async getRestaurantSettings(restaurantId: string): Promise<Restaurant> {
    return this.request<Restaurant>(`/restaurants/${restaurantId}`)
  }

  async updateRestaurantSettings(restaurantId: string, settings: any): Promise<Restaurant> {
    return this.request<Restaurant>(`/restaurants/${restaurantId}`, {
      method: 'PATCH',
      body: JSON.stringify(settings),
    })
  }

  async exportAnalyticsReport(restaurantId: string, timeframe: string): Promise<Blob> {
    const response = await fetch(`${this.baseURL}/analytics/export?restaurantId=${restaurantId}&timeframe=${timeframe}`, {
      method: 'GET',
      headers: this.getAuthHeaders(),
    })
    
    if (!response.ok) {
      throw new Error('Failed to export analytics report')
    }
    
    return response.blob()
  }

  // Restaurant Photo Management APIs
  async getRestaurantPhotos(restaurantId: string): Promise<{ data: any[] }> {
    return this.request<{ data: any[] }>(`/restaurants/${restaurantId}/photos`)
  }

  async uploadRestaurantPhotos(restaurantId: string, formData: FormData): Promise<{ data: any[] }> {
    return this.request<{ data: any[] }>(`/restaurants/${restaurantId}/photos`, {
      method: 'POST',
      body: formData,
      headers: {
        'Authorization': `Bearer ${this.token}`,
        // Don't set Content-Type for FormData
      },
    })
  }

  async deleteRestaurantPhoto(restaurantId: string, photoId: string): Promise<void> {
    return this.request<void>(`/restaurants/${restaurantId}/photos/${photoId}`, {
      method: 'DELETE',
    })
  }

  async setRestaurantCoverPhoto(restaurantId: string, photoId: string): Promise<void> {
    return this.request<void>(`/restaurants/${restaurantId}/photos/${photoId}/cover`, {
      method: 'PATCH',
    })
  }

  async reorderRestaurantPhotos(restaurantId: string, photoIds: string[]): Promise<void> {
    return this.request<void>(`/restaurants/${restaurantId}/photos/reorder`, {
      method: 'PATCH',
      body: JSON.stringify({ photoIds }),
    })
  }

  async updateRestaurantPhoto(restaurantId: string, photoId: string, data: { 
    category?: string; 
    description?: string; 
  }): Promise<void> {
    return this.request<void>(`/restaurants/${restaurantId}/photos/${photoId}`, {
      method: 'PATCH',
      body: JSON.stringify(data),
    })
  }
}

// Export singleton instance
export const apiService = new ApiService();
export default apiService; 