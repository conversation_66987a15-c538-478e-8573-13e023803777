'use client'

import { useState, useEffect } from 'react'
import { formatPersianNumber, formatPersianPrice, toPersianNumbers } from '@/utils/numbers'

type Language = 'fa' | 'en' | 'ps'

interface Translations {
  home: string
  restaurants: string
  categories: string
  aboutUs: string
  contactUs: string
  login: string
  search: string
  searchRestaurants: string
  location: string
  help: string
  terms: string
  privacy: string
  helpCenter: string
  faq: string
  termsOfService: string
  privacyPolicy: string
}

const translations: Record<Language, Translations> = {
  fa: {
    home: 'خانه',
    restaurants: 'رستوران‌ها',
    categories: 'دسته‌بندی‌ها',
    aboutUs: 'درباره ما',
    contactUs: 'تماس با ما',
    login: 'ورود',
    search: 'جستجو',
    searchRestaurants: 'جستجوی رستوران...',
    location: 'موقعیت',
    help: 'راهنما',
    terms: 'قوانین',
    privacy: 'حریم خصوصی',
    helpCenter: 'مرکز راهنمایی',
    faq: 'سوالات متداول',
    termsOfService: 'قوانین و مقررات',
    privacyPolicy: 'سیاست حریم خصوصی'
  },
  en: {
    home: 'Home',
    restaurants: 'Restaurants',
    categories: 'Categories',
    aboutUs: 'About Us',
    contactUs: 'Contact Us',
    login: 'Login',
    search: 'Search',
    searchRestaurants: 'Search restaurants...',
    location: 'Location',
    help: 'Help',
    terms: 'Terms',
    privacy: 'Privacy',
    helpCenter: 'Help Center',
    faq: 'FAQ',
    termsOfService: 'Terms of Service',
    privacyPolicy: 'Privacy Policy'
  },
  ps: {
    home: 'کور',
    restaurants: 'رستورانونه',
    categories: 'کټګورۍ',
    aboutUs: 'زموږ په اړه',
    contactUs: 'اړیکه',
    login: 'ننوتل',
    search: 'پلټنه',
    searchRestaurants: 'رستورانونه پلټل...',
    location: 'ځای',
    help: 'مرسته',
    terms: 'قوانین',
    privacy: 'شخصي پټوالی',
    helpCenter: 'د مرستې مرکز',
    faq: 'ټولې پوښتنې',
    termsOfService: 'د خدماتو قوانین',
    privacyPolicy: 'د شخصي پټوالۍ پالیسي'
  }
}

export function useTranslations() {
  const [language, setLanguage] = useState<Language>('fa')

  useEffect(() => {
    // Load language from localStorage or set default
    if (typeof window !== 'undefined') {
      const savedLanguage = localStorage.getItem('language') as Language
      if (savedLanguage && translations[savedLanguage]) {
        setLanguage(savedLanguage)
      } else {
        // Set Persian as default
        localStorage.setItem('language', 'fa')
        setLanguage('fa')
      }
    }
  }, [])

  useEffect(() => {
    // Save language to localStorage and update document
    if (typeof window !== 'undefined') {
      localStorage.setItem('language', language)
      
      // Update document direction and language
      document.documentElement.dir = language === 'en' ? 'ltr' : 'rtl'
      document.documentElement.lang = language
    }
  }, [language])

  // Number formatting functions
  const formatNumber = (num: number): string => {
    return formatPersianNumber(num, language)
  }

  const formatPrice = (price: number): string => {
    return formatPersianPrice(price, language)
  }

  const formatString = (str: string | number): string => {
    if (language === 'fa' || language === 'ps') {
      return toPersianNumbers(str)
    }
    return str.toString()
  }

  return {
    t: translations[language],
    language,
    setLanguage,
    formatNumber,
    formatPrice,
    formatString,
    isRTL: language !== 'en'
  }
} 