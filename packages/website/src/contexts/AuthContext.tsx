'use client'

import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { User, AuthResponse, RegisterDto, LoginDto, VerifyOtpDto } from '@/lib/api';
import apiService from '@/lib/api';

interface AuthContextType {
  user: User | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  login: (data: LoginDto) => Promise<{ message: string }>;
  register: (data: RegisterDto) => Promise<{ message: string }>;
  verifyOtp: (data: VerifyOtpDto) => Promise<AuthResponse>;
  logout: () => Promise<void>;
  refreshUser: () => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Check if user is already authenticated
    const checkAuthStatus = async () => {
      try {
        const token = localStorage.getItem('auth_token');
        if (token) {
          // Verify token with backend and get user info
          const userData = await apiService.getCurrentUserFromAPI();
          if (userData) {
            setUser(userData);
            localStorage.setItem('auth_user', JSON.stringify(userData));
          } else {
            // Token is invalid, clear it
            localStorage.removeItem('auth_token');
            localStorage.removeItem('auth_user');
          }
        }
      } catch (error) {
        console.error('Error checking auth status:', error);
        // Clear invalid tokens
        localStorage.removeItem('auth_token');
        localStorage.removeItem('auth_user');
      } finally {
        setIsLoading(false);
      }
    };

    checkAuthStatus();
  }, []);

  const login = async (data: LoginDto): Promise<{ message: string }> => {
    setIsLoading(true);
    try {
      const response = await apiService.login(data);
      return response;
    } catch (error) {
      console.error('Login error:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const register = async (data: RegisterDto): Promise<{ message: string }> => {
    setIsLoading(true);
    try {
      const response = await apiService.register(data);
      return response;
    } catch (error) {
      console.error('Registration error:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const verifyOtp = async (data: VerifyOtpDto): Promise<AuthResponse> => {
    setIsLoading(true);
    try {
      const response = await apiService.verifyOtp(data);
      // Store authentication token and user data
      if (response.accessToken) {
        localStorage.setItem('auth_token', response.accessToken);
      }
      if (response.user) {
        localStorage.setItem('auth_user', JSON.stringify(response.user));
        setUser(response.user);
      }
      return response;
    } catch (error) {
      console.error('OTP verification error:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const logout = async (): Promise<void> => {
    setIsLoading(true);
    try {
      await apiService.logout();
      // Clear all authentication data
      localStorage.removeItem('auth_token');
      localStorage.removeItem('auth_user');
      setUser(null);
    } catch (error) {
      console.error('Logout error:', error);
      // Even if logout fails on backend, clear local state
      localStorage.removeItem('auth_token');
      localStorage.removeItem('auth_user');
      setUser(null);
    } finally {
      setIsLoading(false);
    }
  };

  const refreshUser = async () => {
    try {
      const currentUser = await apiService.getCurrentUserFromAPI();
      if (currentUser) {
        setUser(currentUser);
        localStorage.setItem('auth_user', JSON.stringify(currentUser));
      }
    } catch (error) {
      console.error('Error refreshing user:', error);
      // If refresh fails, clear authentication
      localStorage.removeItem('auth_token');
      localStorage.removeItem('auth_user');
      setUser(null);
    }
  };

  const value: AuthContextType = {
    user,
    isLoading,
    isAuthenticated: !!user,
    login,
    register,
    verifyOtp,
    logout,
    refreshUser,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}; 