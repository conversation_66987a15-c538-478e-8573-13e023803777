'use client'

import { useState } from 'react'
import { XMarkIcon, PhoneIcon, UserIcon, ShieldCheckIcon } from '@heroicons/react/24/outline'
import { useTranslations } from '@/hooks/useTranslations'

interface PreVerificationModalProps {
  isOpen: boolean
  onClose: () => void
  onVerified: (managerName: string, managerPhone: string) => void
}

export default function PreVerificationModal({ isOpen, onClose, onVerified }: PreVerificationModalProps) {
  const { t, language } = useTranslations()
  const [step, setStep] = useState<'info' | 'otp'>('info')
  const [managerName, setManagerName] = useState('')
  const [managerPhone, setManagerPhone] = useState('')
  const [otp, setOtp] = useState('')
  const [errors, setErrors] = useState<{ [key: string]: string }>({})
  const [isLoading, setIsLoading] = useState(false)

  const content = {
    fa: {
      title: 'تأیید هویت مدیر',
      subtitle: 'لطفاً اطلاعات مدیر رستوران را وارد کنید',
      managerName: 'نام کامل مدیر',
      managerPhone: 'شماره تلفن مدیر',
      sendOtp: 'ارسال کد تأیید',
      otpTitle: 'کد تأیید را وارد کنید',
      otpSubtitle: 'کد ۶ رقمی به شماره تلفن شما ارسال شد',
      otpPlaceholder: 'کد ۶ رقمی',
      verify: 'تأیید',
      resendOtp: 'ارسال مجدد کد',
      backToInfo: 'بازگشت',
      continue: 'ادامه ثبت نام',
      requiredField: 'این فیلد اجباری است',
      invalidPhone: 'شماره تلفن معتبر افغانستان را وارد کنید',
      invalidOtp: 'کد تأیید نادرست است',
      phoneValidation: 'مثال: +93 70 123 4567 یا ************',
      otpSent: 'کد تأیید ارسال شد',
      verified: 'تأیید شد! در حال انتقال...'
    },
    en: {
      title: 'Manager Verification',
      subtitle: 'Please enter restaurant manager information',
      managerName: 'Manager Full Name',
      managerPhone: 'Manager Phone Number',
      sendOtp: 'Send Verification Code',
      otpTitle: 'Enter Verification Code',
      otpSubtitle: '6-digit code sent to your phone number',
      otpPlaceholder: '6-digit code',
      verify: 'Verify',
      resendOtp: 'Resend Code',
      backToInfo: 'Back',
      continue: 'Continue Registration',
      requiredField: 'This field is required',
      invalidPhone: 'Enter valid Afghanistan phone number',
      invalidOtp: 'Invalid verification code',
      phoneValidation: 'Example: +93 70 123 4567 or ************',
      otpSent: 'Verification code sent',
      verified: 'Verified! Redirecting...'
    },
    ps: {
      title: 'د مدیر تصدیق',
      subtitle: 'د رستوران د مدیر معلومات ولیکئ',
      managerName: 'د مدیر بشپړ نوم',
      managerPhone: 'د مدیر د تلیفون شمیره',
      sendOtp: 'د تصدیق کوډ واستوئ',
      otpTitle: 'د تصدیق کوډ ولیکئ',
      otpSubtitle: '۶ ګڼي کوډ ستاسو ته واستول شو',
      otpPlaceholder: '۶ ګڼي کوډ',
      verify: 'تصدیق',
      resendOtp: 'بیا واستوئ',
      backToInfo: 'بیرته',
      continue: 'ثبت نوم ته دوام',
      requiredField: 'دا ځای اړین دی',
      invalidPhone: 'د افغانستان سمه شمیره ولیکئ',
      invalidOtp: 'د تصدیق کوډ سم نه دی',
      phoneValidation: 'بېلګه: +93 70 123 4567 یا ************',
      otpSent: 'د تصدیق کوډ واستول شو',
      verified: 'تصدیق شو! انتقال کېږي...'
    }
  }

  const currentContent = content[language as keyof typeof content] || content.fa || content.en || {
    title: 'Manager Verification',
    subtitle: 'Please enter restaurant manager information',
    managerName: 'Manager Full Name',
    managerPhone: 'Manager Phone Number',
    sendOtp: 'Send Verification Code',
    otpTitle: 'Enter Verification Code',
    otpSubtitle: '6-digit code sent to your phone number',
    otpPlaceholder: '6-digit code',
    verify: 'Verify',
    resendOtp: 'Resend Code',
    backToInfo: 'Back',
    continue: 'Continue Registration',
    requiredField: 'This field is required',
    invalidPhone: 'Enter valid Afghanistan phone number',
    invalidOtp: 'Invalid verification code',
    phoneValidation: 'Example: +93 70 123 4567 or ************',
    otpSent: 'Verification code sent',
    verified: 'Verified! Redirecting...'
  }

  // Afghanistan phone validation
  const validateAfghanPhone = (phone: string): boolean => {
    const cleanPhone = phone.replace(/[\s\-\(\)]/g, '')
    const afghanPhoneRegex = /^(\+93|0093|93)?(0?[7][0-9])\d{7}$/
    return afghanPhoneRegex.test(cleanPhone)
  }

  const handleSendOtp = async () => {
    const newErrors: { [key: string]: string } = {}

    // Validate manager name
    if (!managerName.trim()) {
      newErrors.managerName = currentContent.requiredField
    }

    // Validate phone number
    if (!managerPhone.trim()) {
      newErrors.managerPhone = currentContent.requiredField
    } else if (!validateAfghanPhone(managerPhone)) {
      newErrors.managerPhone = currentContent.invalidPhone
    }

    setErrors(newErrors)

    if (Object.keys(newErrors).length === 0) {
      setIsLoading(true)
      // Simulate sending OTP
      setTimeout(() => {
        setIsLoading(false)
        setStep('otp')
        // In real implementation, this would trigger SMS sending
        console.log('OTP sent to:', managerPhone)
      }, 1500)
    }
  }

  const handleVerifyOtp = async () => {
    if (!otp.trim()) {
      setErrors({ otp: currentContent.requiredField })
      return
    }

    // Check if OTP is 123456 (default)
    if (otp !== '123456') {
      setErrors({ otp: currentContent.invalidOtp })
      return
    }

    setIsLoading(true)
    // Simulate verification
    setTimeout(() => {
      setIsLoading(false)
      setErrors({})
      
      // Success - pass verified data back to parent
      onVerified(managerName, managerPhone)
      
      // Reset modal state
      setStep('info')
      setManagerName('')
      setManagerPhone('')
      setOtp('')
      onClose()
    }, 1000)
  }

  const handleResendOtp = () => {
    setOtp('')
    setErrors({})
    handleSendOtp()
  }

  const handleBackToInfo = () => {
    setStep('info')
    setOtp('')
    setErrors({})
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-800 rounded-lg max-w-md w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <div>
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
              {currentContent.title}
            </h2>
            <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
              {step === 'info' ? currentContent.subtitle : currentContent.otpSubtitle}
            </p>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
          >
            <XMarkIcon className="w-6 h-6" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          {step === 'info' ? (
            /* Step 1: Manager Information */
            <div className="space-y-6">
              {/* Manager Name */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  {currentContent.managerName} *
                </label>
                <div className="relative">
                  <UserIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                  <input
                    type="text"
                    value={managerName}
                    onChange={(e) => setManagerName(e.target.value)}
                    className={`w-full pl-10 pr-4 py-3 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent ${
                      errors.managerName ? 'border-red-500' : 'border-gray-300'
                    } bg-white dark:bg-gray-700 text-gray-900 dark:text-white`}
                    placeholder={language === 'en' ? 'Enter manager name' : language === 'ps' ? 'د مدیر نوم ولیکئ' : 'نام مدیر را وارد کنید'}
                  />
                </div>
                {errors.managerName && (
                  <p className="text-red-500 text-sm mt-1">{errors.managerName}</p>
                )}
              </div>

              {/* Manager Phone */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  {currentContent.managerPhone} *
                </label>
                <div className="relative">
                  <PhoneIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                  <input
                    type="tel"
                    value={managerPhone}
                    onChange={(e) => setManagerPhone(e.target.value)}
                    className={`w-full pl-10 pr-4 py-3 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent ${
                      errors.managerPhone ? 'border-red-500' : 'border-gray-300'
                    } bg-white dark:bg-gray-700 text-gray-900 dark:text-white`}
                    placeholder={language === 'en' ? '+93 70 123 4567' : '+93 70 123 4567'}
                  />
                </div>
                <p className="text-xs text-gray-500 mt-1">{currentContent.phoneValidation}</p>
                {errors.managerPhone && (
                  <p className="text-red-500 text-sm mt-1">{errors.managerPhone}</p>
                )}
              </div>

              {/* Send OTP Button */}
              <button
                onClick={handleSendOtp}
                disabled={isLoading}
                className="w-full bg-primary-600 hover:bg-primary-700 disabled:bg-gray-400 text-white font-medium py-3 px-4 rounded-lg transition-colors flex items-center justify-center gap-2"
              >
                {isLoading ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
                    {language === 'en' ? 'Sending...' : language === 'ps' ? 'استېږي...' : 'در حال ارسال...'}
                  </>
                ) : (
                  <>
                    <ShieldCheckIcon className="w-5 h-5" />
                    {currentContent.sendOtp}
                  </>
                )}
              </button>
            </div>
          ) : (
            /* Step 2: OTP Verification */
            <div className="space-y-6">
              {/* OTP Title */}
              <div className="text-center">
                <ShieldCheckIcon className="w-12 h-12 text-primary-600 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                  {currentContent.otpTitle}
                </h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {currentContent.otpSubtitle}
                </p>
                <p className="text-sm font-medium text-primary-600 mt-1">
                  {managerPhone}
                </p>
              </div>

              {/* OTP Input */}
              <div>
                <input
                  type="text"
                  value={otp}
                  onChange={(e) => {
                    const value = e.target.value.replace(/\D/g, '').slice(0, 6)
                    setOtp(value)
                  }}
                  className={`w-full px-4 py-4 text-center text-2xl font-mono border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent ${
                    errors.otp ? 'border-red-500' : 'border-gray-300'
                  } bg-white dark:bg-gray-700 text-gray-900 dark:text-white tracking-widest`}
                  placeholder={language === 'en' ? '123456' : '۱۲۳۴۵۶'}
                  maxLength={6}
                />
                {errors.otp && (
                  <p className="text-red-500 text-sm mt-1 text-center">{errors.otp}</p>
                )}
                
                {/* Default OTP hint for development */}
                <p className="text-xs text-gray-500 text-center mt-2">
                  {language === 'en' ? 'Development: Use 123456' : language === 'ps' ? 'پرمختیا: ۱۲۳۴۵۶ وکاروئ' : 'توسعه: از ۱۲۳۴۵۶ استفاده کنید'}
                </p>
              </div>

              {/* Action Buttons */}
              <div className="space-y-3">
                <button
                  onClick={handleVerifyOtp}
                  disabled={isLoading || otp.length !== 6}
                  className="w-full bg-primary-600 hover:bg-primary-700 disabled:bg-gray-400 text-white font-medium py-3 px-4 rounded-lg transition-colors flex items-center justify-center gap-2"
                >
                  {isLoading ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
                      {language === 'en' ? 'Verifying...' : language === 'ps' ? 'تصدیق کېږي...' : 'در حال تأیید...'}
                    </>
                  ) : (
                    <>
                      <ShieldCheckIcon className="w-5 h-5" />
                      {currentContent.verify}
                    </>
                  )}
                </button>

                <div className="flex gap-3">
                  <button
                    onClick={handleBackToInfo}
                    className="flex-1 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 font-medium py-3 px-4 rounded-lg transition-colors"
                  >
                    {currentContent.backToInfo}
                  </button>
                  <button
                    onClick={handleResendOtp}
                    disabled={isLoading}
                    className="flex-1 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 font-medium py-3 px-4 rounded-lg transition-colors"
                  >
                    {currentContent.resendOtp}
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
} 