'use client'

import React, { useState, useCallback } from 'react'
import { useDropzone } from 'react-dropzone'
import { 
  PhotoIcon, 
  XMarkIcon, 
  ArrowUpTrayIcon,
  EyeIcon,
  StarIcon,
  TrashIcon,
  ArrowsUpDownIcon
} from '@heroicons/react/24/outline'
import { StarIcon as StarSolidIcon } from '@heroicons/react/24/solid'

// Types
export type PhotoCategory = 'cover' | 'gallery' | 'menu' | 'interior' | 'exterior'

export interface PhotoFile {
  id: string
  file: File
  preview: string
  category: PhotoCategory
  isCover?: boolean
  uploading?: boolean
  progress?: number
  error?: string
}

export interface UploadedPhoto {
  id: string
  url: string
  category: PhotoCategory
  isCover?: boolean
  description?: string
  order?: number
}

interface PhotoUploaderProps {
  photos: UploadedPhoto[]
  onPhotosChange: (photos: PhotoFile[]) => void
  onPhotoUpload: (photos: PhotoFile[]) => Promise<void>
  onPhotoDelete: (photoId: string) => Promise<void>
  onSetCover: (photoId: string) => Promise<void>
  onReorderPhotos: (photoIds: string[]) => Promise<void>
  maxPhotos?: number
  maxFileSize?: number // in MB
  allowedTypes?: string[]
  categories?: { value: PhotoCategory; label: string; icon: string }[]
}

const defaultCategories = [
  { value: 'cover' as PhotoCategory, label: 'تصویر کاور', icon: '🖼️' },
  { value: 'gallery' as PhotoCategory, label: 'گالری', icon: '📸' },
  { value: 'menu' as PhotoCategory, label: 'منو', icon: '🍽️' },
  { value: 'interior' as PhotoCategory, label: 'فضای داخلی', icon: '🏠' },
  { value: 'exterior' as PhotoCategory, label: 'نمای بیرونی', icon: '🏢' }
]

export default function PhotoUploader({
  photos = [],
  onPhotosChange,
  onPhotoUpload,
  onPhotoDelete,
  onSetCover,
  onReorderPhotos,
  maxPhotos = 20,
  maxFileSize = 5,
  allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'],
  categories = defaultCategories
}: PhotoUploaderProps) {
  const [selectedFiles, setSelectedFiles] = useState<PhotoFile[]>([])
  const [selectedCategory, setSelectedCategory] = useState<PhotoCategory>('gallery')
  const [isUploading, setIsUploading] = useState(false)
  const [previewPhoto, setPreviewPhoto] = useState<string | null>(null)

  // File validation is now handled by react-dropzone

  // Handle file selection with react-dropzone
  const onDrop = useCallback(async (acceptedFiles: File[], rejectedFiles: any[]) => {
    const newPhotos: PhotoFile[] = []

    // Handle accepted files
    for (const file of acceptedFiles) {
      if (selectedFiles.length + newPhotos.length < maxPhotos) {
        const id = `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
        const preview = URL.createObjectURL(file)
        
        newPhotos.push({
          id,
          file,
          preview,
          category: selectedCategory,
          uploading: false,
          progress: 0,
          error: undefined
        })
      }
    }

    // Handle rejected files
    rejectedFiles.forEach((rejection) => {
      const file = rejection.file
      const errors = rejection.errors.map((e: any) => e.message).join(', ')
      const id = `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
      
      // Create preview even for rejected files
      let preview = ''
      try {
        preview = URL.createObjectURL(file)
      } catch (error) {
        console.error('Error creating preview:', error)
      }
      
      newPhotos.push({
        id,
        file,
        preview,
        category: selectedCategory,
        uploading: false,
        progress: 0,
        error: errors || 'فایل نامعتبر است'
      })
    })

    const updatedFiles = [...selectedFiles, ...newPhotos]
    setSelectedFiles(updatedFiles)
    onPhotosChange(updatedFiles)
  }, [selectedFiles, selectedCategory, maxPhotos, onPhotosChange])

  // Configure dropzone
  const {
    getRootProps,
    getInputProps,
    isDragActive,
    isDragAccept,
    isDragReject
  } = useDropzone({
    onDrop,
    accept: {
      'image/*': allowedTypes.map(type => type.replace('image/', '.'))
    },
    maxFiles: maxPhotos,
    maxSize: maxFileSize * 1024 * 1024,
    multiple: true,
    disabled: isUploading
  })

  // Remove photo
  const removePhoto = useCallback((photoId: string) => {
    setSelectedFiles(prev => {
      const updated = prev.filter(photo => {
        if (photo.id === photoId) {
          URL.revokeObjectURL(photo.preview)
          return false
        }
        return true
      })
      onPhotosChange(updated)
      return updated
    })
  }, [onPhotosChange])

  // Upload photos
  const uploadPhotos = useCallback(async () => {
    if (selectedFiles.length === 0) return

    setIsUploading(true)
    try {
      await onPhotoUpload(selectedFiles)
      // Clear selected files after successful upload
      selectedFiles.forEach(photo => URL.revokeObjectURL(photo.preview))
      setSelectedFiles([])
      onPhotosChange([])
    } catch (error) {
      console.error('Upload failed:', error)
    } finally {
      setIsUploading(false)
    }
  }, [selectedFiles, onPhotoUpload, onPhotosChange])

  // Get category label
  const getCategoryLabel = (category: PhotoCategory) => {
    return categories.find(cat => cat.value === category)?.label || category
  }

  // Get category icon
  const getCategoryIcon = (category: PhotoCategory) => {
    return categories.find(cat => cat.value === category)?.icon || '📸'
  }

  return (
    <div className="space-y-6">
      {/* Category Selection */}
      <div className="flex flex-wrap gap-2">
        {categories.map((category) => (
          <button
            key={category.value}
            onClick={() => setSelectedCategory(category.value)}
            className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
              selectedCategory === category.value
                ? 'bg-primary-500 text-white'
                : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
            }`}
          >
            <span className="mr-2">{category.icon}</span>
            {category.label}
          </button>
        ))}
      </div>

      {/* Upload Area */}
      <div
        {...getRootProps()}
        className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors cursor-pointer ${
          isDragActive
            ? isDragAccept
              ? 'border-green-500 bg-green-50 dark:bg-green-900/20'
              : isDragReject
              ? 'border-red-500 bg-red-50 dark:bg-red-900/20'
              : 'border-primary-500 bg-primary-50 dark:bg-primary-900/20'
            : 'border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500'
        } ${isUploading ? 'opacity-50 cursor-not-allowed' : ''}`}
      >
        <input {...getInputProps()} />
        <PhotoIcon className="mx-auto h-12 w-12 text-gray-400" />
        <div className="mt-4">
          <p className="text-lg font-medium text-gray-900 dark:text-white">
            {isDragActive
              ? isDragAccept
                ? 'تصاویر را رها کنید...'
                : isDragReject
                ? 'فایل‌های غیرمجاز!'
                : 'تصاویر را رها کنید...'
              : 'تصاویر خود را اینجا بکشید یا کلیک کنید'
            }
          </p>
          <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
            حداکثر {maxPhotos} تصویر، هر کدام تا {maxFileSize} مگابایت
          </p>
          <p className="mt-1 text-xs text-gray-400">
            فرمت‌های پشتیبانی شده: JPEG، PNG، WebP
          </p>
        </div>
        
        {!isDragActive && (
          <button
            type="button"
            className="mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"
            disabled={isUploading}
          >
            <ArrowUpTrayIcon className="mr-2 h-4 w-4" />
            انتخاب فایل
          </button>
        )}
      </div>

      {/* Selected Photos Preview */}
      {selectedFiles.length > 0 && (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">
              تصاویر انتخاب شده ({selectedFiles.length})
            </h3>
            <button
              onClick={uploadPhotos}
              disabled={isUploading}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50"
            >
              {isUploading ? 'در حال آپلود...' : 'آپلود تصاویر'}
            </button>
          </div>

          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4">
            {selectedFiles.map((photo) => (
              <div key={photo.id} className="relative group">
                <div className="aspect-square rounded-lg overflow-hidden bg-gray-100 dark:bg-gray-800">
                  <img
                    src={photo.preview}
                    alt="Preview"
                    className="w-full h-full object-cover"
                  />
                </div>
                
                {/* Category Badge */}
                <div className="absolute top-2 left-2 bg-black/70 text-white text-xs px-2 py-1 rounded">
                  {getCategoryIcon(photo.category)} {getCategoryLabel(photo.category)}
                </div>
                
                {/* Action Buttons */}
                <div className="absolute top-2 right-2 flex gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                  <button
                    onClick={() => setPreviewPhoto(photo.preview)}
                    className="p-1 bg-black/70 text-white rounded hover:bg-black/90"
                  >
                    <EyeIcon className="h-4 w-4" />
                  </button>
                  <button
                    onClick={() => removePhoto(photo.id)}
                    className="p-1 bg-red-500 text-white rounded hover:bg-red-600"
                  >
                    <XMarkIcon className="h-4 w-4" />
                  </button>
                </div>

                {/* Upload Progress */}
                {photo.uploading && (
                  <div className="absolute inset-0 bg-black/50 flex items-center justify-center">
                    <div className="text-white text-sm">
                      {photo.progress}%
                    </div>
                  </div>
                )}

                {/* Error */}
                {photo.error && (
                  <div className="absolute inset-0 bg-red-500/80 flex items-center justify-center p-2">
                    <div className="text-white text-xs text-center">
                      {photo.error}
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Existing Photos */}
      {photos.length > 0 && (
        <div className="space-y-4">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            تصاویر موجود ({photos.length})
          </h3>
          
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4">
            {photos.map((photo) => (
              <div key={photo.id} className="relative group">
                <div className="aspect-square rounded-lg overflow-hidden bg-gray-100 dark:bg-gray-800">
                  <img
                    src={photo.url}
                    alt="Restaurant"
                    className="w-full h-full object-cover"
                  />
                </div>
                
                {/* Cover Badge */}
                {photo.isCover && (
                  <div className="absolute top-2 left-2 bg-yellow-500 text-white text-xs px-2 py-1 rounded flex items-center">
                    <StarSolidIcon className="h-3 w-3 mr-1" />
                    کاور
                  </div>
                )}
                
                {/* Category Badge */}
                <div className="absolute bottom-2 left-2 bg-black/70 text-white text-xs px-2 py-1 rounded">
                  {getCategoryIcon(photo.category)} {getCategoryLabel(photo.category)}
                </div>
                
                {/* Action Buttons */}
                <div className="absolute top-2 right-2 flex gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                  <button
                    onClick={() => setPreviewPhoto(photo.url)}
                    className="p-1 bg-black/70 text-white rounded hover:bg-black/90"
                  >
                    <EyeIcon className="h-4 w-4" />
                  </button>
                  {!photo.isCover && (
                    <button
                      onClick={() => onSetCover(photo.id)}
                      className="p-1 bg-yellow-500 text-white rounded hover:bg-yellow-600"
                    >
                      <StarIcon className="h-4 w-4" />
                    </button>
                  )}
                  <button
                    onClick={() => onPhotoDelete(photo.id)}
                    className="p-1 bg-red-500 text-white rounded hover:bg-red-600"
                  >
                    <TrashIcon className="h-4 w-4" />
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Photo Preview Modal */}
      {previewPhoto && (
        <div className="fixed inset-0 bg-black/80 flex items-center justify-center z-50 p-4">
          <div className="relative max-w-4xl max-h-full">
            <img
              src={previewPhoto}
              alt="Preview"
              className="max-w-full max-h-full object-contain"
            />
            <button
              onClick={() => setPreviewPhoto(null)}
              className="absolute top-4 right-4 p-2 bg-black/70 text-white rounded-full hover:bg-black/90"
            >
              <XMarkIcon className="h-6 w-6" />
            </button>
          </div>
        </div>
      )}
    </div>
  )
} 