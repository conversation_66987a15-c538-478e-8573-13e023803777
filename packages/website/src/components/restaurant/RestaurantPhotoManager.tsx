'use client'

import React, { useState, useEffect } from 'react'
import { 
  PhotoIcon,
  PlusIcon,
  Cog6ToothIcon,
  EyeIcon,
  ArrowsUpDownIcon,
  FolderIcon
} from '@heroicons/react/24/outline'
import PhotoUploader, { PhotoFile, UploadedPhoto, PhotoCategory } from './PhotoUploader'
import apiService from '@/lib/api'

interface RestaurantPhotoManagerProps {
  restaurantId: string
  onPhotosUpdate?: () => void
}

interface PhotoStats {
  total: number
  byCategory: Record<PhotoCategory, number>
  hasCover: boolean
}

export default function RestaurantPhotoManager({ 
  restaurantId, 
  onPhotosUpdate 
}: RestaurantPhotoManagerProps) {
  const [photos, setPhotos] = useState<UploadedPhoto[]>([])
  const [loading, setLoading] = useState(true)
  const [stats, setStats] = useState<PhotoStats>({
    total: 0,
    byCategory: {
      cover: 0,
      gallery: 0,
      menu: 0,
      interior: 0,
      exterior: 0
    },
    hasCover: false
  })
  const [activeView, setActiveView] = useState<'upload' | 'manage'>('upload')
  const [selectedCategory, setSelectedCategory] = useState<PhotoCategory | 'all'>('all')

  // Load photos
  useEffect(() => {
    loadPhotos()
  }, [restaurantId])

  const loadPhotos = async () => {
    try {
      setLoading(true)
      const response = await apiService.getRestaurantPhotos(restaurantId)
      setPhotos(response.data || [])
      calculateStats(response.data || [])
    } catch (error) {
      console.error('Error loading photos:', error)
    } finally {
      setLoading(false)
    }
  }

  const calculateStats = (photoList: UploadedPhoto[]) => {
    const newStats: PhotoStats = {
      total: photoList.length,
      byCategory: {
        cover: 0,
        gallery: 0,
        menu: 0,
        interior: 0,
        exterior: 0
      },
      hasCover: false
    }

    photoList.forEach(photo => {
      newStats.byCategory[photo.category]++
      if (photo.isCover) {
        newStats.hasCover = true
      }
    })

    setStats(newStats)
  }

  // Upload photos
  const handlePhotoUpload = async (photoFiles: PhotoFile[]) => {
    const formData = new FormData()
    
    photoFiles.forEach((photo, index) => {
      formData.append('photos', photo.file)
      formData.append(`categories[${index}]`, photo.category)
    })

    try {
      const response = await apiService.uploadRestaurantPhotos(restaurantId, formData)
      await loadPhotos() // Reload photos
      onPhotosUpdate?.()
      return response
    } catch (error) {
      console.error('Upload failed:', error)
      throw error
    }
  }

  // Delete photo
  const handlePhotoDelete = async (photoId: string) => {
    try {
      await apiService.deleteRestaurantPhoto(restaurantId, photoId)
      await loadPhotos()
      onPhotosUpdate?.()
    } catch (error) {
      console.error('Delete failed:', error)
      throw error
    }
  }

  // Set cover photo
  const handleSetCover = async (photoId: string) => {
    try {
      await apiService.setRestaurantCoverPhoto(restaurantId, photoId)
      await loadPhotos()
      onPhotosUpdate?.()
    } catch (error) {
      console.error('Set cover failed:', error)
      throw error
    }
  }

  // Reorder photos
  const handleReorderPhotos = async (photoIds: string[]) => {
    try {
      await apiService.reorderRestaurantPhotos(restaurantId, photoIds)
      await loadPhotos()
      onPhotosUpdate?.()
    } catch (error) {
      console.error('Reorder failed:', error)
      throw error
    }
  }

  // Filter photos by category
  const filteredPhotos = selectedCategory === 'all' 
    ? photos 
    : photos.filter(photo => photo.category === selectedCategory)

  const categories = [
    { value: 'all' as const, label: 'همه تصاویر', icon: '📸', count: stats.total },
    { value: 'cover' as PhotoCategory, label: 'تصویر کاور', icon: '🖼️', count: stats.byCategory.cover },
    { value: 'gallery' as PhotoCategory, label: 'گالری', icon: '📸', count: stats.byCategory.gallery },
    { value: 'menu' as PhotoCategory, label: 'منو', icon: '🍽️', count: stats.byCategory.menu },
    { value: 'interior' as PhotoCategory, label: 'فضای داخلی', icon: '🏠', count: stats.byCategory.interior },
    { value: 'exterior' as PhotoCategory, label: 'نمای بیرونی', icon: '🏢', count: stats.byCategory.exterior }
  ]

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-1/3 mb-4"></div>
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4">
            {[...Array(8)].map((_, i) => (
              <div key={i} className="aspect-square bg-gray-200 dark:bg-gray-700 rounded-lg"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
            مدیریت تصاویر رستوران
          </h2>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            {stats.total} تصویر • {stats.hasCover ? 'دارای کاور' : 'بدون کاور'}
          </p>
        </div>
        
        <div className="flex gap-2">
          <button
            onClick={() => setActiveView('upload')}
            className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
              activeView === 'upload'
                ? 'bg-primary-500 text-white'
                : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300'
            }`}
          >
            <PlusIcon className="h-4 w-4 inline mr-2" />
            آپلود تصاویر
          </button>
          <button
            onClick={() => setActiveView('manage')}
            className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
              activeView === 'manage'
                ? 'bg-primary-500 text-white'
                : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300'
            }`}
          >
            <Cog6ToothIcon className="h-4 w-4 inline mr-2" />
            مدیریت تصاویر
          </button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-6 gap-4">
        {categories.map((category) => (
          <div
            key={category.value}
            onClick={() => setSelectedCategory(category.value)}
            className={`p-4 rounded-lg cursor-pointer transition-colors ${
              selectedCategory === category.value
                ? 'bg-primary-50 dark:bg-primary-900/20 border-2 border-primary-500'
                : 'bg-white dark:bg-gray-800 border-2 border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
            }`}
          >
            <div className="text-center">
              <div className="text-2xl mb-1">{category.icon}</div>
              <div className="text-2xl font-bold text-gray-900 dark:text-white">
                {category.count}
              </div>
              <div className="text-xs text-gray-500 dark:text-gray-400">
                {category.label}
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Warning if no cover photo */}
      {!stats.hasCover && stats.total > 0 && (
        <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
          <div className="flex items-center">
            <PhotoIcon className="h-5 w-5 text-yellow-600 dark:text-yellow-400 mr-2" />
            <div>
              <p className="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                تصویر کاور انتخاب نشده
              </p>
              <p className="text-xs text-yellow-700 dark:text-yellow-300">
                یکی از تصاویر را به عنوان کاور رستوران انتخاب کنید
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Content */}
      {activeView === 'upload' ? (
        <PhotoUploader
          photos={photos}
          onPhotosChange={() => {}} // Handle in upload
          onPhotoUpload={handlePhotoUpload}
          onPhotoDelete={handlePhotoDelete}
          onSetCover={handleSetCover}
          onReorderPhotos={handleReorderPhotos}
        />
      ) : (
        <div className="space-y-4">
          {/* Filter Bar */}
          <div className="flex flex-wrap gap-2">
            {categories.map((category) => (
              <button
                key={category.value}
                onClick={() => setSelectedCategory(category.value)}
                className={`px-3 py-1 rounded-full text-sm font-medium transition-colors ${
                  selectedCategory === category.value
                    ? 'bg-primary-500 text-white'
                    : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
                }`}
              >
                {category.icon} {category.label} ({category.count})
              </button>
            ))}
          </div>

          {/* Photo Grid */}
          {filteredPhotos.length === 0 ? (
            <div className="text-center py-12">
              <FolderIcon className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">
                تصویری یافت نشد
              </h3>
              <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                {selectedCategory === 'all' 
                  ? 'هنوز تصویری آپلود نکرده‌اید'
                  : `تصویری در دسته ${categories.find(c => c.value === selectedCategory)?.label} وجود ندارد`
                }
              </p>
              <div className="mt-6">
                <button
                  onClick={() => setActiveView('upload')}
                  className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700"
                >
                  <PlusIcon className="mr-2 h-4 w-4" />
                  آپلود تصویر جدید
                </button>
              </div>
            </div>
          ) : (
            <PhotoUploader
              photos={filteredPhotos}
              onPhotosChange={() => {}} // Not used in manage mode
              onPhotoUpload={handlePhotoUpload}
              onPhotoDelete={handlePhotoDelete}
              onSetCover={handleSetCover}
              onReorderPhotos={handleReorderPhotos}
            />
          )}
        </div>
      )}
    </div>
  )
} 