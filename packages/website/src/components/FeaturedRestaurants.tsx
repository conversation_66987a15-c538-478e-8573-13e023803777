'use client'

import { useState, useEffect } from 'react'
import { StarIcon, MapPinIcon, ClockIcon } from '@heroicons/react/24/solid'
import Image from 'next/image'
import Link from 'next/link'

// Temporary interface until API is fully connected
interface Restaurant {
  id: string
  name: string
  description?: string
  address: string
  city: string
  state: string
  phone: string
  email?: string
  website?: string
  hours_of_operation?: any
  rating?: number
  review_count?: number
  photos?: string[]
  is_featured?: boolean
  cuisine_type?: string
}

export default function FeaturedRestaurants() {
  const [restaurants, setRestaurants] = useState<Restaurant[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchFeaturedRestaurants = async () => {
      try {
        setLoading(true)
        // Simulate API call - replace with real API when ready
        await new Promise(resolve => setTimeout(resolve, 1000))
        
        // Mock data for now
        const mockRestaurants: Restaurant[] = [
          {
            id: '1',
            name: 'رستوران کابلی',
            description: 'بهترین غذاهای افغانی',
            address: 'خیابان اصلی، کابل',
            city: 'کابل',
            state: 'کابل',
            phone: '+93 70 123 4567',
            rating: 4.5,
            review_count: 120,
            is_featured: true,
            cuisine_type: 'آشپزی افغانی'
          },
          {
            id: '2',
            name: 'رستوران هرات',
            description: 'طعم اصیل هرات',
            address: 'مرکز شهر، هرات',
            city: 'هرات',
            state: 'هرات',
            phone: '+93 70 234 5678',
            rating: 4.2,
            review_count: 85,
            is_featured: true,
            cuisine_type: 'آشپزی افغانی'
          },
          {
            id: '3',
            name: 'رستوران مزار',
            description: 'غذاهای محلی مزار شریف',
            address: 'بازار مرکزی، مزار شریف',
            city: 'مزار شریف',
            state: 'بلخ',
            phone: '+93 70 345 6789',
            rating: 4.3,
            review_count: 95,
            is_featured: true,
            cuisine_type: 'آشپزی افغانی'
          },
          {
            id: '4',
            name: 'رستوران قندهار',
            description: 'طعم اصیل جنوب افغانستان',
            address: 'مرکز شهر، قندهار',
            city: 'قندهار',
            state: 'قندهار',
            phone: '+93 70 456 7890',
            rating: 4.1,
            review_count: 75,
            is_featured: true,
            cuisine_type: 'آشپزی افغانی'
          }
        ]
        
        setRestaurants(mockRestaurants)
      } catch (err) {
        console.error('Error fetching featured restaurants:', err)
        setError('خطا در بارگذاری رستوران‌ها')
        setRestaurants([])
      } finally {
        setLoading(false)
      }
    }

    fetchFeaturedRestaurants()
  }, [])

  if (loading) {
    return (
      <section className="py-16 bg-white dark:bg-gray-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
              رستوران‌های برتر
            </h2>
            <p className="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
              بهترین و محبوب‌ترین رستوران‌های افغانستان را کشف کنید
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {[...Array(4)].map((_, index) => (
              <div key={index} className="bg-white dark:bg-gray-900 rounded-2xl shadow-lg overflow-hidden border border-gray-100 dark:border-gray-700 animate-pulse">
                <div className="w-full h-48 bg-gray-200 dark:bg-gray-700"></div>
                <div className="p-6 space-y-3">
                  <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4"></div>
                  <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
                  <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-2/3"></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>
    )
  }

  return (
    <section className="py-16 bg-white dark:bg-gray-800">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
            رستوران‌های برتر
          </h2>
          <p className="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
            بهترین و محبوب‌ترین رستوران‌های افغانستان را کشف کنید
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {restaurants && restaurants.length > 0 ? restaurants.map((restaurant) => (
            <div 
              key={restaurant.id}
              className="group bg-white dark:bg-gray-900 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden border border-gray-100 dark:border-gray-700"
            >
              <div className="relative">
                <div className="w-full h-48 bg-gray-200 dark:bg-gray-700 relative overflow-hidden">
                  {restaurant.photos && restaurant.photos.length > 0 ? (
                    <Image
                      src={restaurant.photos[0]}
                      alt={restaurant.name}
                      fill
                      className="object-cover group-hover:scale-105 transition-transform duration-300"
                    />
                  ) : (
                    <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-800">
                      <div className="text-gray-400 text-4xl">🍽️</div>
                    </div>
                  )}
                </div>
                
                {restaurant.is_featured && (
                  <div className="absolute top-3 right-3 bg-yellow-400 text-yellow-900 px-2 py-1 rounded-full text-xs font-semibold">
                    ویژه
                  </div>
                )}
                
                <div className="absolute bottom-3 left-3 bg-white dark:bg-gray-900 px-2 py-1 rounded-full text-xs font-semibold text-gray-900 dark:text-white shadow-lg">
                  {restaurant.cuisine_type || 'آشپزی افغانی'}
                </div>
              </div>
              
              <div className="p-6">
                <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2 group-hover:text-primary-600 transition-colors">
                  {restaurant.name}
                </h3>
                
                <div className="flex items-center gap-2 mb-3">
                  <MapPinIcon className="w-4 h-4 text-gray-400" />
                  <span className="text-sm text-gray-600 dark:text-gray-400 truncate">
                    {restaurant.address}
                  </span>
                </div>
                
                <div className="flex items-center gap-2 mb-3">
                  <div className="flex items-center">
                    {[...Array(5)].map((_, i) => (
                      <StarIcon
                        key={i}
                        className={`w-4 h-4 ${
                          i < Math.floor(restaurant.rating || 0)
                            ? 'text-yellow-400'
                            : 'text-gray-300 dark:text-gray-600'
                        }`}
                      />
                    ))}
                  </div>
                  <span className="text-sm text-gray-600 dark:text-gray-400">
                    ({restaurant.rating || 0}) • {restaurant.review_count || 0} نظر
                  </span>
                </div>
                
                <div className="flex items-center gap-2 mb-4">
                  <ClockIcon className="w-4 h-4 text-gray-400" />
                  <span className="text-sm text-gray-600 dark:text-gray-400">
                    {restaurant.hours_of_operation ? 'باز' : 'ساعات کار نامشخص'}
                  </span>
                </div>
                
                <Link 
                  href={`/restaurants/${restaurant.id}`}
                  className="block w-full bg-primary-50 hover:bg-primary-100 text-primary-600 font-semibold py-2 px-4 rounded-lg transition-colors duration-200 text-center"
                >
                  مشاهده جزئیات
                </Link>
              </div>
            </div>
          )) : (
            <div className="col-span-full text-center py-12">
              <div className="text-gray-400 text-6xl mb-4">🍽️</div>
              <p className="text-gray-600 dark:text-gray-400 text-lg mb-2">
                {error || 'هیچ رستورانی یافت نشد'}
              </p>
              <p className="text-gray-500 dark:text-gray-500 text-sm">
                لطفاً بعداً دوباره تلاش کنید
              </p>
            </div>
          )}
        </div>
        
        <div className="text-center mt-12">
          <Link 
            href="/restaurants"
            className="inline-flex items-center px-8 py-3 bg-primary-500 hover:bg-primary-600 text-white font-semibold rounded-lg transition-colors duration-200"
          >
            مشاهده همه رستوران‌ها
            <svg className="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
            </svg>
          </Link>
        </div>
      </div>
    </section>
  )
} 