import { 
  MapPinIcon, 
  PhoneIcon, 
  EnvelopeIcon,
  HeartIcon
} from '@heroicons/react/24/outline'

export default function Footer() {
  const footerLinks = {
    company: [
      { name: 'درباره ما', href: '/about' },
      { name: 'تماس با ما', href: '/contact' },
      { name: 'وبلاگ', href: '/blog' },
      { name: 'مشاغل', href: '/careers' },
    ],
    support: [
      { name: 'مرکز کمک', href: '/help' },
      { name: 'سوالات متداول', href: '/help' },
      { name: 'قوانین', href: '/terms' },
      { name: 'حریم خصوصی', href: '/privacy' },
    ],
    business: [
      { name: 'رستوران خود را اضافه کنید', href: '/add-restaurant' },
      { name: 'برای کسب‌وکارها', href: '/business' },
      { name: 'تبلیغات', href: '/advertise' },
      { name: 'API', href: '/api' },
    ],
    categories: [
      { name: 'رستوران‌های سنتی', href: '/category/traditional' },
      { name: 'کافه‌ها', href: '/category/cafe' },
      { name: 'فست فود', href: '/category/fastfood' },
      { name: 'شیرینی فروشی', href: '/category/bakery' },
    ]
  }

  return (
    <footer className="bg-gray-900 text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8">
          {/* Brand Section */}
          <div className="lg:col-span-2">
            <h2 className="text-3xl font-bold text-primary-400 mb-4">از کوجا</h2>
            <p className="text-gray-300 mb-6 leading-relaxed">
              بزرگترین دایرکتوری رستوران‌های افغانستان. بهترین غذاها، بهترین رستوران‌ها، بهترین تجربه‌ها.
            </p>
            
            {/* Contact Info */}
            <div className="space-y-3 mb-6">
              <div className="flex items-center">
                <MapPinIcon className="w-5 h-5 text-primary-400 mr-3 flex-shrink-0" />
                <span className="text-gray-300">کابل، افغانستان</span>
              </div>
              <div className="flex items-center">
                <PhoneIcon className="w-5 h-5 text-primary-400 mr-3 flex-shrink-0" />
                <span className="text-gray-300">+93 XX XXX XXXX</span>
              </div>
              <div className="flex items-center">
                <EnvelopeIcon className="w-5 h-5 text-primary-400 mr-3 flex-shrink-0" />
                <span className="text-gray-300"><EMAIL></span>
              </div>
            </div>

            {/* Social Media */}
            <div className="flex space-x-4 rtl:space-x-reverse">
              <a href="#" className="w-10 h-10 bg-gray-800 hover:bg-primary-600 rounded-full flex items-center justify-center transition-colors">
                <span className="text-sm font-semibold">f</span>
              </a>
              <a href="#" className="w-10 h-10 bg-gray-800 hover:bg-primary-600 rounded-full flex items-center justify-center transition-colors">
                <span className="text-sm font-semibold">t</span>
              </a>
              <a href="#" className="w-10 h-10 bg-gray-800 hover:bg-primary-600 rounded-full flex items-center justify-center transition-colors">
                <span className="text-sm font-semibold">i</span>
              </a>
              <a href="#" className="w-10 h-10 bg-gray-800 hover:bg-primary-600 rounded-full flex items-center justify-center transition-colors">
                <span className="text-sm font-semibold">w</span>
              </a>
            </div>
          </div>

          {/* Company Links */}
          <div>
            <h3 className="text-lg font-semibold mb-4">شرکت</h3>
            <ul className="space-y-3">
              {footerLinks.company.map((link) => (
                <li key={link.name}>
                  <a 
                    href={link.href} 
                    className="text-gray-300 hover:text-primary-400 transition-colors"
                  >
                    {link.name}
                  </a>
                </li>
              ))}
            </ul>
          </div>

          {/* Support Links */}
          <div>
            <h3 className="text-lg font-semibold mb-4">پشتیبانی</h3>
            <ul className="space-y-3">
              {footerLinks.support.map((link) => (
                <li key={link.name}>
                  <a 
                    href={link.href} 
                    className="text-gray-300 hover:text-primary-400 transition-colors"
                  >
                    {link.name}
                  </a>
                </li>
              ))}
            </ul>
          </div>

          {/* Categories */}
          <div>
            <h3 className="text-lg font-semibold mb-4">دسته‌بندی‌ها</h3>
            <ul className="space-y-3">
              {footerLinks.categories.map((link) => (
                <li key={link.name}>
                  <a 
                    href={link.href} 
                    className="text-gray-300 hover:text-primary-400 transition-colors"
                  >
                    {link.name}
                  </a>
                </li>
              ))}
            </ul>
          </div>
        </div>

        {/* Newsletter */}
        <div className="border-t border-gray-800 mt-12 pt-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
            <div>
              <h3 className="text-xl font-semibold mb-2">خبرنامه</h3>
              <p className="text-gray-300">
                آخرین رستوران‌ها و پیشنهادات ویژه را دریافت کنید
              </p>
            </div>
            <div className="flex flex-col sm:flex-row gap-4">
              <input 
                type="email" 
                placeholder="ایمیل شما..."
                className="flex-1 px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent text-white placeholder-gray-400"
              />
              <button className="px-6 py-3 bg-primary-500 hover:bg-primary-600 text-white font-semibold rounded-lg transition-colors whitespace-nowrap">
                عضویت
              </button>
            </div>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="border-t border-gray-800 mt-8 pt-8 flex flex-col md:flex-row justify-between items-center">
          <div className="flex items-center text-gray-400 mb-4 md:mb-0">
            <span>© 2024 از کوجا. تمامی حقوق محفوظ است.</span>
          </div>
          <div className="flex items-center text-gray-400">
            <span>ساخته شده با</span>
            <HeartIcon className="w-4 h-4 text-red-500 mx-1" />
            <span>در افغانستان</span>
          </div>
        </div>
      </div>
    </footer>
  )
} 