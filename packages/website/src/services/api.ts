import axios from 'axios';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:7000/api';

// Create axios instance
const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('authToken');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => response,
  (error) => {
    console.error('API Error:', error.response?.data || error.message);
    return Promise.reject(error);
  }
);

// Photo Management API
export const photoApi = {
  // Upload photos to restaurant (using test server for now)
  uploadPhotos: async (restaurantId: string, photos: File[], category?: string) => {
    const formData = new FormData();
    photos.forEach(photo => {
      formData.append('photos', photo);
    });
    if (category) {
      formData.append('category', category);
    }
    
    // For testing, use the test server
    const response = await fetch('http://localhost:8000/test/upload', {
      method: 'POST',
      body: formData,
    });
    
    if (!response.ok) {
      throw new Error('Upload failed');
    }
    
    return response.json();
  },

  // Get all photos for a restaurant
  getPhotos: async (restaurantId: string, category?: string) => {
    const params = new URLSearchParams();
    if (category) params.append('category', category);
    
    const response = await api.get(`/restaurants/${restaurantId}/photos?${params}`);
    return response.data;
  },

  // Update photo metadata
  updatePhoto: async (restaurantId: string, photoId: string, data: any) => {
    const response = await api.patch(`/restaurants/${restaurantId}/photos/${photoId}`, data);
    return response.data;
  },

  // Delete photo
  deletePhoto: async (restaurantId: string, photoId: string) => {
    const response = await api.delete(`/restaurants/${restaurantId}/photos/${photoId}`);
    return response.data;
  },

  // Set cover photo
  setCoverPhoto: async (restaurantId: string, photoId: string) => {
    const response = await api.patch(`/restaurants/${restaurantId}/photos/${photoId}/cover`);
    return response.data;
  },

  // Reorder photos
  reorderPhotos: async (restaurantId: string, photoIds: string[]) => {
    const response = await api.patch(`/restaurants/${restaurantId}/photos/reorder`, {
      photoIds
    });
    return response.data;
  },

  // Get photo statistics
  getPhotoStats: async (restaurantId: string) => {
    const response = await api.get(`/restaurants/${restaurantId}/photos/stats`);
    return response.data;
  }
};

// Auth API
export const authApi = {
  login: async (email: string, password: string) => {
    const response = await api.post('/auth/login', { email, password });
    return response.data;
  },

  register: async (userData: any) => {
    const response = await api.post('/auth/register', userData);
    return response.data;
  },

  logout: async () => {
    const response = await api.post('/auth/logout');
    return response.data;
  },

  getProfile: async () => {
    const response = await api.get('/auth/profile');
    return response.data;
  }
};

// Restaurant API
export const restaurantApi = {
  getAll: async (filters?: any) => {
    const params = new URLSearchParams();
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value) params.append(key, value as string);
      });
    }
    
    const response = await api.get(`/restaurants?${params}`);
    return response.data;
  },

  getById: async (id: string) => {
    const response = await api.get(`/restaurants/${id}`);
    return response.data;
  },

  create: async (data: any) => {
    const response = await api.post('/restaurants', data);
    return response.data;
  },

  update: async (id: string, data: any) => {
    const response = await api.patch(`/restaurants/${id}`, data);
    return response.data;
  },

  delete: async (id: string) => {
    const response = await api.delete(`/restaurants/${id}`);
    return response.data;
  }
};

export default api; 