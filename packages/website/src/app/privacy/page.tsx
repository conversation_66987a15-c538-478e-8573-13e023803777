'use client'

import Header from '@/components/Header'
import Footer from '@/components/Footer'
import { useTranslations } from '@/hooks/useTranslations'
import { 
  ShieldCheckIcon,
  EyeIcon,
  LockClosedIcon
} from '@heroicons/react/24/outline'

export default function PrivacyPage() {
  const { language } = useTranslations()

  const content = {
    fa: {
      title: 'سیاست حریم خصوصی',
      subtitle: 'نحوه جمع‌آوری، استفاده و حفاظت از اطلاعات شما',
      lastUpdated: 'آخرین بروزرسانی:',
      intro: 'مقدمه',
      introText: 'اذکیا متعهد به حفظ حریم خصوصی شما است. این سیاست نحوه جمع‌آوری، استفاده و حفاظت از اطلاعات شخصی شما را توضیح می‌دهد.',
      dataCollection: 'جمع‌آوری اطلاعات',
      dataCollectionText: 'ما اطلاعات زیر را جمع‌آوری می‌کنیم:',
      dataTypes: [
        'اطلاعات شخصی (نام، شماره تلفن، آدرس)',
        'اطلاعات سفارش (تاریخچه سفارشات، ترجیحات)',
        'اطلاعات فنی (آدرس IP، نوع مرورگر)',
        'اطلاعات موقعیت جغرافیایی (برای تحویل)'
      ],
      dataUse: 'استفاده از اطلاعات',
      dataUseText: 'اطلاعات شما برای موارد زیر استفاده می‌شود:',
      dataUses: [
        'پردازش و تحویل سفارشات',
        'ارتباط با شما درباره سفارشات',
        'بهبود خدمات ما',
        'ارسال اطلاعات بازاریابی (با رضایت شما)'
      ],
      dataSharing: 'اشتراک‌گذاری اطلاعات',
      dataSharingText: 'ما اطلاعات شما را در موارد زیر به اشتراک می‌گذاریم:',
      dataSharings: [
        'با رستوران‌ها برای پردازش سفارش',
        'با سرویس‌های پرداخت برای تراکنش‌ها',
        'با مقامات قانونی در صورت ضرورت',
        'با شرکای تجاری (با رضایت شما)'
      ],
      dataSecurity: 'امنیت اطلاعات',
      dataSecurityText: 'ما از روش‌های امنیتی پیشرفته برای حفاظت از اطلاعات شما استفاده می‌کنیم، شامل رمزگذاری و کنترل دسترسی.',
      yourRights: 'حقوق شما',
      yourRightsText: 'شما حقوق زیر را دارید:',
      rights: [
        'دسترسی به اطلاعات شخصی خود',
        'اصلاح اطلاعات نادرست',
        'حذف اطلاعات شخصی',
        'محدود کردن پردازش اطلاعات'
      ],
      cookies: 'کوکی‌ها',
      cookiesText: 'ما از کوکی‌ها برای بهبود تجربه کاربری استفاده می‌کنیم. می‌توانید در تنظیمات مرورگر خود کوکی‌ها را مدیریت کنید.',
      changes: 'تغییرات',
      changesText: 'این سیاست ممکن است تغییر کند. تغییرات از طریق وب‌سایت اطلاع‌رسانی می‌شود.',
      contact: 'تماس',
      contactText: 'برای سوالات درباره حریم خصوصی، با ما تماس بگیرید:'
    },
    en: {
      title: 'Privacy Policy',
      subtitle: 'How we collect, use, and protect your information',
      lastUpdated: 'Last updated:',
      intro: 'Introduction',
      introText: 'Azkuja is committed to protecting your privacy. This policy explains how we collect, use, and protect your personal information.',
      dataCollection: 'Information Collection',
      dataCollectionText: 'We collect the following information:',
      dataTypes: [
        'Personal information (name, phone number, address)',
        'Order information (order history, preferences)',
        'Technical information (IP address, browser type)',
        'Location information (for delivery)'
      ],
      dataUse: 'Use of Information',
      dataUseText: 'Your information is used for:',
      dataUses: [
        'Processing and delivering orders',
        'Communicating with you about orders',
        'Improving our services',
        'Sending marketing information (with your consent)'
      ],
      dataSharing: 'Information Sharing',
      dataSharingText: 'We share your information in the following cases:',
      dataSharings: [
        'With restaurants for order processing',
        'With payment services for transactions',
        'With legal authorities when required',
        'With business partners (with your consent)'
      ],
      dataSecurity: 'Data Security',
      dataSecurityText: 'We use advanced security methods to protect your information, including encryption and access control.',
      yourRights: 'Your Rights',
      yourRightsText: 'You have the following rights:',
      rights: [
        'Access to your personal information',
        'Correction of incorrect information',
        'Deletion of personal information',
        'Restriction of information processing'
      ],
      cookies: 'Cookies',
      cookiesText: 'We use cookies to improve user experience. You can manage cookies in your browser settings.',
      changes: 'Changes',
      changesText: 'This policy may change. Changes will be announced through the website.',
      contact: 'Contact',
      contactText: 'For privacy questions, contact us:'
    },
    ps: {
      title: 'د شخصي پټوالۍ پالیسي',
      subtitle: 'موږ څنګه ستاسو معلومات راټولوو، کاروو او ساتو',
      lastUpdated: 'وروستي تازه کول:',
      intro: 'پیژندنه',
      introText: 'اذکیا ستاسو د شخصي پټوالۍ ساتلو ته ژمن دی. دا پالیسي توضیح کوي چې موږ څنګه ستاسو شخصي معلومات راټولوو، کاروو او ساتو.',
      dataCollection: 'د معلوماتو راټولول',
      dataCollectionText: 'موږ لاندې معلومات راټولوو:',
      dataTypes: [
        'شخصي معلومات (نوم، د تلیفون شمیره، پته)',
        'د امر معلومات (د امرونو تاریخ، غوره توبونه)',
        'تخنیکي معلومات (IP پته، د براوزر ډول)',
        'د موقعیت معلومات (د رسولو لپاره)'
      ],
      dataUse: 'د معلوماتو کارول',
      dataUseText: 'ستاسو معلومات د لاندې موخو لپاره کارول کیږي:',
      dataUses: [
        'د امرونو پروسس او رسول',
        'د امرونو په اړه د تاسو سره اړیکه',
        'زموږ د خدماتو ښه کول',
        'د بازار موندنې معلومات لیږل (د تاسو د رضایت سره)'
      ],
      dataSharing: 'د معلوماتو شریکول',
      dataSharingText: 'موږ ستاسو معلومات په لاندې حالاتو کې شریکوو:',
      dataSharings: [
        'د امر پروسس لپاره د رستورانونو سره',
        'د لیږد لپاره د تادیاتو خدماتو سره',
        'د اړتیا په صورت کې د قانوني چارواکو سره',
        'د سوداګریزو ملګرو سره (د تاسو د رضایت سره)'
      ],
      dataSecurity: 'د ډیټا امنیت',
      dataSecurityText: 'موږ ستاسو د معلوماتو د ساتنې لپاره پرمختللي امنیتي میتودونه کاروو، پشمول د کوډ کولو او د لاسرسي کنټرول.',
      yourRights: 'ستاسو حقونه',
      yourRightsText: 'تاسو لاندې حقونه لرئ:',
      rights: [
        'خپلو شخصي معلوماتو ته لاسرسی',
        'د غلطو معلوماتو سمون',
        'د شخصي معلوماتو ړنګول',
        'د معلوماتو پروسس محدودول'
      ],
      cookies: 'کوکیز',
      cookiesText: 'موږ د کارونکي تجربې د ښه کولو لپاره کوکیز کاروو. تاسو کولی شئ د خپل براوزر تنظیماتو کې کوکیز اداره کړئ.',
      changes: 'بدلونونه',
      changesText: 'دا پالیسي ممکن بدلون ومومي. بدلونونه د ویب پاڼې له لارې اعلان کیږي.',
      contact: 'اړیکه',
      contactText: 'د شخصي پټوالۍ د پوښتنو لپاره، موږ سره اړیکه ونیسئ:'
    }
  }

  const currentContent = content[language] || content.fa

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <Header />
      
      <main>
        {/* Hero Section */}
        <section className="bg-gradient-to-br from-primary-50 to-orange-50 dark:from-gray-800 dark:to-gray-900 py-20">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center">
              <ShieldCheckIcon className="w-16 h-16 text-primary-600 mx-auto mb-6" />
              <h1 className="text-4xl md:text-6xl font-bold text-gray-900 dark:text-white mb-6">
                {currentContent.title}
              </h1>
              <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto mb-4">
                {currentContent.subtitle}
              </p>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {currentContent.lastUpdated} {language === 'en' ? 'December 2024' : language === 'ps' ? 'د دسمبر ۲۰۲۴' : 'دسامبر ۲۰۲۴'}
              </p>
            </div>
          </div>
        </section>

        {/* Privacy Content */}
        <section className="py-16 bg-white dark:bg-gray-800">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="prose prose-lg dark:prose-invert max-w-none">
              
              {/* Introduction */}
              <div className="mb-12">
                <div className="flex items-center mb-6">
                  <EyeIcon className="w-8 h-8 text-primary-600 ml-3 rtl:mr-3 rtl:ml-0" />
                  <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
                    {currentContent.intro}
                  </h2>
                </div>
                <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                  {currentContent.introText}
                </p>
              </div>

              {/* Data Collection */}
              <div className="mb-12">
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                  ۱. {currentContent.dataCollection}
                </h3>
                <p className="text-gray-600 dark:text-gray-300 mb-4">
                  {currentContent.dataCollectionText}
                </p>
                <ul className="list-disc list-inside space-y-2 text-gray-600 dark:text-gray-300">
                  {currentContent.dataTypes.map((type, index) => (
                    <li key={index}>{type}</li>
                  ))}
                </ul>
              </div>

              {/* Data Use */}
              <div className="mb-12">
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                  ۲. {currentContent.dataUse}
                </h3>
                <p className="text-gray-600 dark:text-gray-300 mb-4">
                  {currentContent.dataUseText}
                </p>
                <ul className="list-disc list-inside space-y-2 text-gray-600 dark:text-gray-300">
                  {currentContent.dataUses.map((use, index) => (
                    <li key={index}>{use}</li>
                  ))}
                </ul>
              </div>

              {/* Data Sharing */}
              <div className="mb-12">
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                  ۳. {currentContent.dataSharing}
                </h3>
                <p className="text-gray-600 dark:text-gray-300 mb-4">
                  {currentContent.dataSharingText}
                </p>
                <ul className="list-disc list-inside space-y-2 text-gray-600 dark:text-gray-300">
                  {currentContent.dataSharings.map((sharing, index) => (
                    <li key={index}>{sharing}</li>
                  ))}
                </ul>
              </div>

              {/* Data Security */}
              <div className="mb-12">
                <div className="flex items-center mb-4">
                  <LockClosedIcon className="w-6 h-6 text-primary-600 ml-2 rtl:mr-2 rtl:ml-0" />
                  <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
                    ۴. {currentContent.dataSecurity}
                  </h3>
                </div>
                <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                  {currentContent.dataSecurityText}
                </p>
              </div>

              {/* Your Rights */}
              <div className="mb-12">
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                  ۵. {currentContent.yourRights}
                </h3>
                <p className="text-gray-600 dark:text-gray-300 mb-4">
                  {currentContent.yourRightsText}
                </p>
                <ul className="list-disc list-inside space-y-2 text-gray-600 dark:text-gray-300">
                  {currentContent.rights.map((right, index) => (
                    <li key={index}>{right}</li>
                  ))}
                </ul>
              </div>

              {/* Cookies */}
              <div className="mb-12">
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                  ۶. {currentContent.cookies}
                </h3>
                <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                  {currentContent.cookiesText}
                </p>
              </div>

              {/* Changes */}
              <div className="mb-12">
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                  ۷. {currentContent.changes}
                </h3>
                <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                  {currentContent.changesText}
                </p>
              </div>

              {/* Contact */}
              <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                  {currentContent.contact}
                </h3>
                <p className="text-gray-600 dark:text-gray-300 mb-4">
                  {currentContent.contactText}
                </p>
                <div className="space-y-2">
                  <p className="text-gray-600 dark:text-gray-300">
                    📧 <EMAIL>
                  </p>
                  <p className="text-gray-600 dark:text-gray-300">
                    📞 +93 70 123 4567
                  </p>
                  <p className="text-gray-600 dark:text-gray-300">
                    📍 {language === 'en' ? 'Kabul, Afghanistan' : language === 'ps' ? 'کابل، افغانستان' : 'کابل، افغانستان'}
                  </p>
                </div>
              </div>

            </div>
          </div>
        </section>
      </main>

      <Footer />
    </div>
  )
} 