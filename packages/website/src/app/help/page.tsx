'use client'

import { useState } from 'react'
import Header from '@/components/Header'
import Footer from '@/components/Footer'
import { useTranslations } from '@/hooks/useTranslations'
import { 
  MagnifyingGlassIcon,
  QuestionMarkCircleIcon,
  BookOpenIcon,
  ChatBubbleLeftRightIcon,
  PhoneIcon,
  ChevronDownIcon
} from '@heroicons/react/24/outline'

export default function HelpPage() {
  const { language } = useTranslations()
  const [searchTerm, setSearchTerm] = useState('')
  const [openFaq, setOpenFaq] = useState<number | null>(null)

  const content = {
    fa: {
      title: 'مرکز راهنمایی',
      subtitle: 'پاسخ سوالات شما در اینجا',
      searchPlaceholder: 'جستجو در راهنما...',
      commonQuestions: 'سوالات متداول',
      gettingStarted: 'شروع کار',
      orderingFood: 'سفارش غذا',
      payments: 'پرداخت‌ها',
      troubleshooting: 'رفع مشکل',
      stillNeedHelp: 'هنوز کمک نیاز دارید؟',
      contactSupport: 'تماس با پشتیبانی',
      callUs: 'تماس با ما',
      emailUs: 'ایمیل به ما',
      faqs: [
        {
          question: 'چگونه حساب کاربری ایجاد کنم؟',
          answer: 'برای ایجاد حساب کاربری، روی دکمه "ورود" کلیک کنید و سپس "ثبت نام" را انتخاب کنید. شماره تلفن خود را وارد کرده و کد تأیید را دریافت کنید.'
        },
        {
          question: 'چگونه سفارش دهم؟',
          answer: 'برای سفارش، ابتدا رستوران مورد نظر را انتخاب کنید، غذاهای دلخواه را به سبد خرید اضافه کنید، آدرس تحویل را وارد کرده و پرداخت را انجام دهید.'
        },
        {
          question: 'روش‌های پرداخت چیست؟',
          answer: 'شما می‌توانید نقدی هنگام تحویل، با کارت بانکی آنلاین، یا از طریق کیف پول دیجیتال پرداخت کنید.'
        },
        {
          question: 'زمان تحویل چقدر است؟',
          answer: 'زمان تحویل معمولاً بین ۳۰ تا ۶۰ دقیقه است، اما بسته به موقعیت و رستوران انتخابی متفاوت است.'
        },
        {
          question: 'آیا می‌توانم سفارش را لغو کنم؟',
          answer: 'بله، تا زمانی که رستوران سفارش را تأیید نکرده، می‌توانید لغو کنید. بعد از تأیید، با پشتیبانی تماس بگیرید.'
        },
        {
          question: 'هزینه ارسال چقدر است؟',
          answer: 'هزینه ارسال بر اساس فاصله و رستوران محاسبه می‌شود. معمولاً بین ۲۰ تا ۱۰۰ افغانی است.'
        },
        {
          question: 'چگونه وضعیت سفارش را پیگیری کنم؟',
          answer: 'می‌توانید در بخش "سفارشات من" وضعیت سفارش خود را مشاهده کنید. همچنین پیامک اطلاع‌رسانی دریافت خواهید کرد.'
        },
        {
          question: 'اگر مشکلی با سفارش داشته باشم چه کنم؟',
          answer: 'در صورت هر گونه مشکل، با پشتیبانی از طریق تلفن یا ایمیل تماس بگیرید. ما در اسرع وقت مشکل را حل خواهیم کرد.'
        }
      ]
    },
    en: {
      title: 'Help Center',
      subtitle: 'Find answers to your questions here',
      searchPlaceholder: 'Search help...',
      commonQuestions: 'Frequently Asked Questions',
      gettingStarted: 'Getting Started',
      orderingFood: 'Ordering Food',
      payments: 'Payments',
      troubleshooting: 'Troubleshooting',
      stillNeedHelp: 'Still need help?',
      contactSupport: 'Contact Support',
      callUs: 'Call Us',
      emailUs: 'Email Us',
      faqs: [
        {
          question: 'How do I create an account?',
          answer: 'To create an account, click the "Login" button and then select "Sign Up". Enter your phone number and receive a verification code.'
        },
        {
          question: 'How do I place an order?',
          answer: 'To place an order, first select your desired restaurant, add your favorite foods to the cart, enter delivery address, and complete payment.'
        },
        {
          question: 'What are the payment methods?',
          answer: 'You can pay cash on delivery, with online bank card, or through digital wallet.'
        },
        {
          question: 'What is the delivery time?',
          answer: 'Delivery time is usually between 30 to 60 minutes, but varies depending on location and selected restaurant.'
        },
        {
          question: 'Can I cancel my order?',
          answer: 'Yes, you can cancel as long as the restaurant hasn\'t confirmed the order. After confirmation, contact support.'
        },
        {
          question: 'How much is the delivery fee?',
          answer: 'Delivery fee is calculated based on distance and restaurant. Usually between 20 to 100 AFN.'
        },
        {
          question: 'How do I track my order status?',
          answer: 'You can view your order status in the "My Orders" section. You will also receive SMS notifications.'
        },
        {
          question: 'What should I do if I have a problem with my order?',
          answer: 'If you have any issues, contact support via phone or email. We will resolve the problem as soon as possible.'
        }
      ]
    },
    ps: {
      title: 'د مرستې مرکز',
      subtitle: 'دلته د خپلو پوښتنو ځوابونه ومومئ',
      searchPlaceholder: 'په مرسته کې پلټنه...',
      commonQuestions: 'ډیری پوښتل شوي پوښتنې',
      gettingStarted: 'پیل',
      orderingFood: 'د خوړو امر ورکول',
      payments: 'تادیات',
      troubleshooting: 'د ستونزو حل',
      stillNeedHelp: 'لاهم مرستې ته اړتیا لرئ؟',
      contactSupport: 'د ملاتړ سره اړیکه',
      callUs: 'موږ ته زنګ ووهئ',
      emailUs: 'موږ ته ایمیل واستوئ',
      faqs: [
        {
          question: 'څنګه حساب جوړ کړم؟',
          answer: 'د حساب جوړولو لپاره، د "ننوتل" تڼۍ کلیک کړئ او بیا "ثبت نوم" وټاکئ. خپل د تلیفون شمیره ولیکئ او د تایید کوډ ترلاسه کړئ.'
        },
        {
          question: 'څنګه امر ورکړم؟',
          answer: 'د امر ورکولو لپاره، لومړی خپل غوښتل شوی رستوران وټاکئ، خپل خوښې خواړه سبد ته اضافه کړئ، د رسولو پته ولیکئ او تادیه بشپړه کړئ.'
        },
        {
          question: 'د تادیاتو میتودونه څه دي؟',
          answer: 'تاسو کولی شئ د رسولو پر وخت نغدي، د آنلاین بانکي کارټ سره، یا د ډیجیټل پیسو کیف له لارې تادیه وکړئ.'
        },
        {
          question: 'د رسولو وخت څومره دی؟',
          answer: 'د رسولو وخت معمولاً د ۳۰ څخه تر ۶۰ دقیقو پورې دی، مګر د موقعیت او ټاکل شوي رستوران پورې اړه لري.'
        },
        {
          question: 'ایا زه کولی شم امر لغوه کړم؟',
          answer: 'هو، تاسو کولی شئ لغوه کړئ تر هغه چې رستوران امر تایید نه کړي. د تایید وروسته، د ملاتړ سره اړیکه ونیسئ.'
        },
        {
          question: 'د رسولو فیس څومره دی؟',
          answer: 'د رسولو فیس د واټن او رستوران پر بنسټ محاسبه کیږي. معمولاً د ۲۰ څخه تر ۱۰۰ افغانۍ پورې.'
        },
        {
          question: 'څنګه د خپل امر حالت تعقیب کړم؟',
          answer: 'تاسو کولی شئ د "زموږ امرونه" برخه کې د خپل امر حالت وګورئ. تاسو به د SMS اطلاع هم ترلاسه کړئ.'
        },
        {
          question: 'که زه د خپل امر سره ستونزه ولرم څه وکړم؟',
          answer: 'که چیرې کومه ستونزه ولرئ، د تلیفون یا ایمیل له لارې د ملاتړ سره اړیکه ونیسئ. موږ به ډیر ژر ستونزه حل کړو.'
        }
      ]
    }
  }

  const currentContent = content[language] || content.fa

  const filteredFaqs = currentContent.faqs.filter(faq =>
    faq.question.toLowerCase().includes(searchTerm.toLowerCase()) ||
    faq.answer.toLowerCase().includes(searchTerm.toLowerCase())
  )

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <Header />
      
      <main>
        {/* Hero Section */}
        <section className="bg-gradient-to-br from-primary-50 to-orange-50 dark:from-gray-800 dark:to-gray-900 py-20">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center">
              <h1 className="text-4xl md:text-6xl font-bold text-gray-900 dark:text-white mb-6">
                {currentContent.title}
              </h1>
              <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto mb-8">
                {currentContent.subtitle}
              </p>
              
              {/* Search Bar */}
              <div className="max-w-2xl mx-auto relative">
                <div className="relative">
                  <MagnifyingGlassIcon className="absolute left-4 rtl:right-4 rtl:left-auto top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                  <input
                    type="text"
                    placeholder={currentContent.searchPlaceholder}
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pl-12 rtl:pr-12 rtl:pl-4 pr-4 py-4 rounded-xl border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  />
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Help Categories */}
        <section className="py-16 bg-white dark:bg-gray-800">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900 dark:to-blue-800 rounded-xl p-6 text-center hover:shadow-lg transition-shadow">
                <BookOpenIcon className="w-12 h-12 text-blue-600 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                  {currentContent.gettingStarted}
                </h3>
                <p className="text-gray-600 dark:text-gray-300 text-sm">
                  {language === 'en' ? 'Learn the basics' : language === 'ps' ? 'اساسات زده کړئ' : 'آموزش مقدماتی'}
                </p>
              </div>

              <div className="bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900 dark:to-green-800 rounded-xl p-6 text-center hover:shadow-lg transition-shadow">
                <QuestionMarkCircleIcon className="w-12 h-12 text-green-600 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                  {currentContent.orderingFood}
                </h3>
                <p className="text-gray-600 dark:text-gray-300 text-sm">
                  {language === 'en' ? 'How to place orders' : language === 'ps' ? 'څنګه امر ورکړئ' : 'نحوه سفارش‌دهی'}
                </p>
              </div>

              <div className="bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900 dark:to-purple-800 rounded-xl p-6 text-center hover:shadow-lg transition-shadow">
                <ChatBubbleLeftRightIcon className="w-12 h-12 text-purple-600 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                  {currentContent.payments}
                </h3>
                <p className="text-gray-600 dark:text-gray-300 text-sm">
                  {language === 'en' ? 'Payment methods' : language === 'ps' ? 'د تادیاتو میتودونه' : 'روش‌های پرداخت'}
                </p>
              </div>

              <div className="bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-900 dark:to-orange-800 rounded-xl p-6 text-center hover:shadow-lg transition-shadow">
                <PhoneIcon className="w-12 h-12 text-orange-600 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                  {currentContent.troubleshooting}
                </h3>
                <p className="text-gray-600 dark:text-gray-300 text-sm">
                  {language === 'en' ? 'Solve common issues' : language === 'ps' ? 'عام ستونزې حل کړئ' : 'حل مشکلات رایج'}
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* FAQ Section */}
        <section className="py-16 bg-gray-50 dark:bg-gray-900">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
                {currentContent.commonQuestions}
              </h2>
            </div>

            <div className="space-y-4">
              {filteredFaqs.map((faq, index) => (
                <div key={index} className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
                  <button
                    onClick={() => setOpenFaq(openFaq === index ? null : index)}
                    className="w-full px-6 py-4 text-left flex items-center justify-between hover:bg-gray-50 dark:hover:bg-gray-700 rounded-lg transition-colors"
                  >
                    <span className="font-medium text-gray-900 dark:text-white">
                      {faq.question}
                    </span>
                    <ChevronDownIcon className={`w-5 h-5 text-gray-500 transform transition-transform ${openFaq === index ? 'rotate-180' : ''}`} />
                  </button>
                  {openFaq === index && (
                    <div className="px-6 pb-4">
                      <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                        {faq.answer}
                      </p>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Contact Support Section */}
        <section className="py-16 bg-primary-600">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 className="text-3xl font-bold text-white mb-4">
              {currentContent.stillNeedHelp}
            </h2>
            <p className="text-xl text-primary-100 mb-8">
              {currentContent.contactSupport}
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a
                href="tel:+93701234567"
                className="bg-white text-primary-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors inline-flex items-center justify-center space-x-2 rtl:space-x-reverse"
              >
                <PhoneIcon className="w-5 h-5" />
                <span>{currentContent.callUs}</span>
              </a>
              <a
                href="mailto:<EMAIL>"
                className="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-primary-600 transition-colors inline-flex items-center justify-center space-x-2 rtl:space-x-reverse"
              >
                <ChatBubbleLeftRightIcon className="w-5 h-5" />
                <span>{currentContent.emailUs}</span>
              </a>
            </div>
          </div>
        </section>
      </main>

      <Footer />
    </div>
  )
} 