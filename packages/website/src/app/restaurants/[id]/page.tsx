'use client'

import { useState, useEffect } from 'react'
import { usePara<PERSON>, useRouter } from 'next/navigation'
import Header from '@/components/Header'
import Footer from '@/components/Footer'
import RestaurantGallery from '@/components/RestaurantGallery'
import RestaurantInfo from '@/components/RestaurantInfo'
import RestaurantMenu from '@/components/RestaurantMenu'
import RestaurantReviews from '@/components/RestaurantReviews'
// import RestaurantReservation from '@/components/RestaurantReservation' // DISABLED: Reservation system removed
import { Restaurant, MenuItem, Review } from '@/lib/api'
import apiService from '@/lib/api'
import { 
  StarIcon, 
  MapPinIcon, 
  PhoneIcon,
  ClockIcon,
  ShareIcon,
  HeartIcon,
  ArrowLeftIcon,
  GlobeAltIcon,
  ChevronLeftIcon
} from '@heroicons/react/24/outline'
import { 
  StarIcon as StarIconSolid, 
  HeartIcon as HeartIconSolid
} from '@heroicons/react/24/solid'
import Link from 'next/link'
import { useAuth } from '@/contexts/AuthContext'
import Image from 'next/image'

export default function RestaurantDetailPage() {
  const params = useParams()
  const router = useRouter()
  const { isAuthenticated } = useAuth()
  const restaurantId = params?.id as string

  const [restaurant, setRestaurant] = useState<Restaurant | null>(null)
  const [menuItems, setMenuItems] = useState<MenuItem[]>([])
  const [reviews, setReviews] = useState<Review[]>([])
  const [loading, setLoading] = useState(true)
  const [menuLoading, setMenuLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [isFavorite, setIsFavorite] = useState(false)
  const [activeTab, setActiveTab] = useState('overview')
  const [selectedCategory, setSelectedCategory] = useState<string>('all')

  useEffect(() => {
    if (restaurantId) {
      loadRestaurantData()
    }
  }, [restaurantId])

  const loadRestaurantData = async () => {
    try {
      setLoading(true)
      const [restaurantData, menuData, reviewsData] = await Promise.all([
        apiService.getRestaurant(restaurantId),
        apiService.getRestaurantMenu(restaurantId),
        apiService.getRestaurantReviews(restaurantId)
      ])
      
      setRestaurant(restaurantData)
      setMenuItems(menuData)
      setReviews(reviewsData.data)
    } catch (err) {
      console.error('Error loading restaurant data:', err)
      setError('خطا در بارگذاری اطلاعات رستوران')
    } finally {
      setLoading(false)
    }
  }

  const toggleFavorite = () => {
    if (!isAuthenticated) {
      router.push('/login')
      return
    }
    setIsFavorite(!isFavorite)
  }

  const shareRestaurant = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: restaurant?.name,
          text: `${restaurant?.name} - ${restaurant?.description}`,
          url: window.location.href,
        })
      } catch (err) {
        console.log('Error sharing:', err)
      }
    } else {
      // Fallback to clipboard
      navigator.clipboard.writeText(window.location.href)
      // You could show a toast notification here
    }
  }

  const getPriceRangeText = (range: number) => {
    return '۔'.repeat(range) + '۔'.repeat(Math.max(0, 4 - range))
  }

  const menuCategories = ['all', ...new Set(menuItems.map(item => item.category))]
  const filteredMenu = selectedCategory === 'all' 
    ? menuItems 
    : menuItems.filter(item => item.category === selectedCategory)

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <Header />
        <div className="flex items-center justify-center min-h-[calc(100vh-8rem)]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500 mx-auto mb-4"></div>
            <p className="text-gray-600 dark:text-gray-400">در حال بارگذاری...</p>
          </div>
        </div>
      </div>
    )
  }

  if (error || !restaurant) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <Header />
        <div className="flex items-center justify-center min-h-[calc(100vh-8rem)]">
          <div className="text-center">
            <p className="text-red-600 dark:text-red-400 mb-4">{error || 'رستوران یافت نشد'}</p>
            <Link 
              href="/restaurants"
              className="text-primary-600 hover:text-primary-700 font-medium"
            >
              بازگشت به لیست رستوران‌ها
            </Link>
          </div>
        </div>
      </div>
    )
  }

  const tabs = [
    { id: 'overview', label: 'نمای کلی', count: null },
    { id: 'menu', label: 'منو', count: menuItems.length },
    { id: 'reviews', label: 'نظرات', count: reviews.length },
    { id: 'photos', label: 'تصاویر', count: restaurant.photos?.length || 0 },
    { id: 'info', label: 'اطلاعات', count: null }
  ]

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <Header />
      
      {/* Breadcrumb */}
      <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
            <Link href="/" className="hover:text-primary-600">خانه</Link>
            <ArrowLeftIcon className="w-4 h-4" />
            <Link href="/restaurants" className="hover:text-primary-600">رستوران‌ها</Link>
            <ArrowLeftIcon className="w-4 h-4" />
            <span className="text-gray-900 dark:text-white font-medium">{restaurant.name}</span>
          </div>
        </div>
      </div>

      {/* Hero Section */}
      <div className="relative h-64 md:h-80">
        {restaurant.photos && restaurant.photos.length > 0 ? (
          <Image
            src={restaurant.photos[0].url}
            alt={restaurant.photos[0].alt_text || restaurant.name}
            fill
            className="object-cover"
          />
        ) : (
          <div className="w-full h-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center">
            <span className="text-gray-400 text-lg">بدون تصویر</span>
          </div>
        )}
        <div className="absolute inset-0 bg-black bg-opacity-40"></div>
        
        {/* Back Button */}
        <Link
          href="/restaurants"
          className="absolute top-4 left-4 rtl:right-4 rtl:left-auto bg-white dark:bg-gray-800 p-2 rounded-full shadow-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
        >
          <ChevronLeftIcon className="h-5 w-5 text-gray-700 dark:text-gray-300" />
        </Link>

        {/* Action Buttons */}
        <div className="absolute top-4 right-4 rtl:left-4 rtl:right-auto flex gap-2">
          <button
            onClick={shareRestaurant}
            className="bg-white dark:bg-gray-800 p-2 rounded-full shadow-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
          >
            <ShareIcon className="h-5 w-5 text-gray-700 dark:text-gray-300" />
          </button>
          <button
            onClick={toggleFavorite}
            className="bg-white dark:bg-gray-800 p-2 rounded-full shadow-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
          >
            {isFavorite ? (
              <HeartIconSolid className="h-5 w-5 text-red-500" />
            ) : (
              <HeartIcon className="h-5 w-5 text-gray-700 dark:text-gray-300" />
            )}
          </button>
        </div>
      </div>

      {/* Restaurant Header */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="flex flex-col lg:flex-row gap-6">
          {/* Main Info */}
          <div className="flex-1">
            <div className="flex items-start justify-between mb-4">
              <div className="flex-1">
                <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                  {restaurant.name}
                </h1>
                
                <div className="flex flex-wrap items-center gap-4 mb-4">
                  {/* Rating */}
                  <div className="flex items-center gap-2">
                    <div className="flex items-center">
                      <StarIconSolid className="w-5 h-5 text-yellow-400 fill-current" />
                      <span className="text-lg font-semibold text-gray-900 dark:text-white ml-1">
                        {typeof restaurant.avg_rating === 'number' ? restaurant.avg_rating.toFixed(1) : Number(restaurant.avg_rating || 0).toFixed(1)}
                      </span>
                    </div>
                    <span className="text-gray-600 dark:text-gray-400">
                      ({restaurant.reviews?.length || 0} نظر)
                    </span>
                  </div>

                  {/* Price Range */}
                  <div className="flex items-center text-gray-600 dark:text-gray-400">
                    <span className="text-lg">{getPriceRangeText(restaurant.price_range)}</span>
                  </div>

                  {/* Status */}
                  <div className={`px-3 py-1 rounded-full text-sm font-medium ${
                    restaurant.status === 'active' 
                      ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                      : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                  }`}>
                    {restaurant.status === 'active' ? 'باز' : 'تعطیل'}
                  </div>

                  {restaurant.is_featured && (
                    <div className="bg-primary-100 text-primary-800 dark:bg-primary-900 dark:text-primary-200 px-3 py-1 rounded-full text-sm font-medium">
                      ویژه
                    </div>
                  )}
                </div>

                {/* Categories */}
                {restaurant.categories && restaurant.categories.length > 0 && (
                  <div className="flex flex-wrap gap-2 mb-4">
                    {restaurant.categories.map((category) => (
                      <span 
                        key={category.id}
                        className="px-3 py-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 text-sm rounded-full"
                      >
                        {category.name}
                      </span>
                    ))}
                  </div>
                )}

                {/* Quick Info */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                  <div className="flex items-center text-gray-600 dark:text-gray-400">
                    <MapPinIcon className="w-5 h-5 ml-2 flex-shrink-0" />
                    <span>{restaurant.address}, {restaurant.city}</span>
                  </div>
                  
                  {restaurant.phone && (
                    <div className="flex items-center text-gray-600 dark:text-gray-400">
                      <PhoneIcon className="w-5 h-5 ml-2 flex-shrink-0" />
                      <span>{restaurant.phone}</span>
                    </div>
                  )}
                  
                  <div className="flex items-center text-gray-600 dark:text-gray-400">
                    <ClockIcon className="w-5 h-5 ml-2 flex-shrink-0" />
                    <span>امروز تا ۲۳:۰۰ باز است</span>
                  </div>

                  {restaurant.website && (
                    <div className="flex items-center text-gray-600 dark:text-gray-400">
                      <GlobeAltIcon className="w-5 h-5 ml-2 flex-shrink-0" />
                      <a 
                        href={restaurant.website} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="hover:text-primary-600 dark:hover:text-primary-400"
                      >
                        وبسایت
                      </a>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Description */}
            {restaurant.description && (
              <p className="text-gray-600 dark:text-gray-400 leading-relaxed">
                {restaurant.description}
              </p>
            )}
          </div>

                        {/* Reservation Card - DISABLED */}
              {/* <RestaurantReservation restaurant={restaurant} /> */}
        </div>
      </div>

      {/* Tabs Navigation */}
      <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 sticky top-16 z-40">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex space-x-8 rtl:space-x-reverse overflow-x-auto">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center gap-2 py-4 px-2 border-b-2 font-medium text-sm whitespace-nowrap transition-colors ${
                  activeTab === tab.id
                    ? 'border-primary-500 text-primary-600 dark:text-primary-400'
                    : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
                }`}
              >
                <span>{tab.label}</span>
                {tab.count !== null && (
                  <span className="bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 px-2 py-0.5 rounded-full text-xs">
                    {tab.count}
                  </span>
                )}
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Tab Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {activeTab === 'overview' && (
          <RestaurantInfo restaurant={restaurant} />
        )}
        
        {activeTab === 'menu' && (
          <RestaurantMenu 
            menuItems={filteredMenu} 
            restaurantId={restaurantId}
            restaurantName={restaurant.name}
          />
        )}
        
        {activeTab === 'reviews' && (
          <RestaurantReviews 
            reviews={reviews} 
            restaurantId={restaurantId}
            averageRating={restaurant.avg_rating}
          />
        )}
        
        {activeTab === 'photos' && (
          <RestaurantGallery 
            photos={restaurant.photos || []}
            restaurantName={restaurant.name}
            showAll={true}
          />
        )}
        
        {activeTab === 'info' && (
          <RestaurantInfo restaurant={restaurant} detailed={true} />
        )}
      </div>

      <Footer />
    </div>
  )
} 