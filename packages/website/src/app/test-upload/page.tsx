'use client';

import React, { useState } from 'react';
import { photoApi } from '@/services/api';

const TestUploadPage = () => {
  const [files, setFiles] = useState<File[]>([]);
  const [uploading, setUploading] = useState(false);
  const [results, setResults] = useState<any>(null);
  const [error, setError] = useState<string>('');

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFiles = Array.from(event.target.files || []);
    setFiles(selectedFiles);
    setError('');
    setResults(null);
  };

  const handleUpload = async () => {
    if (files.length === 0) {
      setError('Please select at least one file');
      return;
    }

    setUploading(true);
    setError('');

    try {
      const result = await photoApi.uploadPhotos('test-restaurant', files, 'gallery');
      setResults(result);
      console.log('Upload successful:', result);
    } catch (err: any) {
      setError(`Upload failed: ${err.message}`);
      console.error('Upload error:', err);
    } finally {
      setUploading(false);
    }
  };

  const handleDragOver = (event: React.DragEvent) => {
    event.preventDefault();
  };

  const handleDrop = (event: React.DragEvent) => {
    event.preventDefault();
    const droppedFiles = Array.from(event.dataTransfer.files);
    const imageFiles = droppedFiles.filter(file => file.type.startsWith('image/'));
    setFiles(imageFiles);
    setError('');
    setResults(null);
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <div className="bg-white rounded-lg shadow-lg p-6">
          <h1 className="text-3xl font-bold text-gray-900 mb-6">
            🧪 Cloudinary Photo Upload Test
          </h1>
          
          <div className="space-y-6">
            {/* File Upload Area */}
            <div
              className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-gray-400 transition-colors"
              onDragOver={handleDragOver}
              onDrop={handleDrop}
            >
              <div className="space-y-4">
                <div className="text-6xl">📸</div>
                <div>
                  <p className="text-lg font-medium text-gray-900">
                    Drop photos here or click to select
                  </p>
                  <p className="text-sm text-gray-500 mt-1">
                    PNG, JPG, GIF up to 10MB each
                  </p>
                </div>
                <input
                  type="file"
                  multiple
                  accept="image/*"
                  onChange={handleFileChange}
                  className="hidden"
                  id="file-upload"
                />
                <label
                  htmlFor="file-upload"
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 cursor-pointer"
                >
                  Select Photos
                </label>
              </div>
            </div>

            {/* Selected Files */}
            {files.length > 0 && (
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-3">
                  Selected Files ({files.length})
                </h3>
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                  {files.map((file, index) => (
                    <div key={index} className="bg-gray-50 rounded-lg p-4">
                      <div className="flex items-center space-x-3">
                        <div className="text-2xl">🖼️</div>
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium text-gray-900 truncate">
                            {file.name}
                          </p>
                          <p className="text-xs text-gray-500">
                            {(file.size / 1024 / 1024).toFixed(2)} MB
                          </p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Upload Button */}
            <div className="flex justify-center">
              <button
                onClick={handleUpload}
                disabled={uploading || files.length === 0}
                className={`px-6 py-3 rounded-lg font-medium text-white ${
                  uploading || files.length === 0
                    ? 'bg-gray-400 cursor-not-allowed'
                    : 'bg-green-600 hover:bg-green-700'
                } transition-colors`}
              >
                {uploading ? (
                  <span className="flex items-center">
                    <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Uploading...
                  </span>
                ) : (
                  `Upload ${files.length} Photo${files.length !== 1 ? 's' : ''}`
                )}
              </button>
            </div>

            {/* Error Message */}
            {error && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                <div className="flex">
                  <div className="text-red-400 text-xl mr-3">❌</div>
                  <div>
                    <h3 className="text-sm font-medium text-red-800">
                      Upload Failed
                    </h3>
                    <p className="text-sm text-red-700 mt-1">{error}</p>
                  </div>
                </div>
              </div>
            )}

            {/* Success Results */}
            {results && (
              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <div className="flex">
                  <div className="text-green-400 text-xl mr-3">✅</div>
                  <div className="flex-1">
                    <h3 className="text-sm font-medium text-green-800">
                      Upload Successful!
                    </h3>
                    <p className="text-sm text-green-700 mt-1">
                      {results.message} ({results.count} photos)
                    </p>
                  </div>
                </div>

                {/* Uploaded Photos */}
                {results.photos && (
                  <div className="mt-4">
                    <h4 className="text-sm font-medium text-green-800 mb-2">
                      Uploaded Photos:
                    </h4>
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                      {results.photos.map((photo: any, index: number) => (
                        <div key={index} className="bg-white rounded-lg p-3 border border-green-200">
                          <img
                            src={photo.url}
                            alt={`Uploaded ${index + 1}`}
                            className="w-full h-32 object-cover rounded-md mb-2"
                          />
                          <div className="text-xs text-gray-600">
                            <p><strong>URL:</strong> <a href={photo.url} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">View</a></p>
                            <p><strong>Size:</strong> {photo.width}x{photo.height}</p>
                            <p><strong>Format:</strong> {photo.format}</p>
                            <p><strong>Size:</strong> {(photo.bytes / 1024).toFixed(1)} KB</p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            )}

            {/* Instructions */}
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h3 className="text-sm font-medium text-blue-800 mb-2">
                🔧 Test Instructions
              </h3>
              <ul className="text-sm text-blue-700 space-y-1">
                <li>• Make sure the test server is running: <code className="bg-blue-100 px-1 rounded">yarn ts-node src/test-server.ts</code></li>
                <li>• Test server should be running on: <code className="bg-blue-100 px-1 rounded">http://localhost:8000</code></li>
                <li>• Upload endpoint: <code className="bg-blue-100 px-1 rounded">POST /test/upload</code></li>
                <li>• Photos will be uploaded to Cloudinary in the <code className="bg-blue-100 px-1 rounded">test-restaurant-photos</code> folder</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TestUploadPage; 