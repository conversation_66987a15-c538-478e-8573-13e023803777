'use client'

import { useState } from 'react'
import Header from '@/components/Header'
import Footer from '@/components/Footer'
import { useTranslations } from '@/hooks/useTranslations'
import { 
  PhoneIcon,
  EnvelopeIcon,
  MapPinIcon,
  ClockIcon,
  ChatBubbleLeftRightIcon,
  PaperAirplaneIcon
} from '@heroicons/react/24/outline'

export default function ContactPage() {
  const { formatString, language } = useTranslations()
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    subject: '',
    message: ''
  })
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [submitted, setSubmitted] = useState(false)

  const content = {
    fa: {
      title: 'تماس با ما',
      subtitle: 'ما همیشه آماده شنیدن نظرات و پیشنهادات شما هستیم',
      contactInfo: 'اطلاعات تماس',
      phone: 'تلفن',
      email: 'ایمیل',
      address: 'آدرس',
      workingHours: 'ساعات کاری',
      addressText: 'کابل، افغانستان\nشهر نو، خیابان اصلی\nساختمان تجاری اذکیا، طبقه سوم',
      hoursText: 'شنبه تا پنج‌شنبه: 9:00 - 18:00\nجمعه: 14:00 - 18:00',
      followUs: 'ما را دنبال کنید',
      sendMessage: 'پیام خود را ارسال کنید',
      fullName: 'نام کامل',
      emailLabel: 'ایمیل',
      phoneLabel: 'شماره تلفن',
      subject: 'موضوع',
      message: 'پیام',
      selectSubject: 'موضوع را انتخاب کنید',
      generalQuestion: 'سوال عمومی',
      technicalSupport: 'پشتیبانی فنی',
      businessCooperation: 'همکاری تجاری',
      complaint: 'شکایت',
      suggestion: 'پیشنهاد',
      sendButton: 'ارسال پیام',
      sending: 'در حال ارسال...',
      messageSent: 'پیام شما ارسال شد!',
      messageSuccess: 'ما در اسرع وقت با شما تماس خواهیم گرفت.',
      faq: 'سوالات متداول',
      faqSubtitle: 'پاسخ سوالات رایج در اینجا',
      faqQ1: 'چگونه سفارش ثبت کنم؟',
      faqA1: 'برای ثبت سفارش، ابتدا رستوران مورد نظر خود را انتخاب کنید، سپس غذاهای دلخواه را به سبد خرید اضافه کرده و مراحل پرداخت را تکمیل کنید.',
      faqQ2: 'زمان تحویل چقدر است؟',
      faqA2: 'زمان تحویل معمولاً بین 30 تا 60 دقیقه است، اما بسته به موقعیت شما و رستوران انتخابی ممکن است متفاوت باشد.',
      faqQ3: 'آیا امکان لغو سفارش وجود دارد؟',
      faqA3: 'بله، تا زمانی که رستوران سفارش شما را تأیید نکرده باشد، می‌توانید آن را لغو کنید. پس از تأیید، لطفاً با پشتیبانی تماس بگیرید.',
      faqQ4: 'روش‌های پرداخت چیست؟',
      faqA4: 'شما می‌توانید نقدی هنگام تحویل، از طریق کارت بانکی آنلاین یا کیف پول دیجیتال پرداخت کنید.',
      namePlaceholder: 'نام خود را وارد کنید',
      emailPlaceholder: 'ایمیل خود را وارد کنید',
      phonePlaceholder: 'شماره تلفن خود را وارد کنید',
      messagePlaceholder: 'پیام خود را اینجا بنویسید...'
    },
    en: {
      title: 'Contact Us',
      subtitle: 'We are always ready to hear your feedback and suggestions',
      contactInfo: 'Contact Information',
      phone: 'Phone',
      email: 'Email',
      address: 'Address',
      workingHours: 'Working Hours',
      addressText: 'Kabul, Afghanistan\nShahr-e-Naw, Main Street\nAzkuja Commercial Building, 3rd Floor',
      hoursText: 'Saturday to Thursday: 9:00 AM - 6:00 PM\nFriday: 2:00 PM - 6:00 PM',
      followUs: 'Follow Us',
      sendMessage: 'Send Your Message',
      fullName: 'Full Name',
      emailLabel: 'Email',
      phoneLabel: 'Phone Number',
      subject: 'Subject',
      message: 'Message',
      selectSubject: 'Select a subject',
      generalQuestion: 'General Question',
      technicalSupport: 'Technical Support',
      businessCooperation: 'Business Cooperation',
      complaint: 'Complaint',
      suggestion: 'Suggestion',
      sendButton: 'Send Message',
      sending: 'Sending...',
      messageSent: 'Your message has been sent!',
      messageSuccess: 'We will contact you as soon as possible.',
      faq: 'Frequently Asked Questions',
      faqSubtitle: 'Answers to common questions here',
      faqQ1: 'How do I place an order?',
      faqA1: 'To place an order, first select your desired restaurant, then add your favorite foods to the cart and complete the payment process.',
      faqQ2: 'What is the delivery time?',
      faqA2: 'Delivery time is usually between 30 to 60 minutes, but may vary depending on your location and the selected restaurant.',
      faqQ3: 'Can I cancel my order?',
      faqA3: 'Yes, you can cancel your order as long as the restaurant has not confirmed it. After confirmation, please contact support.',
      faqQ4: 'What are the payment methods?',
      faqA4: 'You can pay cash on delivery, online via bank card, or through digital wallet.',
      namePlaceholder: 'Enter your name',
      emailPlaceholder: 'Enter your email',
      phonePlaceholder: 'Enter your phone number',
      messagePlaceholder: 'Write your message here...'
    },
    ps: {
      title: 'موږ سره اړیکه ونیسئ',
      subtitle: 'موږ تل چمتو یو چې ستاسو نظرونه او وړاندیزونه واورو',
      contactInfo: 'د اړیکې معلومات',
      phone: 'تلیفون',
      email: 'ایمیل',
      address: 'پته',
      workingHours: 'د کار ساعتونه',
      addressText: 'کابل، افغانستان\nشهر نو، اصلي کوڅه\nد اذکیا سوداګریز ودانۍ، دریمه پوړ',
      hoursText: 'د شنبې څخه تر پنجشنبې: 9:00 - 18:00\nجمعه: 14:00 - 18:00',
      followUs: 'موږ تعقیب کړئ',
      sendMessage: 'خپل پیغام واستوئ',
      fullName: 'بشپړ نوم',
      emailLabel: 'ایمیل',
      phoneLabel: 'د تلیفون شمیره',
      subject: 'موضوع',
      message: 'پیغام',
      selectSubject: 'موضوع وټاکئ',
      generalQuestion: 'عمومي پوښتنه',
      technicalSupport: 'تخنیکي ملاتړ',
      businessCooperation: 'سوداګریز همکاري',
      complaint: 'شکایت',
      suggestion: 'وړاندیز',
      sendButton: 'پیغام واستوئ',
      sending: 'استولو کې...',
      messageSent: 'ستاسو پیغام واستول شو!',
      messageSuccess: 'موږ به ډیر ژر تاسو سره اړیکه ونیسو.',
      faq: 'ډیری پوښتل شوي پوښتنې',
      faqSubtitle: 'د عامو پوښتنو ځوابونه دلته',
      faqQ1: 'څنګه امر ورکړم؟',
      faqA1: 'د امر ورکولو لپاره، لومړی خپل غوښتل شوی رستوران وټاکئ، بیا خپل خوښې خواړه سبد ته اضافه کړئ او د تادیاتو پروسه بشپړه کړئ.',
      faqQ2: 'د رسولو وخت څومره دی؟',
      faqA2: 'د رسولو وخت معمولاً د 30 څخه تر 60 دقیقو پورې دی، مګر ستاسو د موقعیت او ټاکل شوي رستوران پورې اړه لري.',
      faqQ3: 'ایا زه کولی شم خپل امر لغوه کړم؟',
      faqA3: 'هو، تاسو کولی شئ خپل امر لغوه کړئ تر هغه چې رستوران دا تایید نه کړي. د تایید وروسته، مهرباني وکړئ د ملاتړ سره اړیکه ونیسئ.',
      faqQ4: 'د تادیاتو میتودونه څه دي؟',
      faqA4: 'تاسو کولی شئ د رسولو پر وخت نغدي، د بانکي کارټ له لارې آنلاین یا د ډیجیټل پیسو کیف له لارې تادیه وکړئ.',
      namePlaceholder: 'خپل نوم ولیکئ',
      emailPlaceholder: 'خپل ایمیل ولیکئ',
      phonePlaceholder: 'د خپل تلیفون شمیره ولیکئ',
      messagePlaceholder: 'خپل پیغام دلته ولیکئ...'
    }
  }

  const currentContent = content[language] || content.fa

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)
    
    // Simulate form submission
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    setIsSubmitting(false)
    setSubmitted(true)
    setFormData({
      name: '',
      email: '',
      phone: '',
      subject: '',
      message: ''
    })
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <Header />
      
      <main>
        {/* Hero Section */}
        <section className="bg-gradient-to-br from-primary-50 to-orange-50 dark:from-gray-800 dark:to-gray-900 py-20">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center">
              <h1 className="text-4xl md:text-6xl font-bold text-gray-900 dark:text-white mb-6">
                {currentContent.title}
              </h1>
              <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                {currentContent.subtitle}
              </p>
            </div>
          </div>
        </section>

        {/* Contact Info & Form Section */}
        <section className="py-16 bg-white dark:bg-gray-800">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
              
              {/* Contact Information */}
              <div>
                <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-8">
                  {currentContent.contactInfo}
                </h2>
                
                <div className="space-y-6">
                  <div className="flex items-start space-x-4 rtl:space-x-reverse">
                    <div className="bg-primary-100 dark:bg-primary-900 p-3 rounded-lg">
                      <PhoneIcon className="w-6 h-6 text-primary-600" />
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-1">
                        {currentContent.phone}
                      </h3>
                      <p className="text-gray-600 dark:text-gray-300">
                        {formatString('+93 70 123 4567')}
                      </p>
                      <p className="text-gray-600 dark:text-gray-300">
                        {formatString('+93 78 987 6543')}
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-4 rtl:space-x-reverse">
                    <div className="bg-primary-100 dark:bg-primary-900 p-3 rounded-lg">
                      <EnvelopeIcon className="w-6 h-6 text-primary-600" />
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-1">
                        {currentContent.email}
                      </h3>
                      <p className="text-gray-600 dark:text-gray-300">
                        <EMAIL>
                      </p>
                      <p className="text-gray-600 dark:text-gray-300">
                        <EMAIL>
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-4 rtl:space-x-reverse">
                    <div className="bg-primary-100 dark:bg-primary-900 p-3 rounded-lg">
                      <MapPinIcon className="w-6 h-6 text-primary-600" />
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-1">
                        {currentContent.address}
                      </h3>
                      <p className="text-gray-600 dark:text-gray-300 whitespace-pre-line">
                        {currentContent.addressText}
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-4 rtl:space-x-reverse">
                    <div className="bg-primary-100 dark:bg-primary-900 p-3 rounded-lg">
                      <ClockIcon className="w-6 h-6 text-primary-600" />
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-1">
                        {currentContent.workingHours}
                      </h3>
                      <p className="text-gray-600 dark:text-gray-300 whitespace-pre-line">
                        {currentContent.hoursText}
                      </p>
                    </div>
                  </div>
                </div>

                {/* Social Media */}
                <div className="mt-8">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                    {currentContent.followUs}
                  </h3>
                  <div className="flex space-x-4 rtl:space-x-reverse">
                    <a href="#" className="bg-blue-600 text-white p-3 rounded-lg hover:bg-blue-700 transition-colors">
                      <span className="sr-only">Facebook</span>
                      <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M20 10C20 4.477 15.523 0 10 0S0 4.477 0 10c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V10h2.54V7.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V10h2.773l-.443 2.89h-2.33v6.988C16.343 19.128 20 14.991 20 10z" clipRule="evenodd" />
                      </svg>
                    </a>
                    <a href="#" className="bg-blue-400 text-white p-3 rounded-lg hover:bg-blue-500 transition-colors">
                      <span className="sr-only">Twitter</span>
                      <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M6.29 18.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0020 3.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.073 4.073 0 01.8 7.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 010 16.407a11.616 11.616 0 006.29 1.84" />
                      </svg>
                    </a>
                    <a href="#" className="bg-pink-600 text-white p-3 rounded-lg hover:bg-pink-700 transition-colors">
                      <span className="sr-only">Instagram</span>
                      <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M10 0C4.477 0 0 4.484 0 10.017c0 4.425 2.865 8.18 6.839 9.504.5.092.682-.217.682-.483 0-.237-.008-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0110 4.844c.85.004 1.705.115 2.504.337 1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.203 2.398.1 2.651.64.7 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.942.359.31.678.921.678 1.856 0 1.338-.012 2.419-.012 2.747 0 .268.18.58.688.482A10.019 10.019 0 0020 10.017C20 4.484 15.522 0 10 0z" clipRule="evenodd" />
                      </svg>
                    </a>
                  </div>
                </div>
              </div>

              {/* Contact Form */}
              <div>
                <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-8">
                  {currentContent.sendMessage}
                </h2>

                {submitted ? (
                  <div className="bg-green-50 dark:bg-green-900 border border-green-200 dark:border-green-700 rounded-lg p-6 text-center">
                    <ChatBubbleLeftRightIcon className="w-12 h-12 text-green-600 mx-auto mb-4" />
                    <h3 className="text-lg font-semibold text-green-800 dark:text-green-200 mb-2">
                      {currentContent.messageSent}
                    </h3>
                    <p className="text-green-600 dark:text-green-300">
                      {currentContent.messageSuccess}
                    </p>
                  </div>
                ) : (
                  <form onSubmit={handleSubmit} className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <label htmlFor="name" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                          {currentContent.fullName} *
                        </label>
                        <input
                          type="text"
                          id="name"
                          name="name"
                          required
                          value={formData.name}
                          onChange={handleInputChange}
                          className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                          placeholder={currentContent.namePlaceholder}
                        />
                      </div>

                      <div>
                        <label htmlFor="email" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                          {currentContent.emailLabel} *
                        </label>
                        <input
                          type="email"
                          id="email"
                          name="email"
                          required
                          value={formData.email}
                          onChange={handleInputChange}
                          className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                          placeholder={currentContent.emailPlaceholder}
                        />
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <label htmlFor="phone" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                          {currentContent.phoneLabel}
                        </label>
                        <input
                          type="tel"
                          id="phone"
                          name="phone"
                          value={formData.phone}
                          onChange={handleInputChange}
                          className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                          placeholder={currentContent.phonePlaceholder}
                        />
                      </div>

                      <div>
                        <label htmlFor="subject" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                          {currentContent.subject} *
                        </label>
                        <select
                          id="subject"
                          name="subject"
                          required
                          value={formData.subject}
                          onChange={handleInputChange}
                          className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                        >
                          <option value="">{currentContent.selectSubject}</option>
                          <option value="general">{currentContent.generalQuestion}</option>
                          <option value="support">{currentContent.technicalSupport}</option>
                          <option value="business">{currentContent.businessCooperation}</option>
                          <option value="complaint">{currentContent.complaint}</option>
                          <option value="suggestion">{currentContent.suggestion}</option>
                        </select>
                      </div>
                    </div>

                    <div>
                      <label htmlFor="message" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        {currentContent.message} *
                      </label>
                      <textarea
                        id="message"
                        name="message"
                        required
                        rows={6}
                        value={formData.message}
                        onChange={handleInputChange}
                        className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                        placeholder={currentContent.messagePlaceholder}
                      />
                    </div>

                    <button
                      type="submit"
                      disabled={isSubmitting}
                      className="w-full bg-primary-600 hover:bg-primary-700 disabled:bg-primary-400 text-white px-6 py-3 rounded-lg font-semibold transition-colors flex items-center justify-center space-x-2 rtl:space-x-reverse"
                    >
                      {isSubmitting ? (
                        <>
                          <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                          <span>{currentContent.sending}</span>
                        </>
                      ) : (
                        <>
                          <PaperAirplaneIcon className="w-5 h-5" />
                          <span>{currentContent.sendButton}</span>
                        </>
                      )}
                    </button>
                  </form>
                )}
              </div>
            </div>
          </div>
        </section>

        {/* FAQ Section */}
        <section className="py-16 bg-gray-50 dark:bg-gray-900">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
                {currentContent.faq}
              </h2>
              <p className="text-lg text-gray-600 dark:text-gray-300">
                {currentContent.faqSubtitle}
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div className="bg-white dark:bg-gray-800 rounded-lg p-6">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                  {currentContent.faqQ1}
                </h3>
                <p className="text-gray-600 dark:text-gray-300">
                  {currentContent.faqA1}
                </p>
              </div>

              <div className="bg-white dark:bg-gray-800 rounded-lg p-6">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                  {currentContent.faqQ2}
                </h3>
                <p className="text-gray-600 dark:text-gray-300">
                  {currentContent.faqA2}
                </p>
              </div>

              <div className="bg-white dark:bg-gray-800 rounded-lg p-6">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                  {currentContent.faqQ3}
                </h3>
                <p className="text-gray-600 dark:text-gray-300">
                  {currentContent.faqA3}
                </p>
              </div>

              <div className="bg-white dark:bg-gray-800 rounded-lg p-6">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                  {currentContent.faqQ4}
                </h3>
                <p className="text-gray-600 dark:text-gray-300">
                  {currentContent.faqA4}
                </p>
              </div>
            </div>
          </div>
        </section>
      </main>

      <Footer />
    </div>
  )
} 