'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import Header from '@/components/Header'
import Footer from '@/components/Footer'
import { useTranslations } from '@/hooks/useTranslations'
import PreVerificationModal from '@/components/PreVerificationModal'
import { 
  BuildingStorefrontIcon,
  DocumentTextIcon,
  PhoneIcon,
  MapPinIcon,
  CameraIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  ArrowRightIcon,
  ArrowLeftIcon,
  ClockIcon
} from '@heroicons/react/24/outline'

interface FormData {
  // Step 1: Manager Information
  managerName: string
  managerPhone: string
  managerEmail: string
  nationalIdNumber: string
  nationalIdFront: File | null
  nationalIdBack: File | null
  
  // Step 2: Restaurant Information
  restaurantName: string
  restaurantPhone: string
  restaurantEmail: string
  description: string
  cuisineTypes: string[]
  
  // Step 3: Location & Address
  address: string
  city: string
  province: string
  postalCode: string
  latitude: number | null
  longitude: number | null
  
  // Step 4: Business Documents (Optional)
  businessLicense: File | null
  
  // Step 5: Additional Information
  operatingHours: {
    [key: string]: { open: string; close: string; isOpen: boolean }
  }
  deliveryRadius: number
  minimumOrder: number
  estimatedDeliveryTime: string
}

export default function RegisterRestaurantPage() {
  const { language } = useTranslations()
  const router = useRouter()
  const [currentStep, setCurrentStep] = useState(1)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [submitted, setSubmitted] = useState(false)
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [showPreVerification, setShowPreVerification] = useState(true)
  const [isManagerVerified, setIsManagerVerified] = useState(false)

  const [formData, setFormData] = useState<FormData>({
    managerName: '',
    managerPhone: '',
    managerEmail: '',
    nationalIdNumber: '',
    nationalIdFront: null,
    nationalIdBack: null,
    restaurantName: '',
    restaurantPhone: '',
    restaurantEmail: '',
    description: '',
    cuisineTypes: [],
    address: '',
    city: '',
    province: '',
    postalCode: '',
    latitude: null,
    longitude: null,
    businessLicense: null,
    operatingHours: {
      saturday: { open: '09:00', close: '22:00', isOpen: true },
      sunday: { open: '09:00', close: '22:00', isOpen: true },
      monday: { open: '09:00', close: '22:00', isOpen: true },
      tuesday: { open: '09:00', close: '22:00', isOpen: true },
      wednesday: { open: '09:00', close: '22:00', isOpen: true },
      thursday: { open: '09:00', close: '22:00', isOpen: true },
      friday: { open: '14:00', close: '22:00', isOpen: true },
    },
    deliveryRadius: 5,
    minimumOrder: 50,
    estimatedDeliveryTime: '30-45'
  })

  const content = {
    fa: {
      title: 'ثبت رستوران جدید',
      subtitle: 'رستوران خود را در اذکیا ثبت کنید و به هزاران مشتری دسترسی پیدا کنید',
      steps: [
        'اطلاعات مدیر',
        'اطلاعات رستوران', 
        'موقعیت و آدرس',
        'مدارک کسب‌وکار',
        'جزئیات تکمیلی'
      ],
      managerInfo: 'اطلاعات مدیر رستوران',
      managerName: 'نام و نام خانوادگی مدیر',
      managerPhone: 'شماره تلفن مدیر',
      managerEmail: 'ایمیل مدیر',
      nationalIdNumber: 'نمبر تذکره',
      nationalId: 'تصویر تذکره/شناسنامه ملی',
      nationalIdFront: 'روی تذکره',
      nationalIdBack: 'پشت تذکره',
      nationalIdRequired: '* آپلود تذکره الزامی است',
      restaurantInfo: 'اطلاعات رستوران',
      restaurantName: 'نام رستوران',
      restaurantPhone: 'شماره تلفن رستوران',
      restaurantEmail: 'ایمیل رستوران',
      description: 'توضیحات رستوران',
      cuisineTypes: 'نوع غذاها',
      location: 'موقعیت و آدرس',
      fullAddress: 'آدرس کامل',
      city: 'شهر',
      province: 'استان',
      postalCode: 'کد پستی',
      selectLocation: 'انتخاب موقعیت روی نقشه',
      businessDocs: 'مدارک کسب‌وکار',
      businessLicense: 'جواز کسب‌وکار',
      businessLicenseOptional: '* جواز کسب‌وکار اختیاری است',
      additionalInfo: 'جزئیات تکمیلی',
      operatingHours: 'ساعات کاری',
      deliveryRadius: 'شعاع تحویل (کیلومتر)',
      minimumOrder: 'حداقل سفارش (افغانی)',
      estimatedDeliveryTime: 'زمان تقریبی تحویل',
      phoneValidation: 'شماره تلفن معتبر افغانستان وارد کنید (مثال: +93 70 123 4567 یا ************)',
      requiredField: 'این فیلد الزامی است',
      invalidEmail: 'ایمیل معتبر وارد کنید',
      invalidFile: 'فایل انتخابی معتبر نیست',
      next: 'مرحله بعد',
      previous: 'مرحله قبل', 
      submit: 'ثبت درخواست',
      submitting: 'در حال ثبت...',
      success: 'درخواست شما با موفقیت ثبت شد!',
      successMessage: 'درخواست ثبت رستوران شما دریافت شد. تیم ما در عرض ۲۴ ساعت بررسی و با شما تماس خواهد گرفت.',
      days: {
        saturday: 'شنبه',
        sunday: 'یکشنبه', 
        monday: 'دوشنبه',
        tuesday: 'سه‌شنبه',
        wednesday: 'چهارشنبه',
        thursday: 'پنج‌شنبه',
        friday: 'جمعه'
      },
      cuisineOptions: [
        'غذاهای افغانی',
        'غذاهای ایرانی',
        'غذاهای هندی',
        'غذاهای عربی',
        'فست فود',
        'پیتزا',
        'کباب',
        'غذاهای سنتی',
        'غذاهای گیاهی',
        'دریایی'
      ]
    },
    en: {
      title: 'Register New Restaurant',
      subtitle: 'Register your restaurant on Azkuja and reach thousands of customers',
      steps: [
        'Manager Info',
        'Restaurant Info',
        'Location & Address', 
        'Business Documents',
        'Additional Details'
      ],
      managerInfo: 'Restaurant Manager Information',
      managerName: 'Manager Full Name',
      managerPhone: 'Manager Phone Number',
      managerEmail: 'Manager Email',
      nationalIdNumber: 'National ID Number',
      nationalId: 'National ID/Tazkira Photo',
      nationalIdFront: 'Front of ID',
      nationalIdBack: 'Back of ID',
      nationalIdRequired: '* National ID upload is required',
      restaurantInfo: 'Restaurant Information',
      restaurantName: 'Restaurant Name',
      restaurantPhone: 'Restaurant Phone Number',
      restaurantEmail: 'Restaurant Email',
      description: 'Restaurant Description',
      cuisineTypes: 'Cuisine Types',
      location: 'Location & Address',
      fullAddress: 'Full Address',
      city: 'City',
      province: 'Province',
      postalCode: 'Postal Code',
      selectLocation: 'Select Location on Map',
      businessDocs: 'Business Documents',
      businessLicense: 'Business License',
      businessLicenseOptional: '* Business license is optional',
      additionalInfo: 'Additional Details',
      operatingHours: 'Operating Hours',
      deliveryRadius: 'Delivery Radius (km)',
      minimumOrder: 'Minimum Order (AFN)',
      estimatedDeliveryTime: 'Estimated Delivery Time',
      phoneValidation: 'Enter valid Afghanistan phone number (example: +93 70 123 4567 or ************)',
      requiredField: 'This field is required',
      invalidEmail: 'Enter valid email',
      invalidFile: 'Selected file is not valid',
      next: 'Next',
      previous: 'Previous',
      submit: 'Submit Request',
      submitting: 'Submitting...',
      success: 'Your request has been submitted successfully!',
      successMessage: 'Your restaurant registration request has been received. Our team will review and contact you within 24 hours.',
      days: {
        saturday: 'Saturday',
        sunday: 'Sunday',
        monday: 'Monday', 
        tuesday: 'Tuesday',
        wednesday: 'Wednesday',
        thursday: 'Thursday',
        friday: 'Friday'
      },
      cuisineOptions: [
        'Afghan Food',
        'Iranian Food',
        'Indian Food',
        'Arabic Food',
        'Fast Food',
        'Pizza',
        'Kebab',
        'Traditional Food',
        'Vegetarian',
        'Seafood'
      ]
    },
    ps: {
      title: 'د نوي رستوران ثبت',
      subtitle: 'خپل رستوران په اذکیا کې ثبت کړئ او د زرګونو پیرودونکو ته لاسرسی ومومئ',
      steps: [
        'د مدیر معلومات',
        'د رستوران معلومات',
        'موقعیت او پته',
        'د سوداګرۍ اسناد',
        'اضافي جزئیات'
      ],
      managerInfo: 'د رستوران د مدیر معلومات',
      managerName: 'د مدیر بشپړ نوم',
      managerPhone: 'د مدیر د تلیفون شمیره',
      managerEmail: 'د مدیر ایمیل',
      nationalIdNumber: 'د ملي تذکرې شمیره',
      nationalId: 'د ملي تذکرې انځور',
      nationalIdFront: 'د تذکرې مخ',
      nationalIdBack: 'د تذکرې شا',
      nationalIdRequired: '* د ملي تذکرې اپلوډ اړین دی',
      restaurantInfo: 'د رستوران معلومات',
      restaurantName: 'د رستوران نوم',
      restaurantPhone: 'د رستوران د تلیفون شمیره',
      restaurantEmail: 'د رستوران ایمیل',
      description: 'د رستوران توضیحات',
      cuisineTypes: 'د خوړو ډولونه',
      location: 'موقعیت او پته',
      fullAddress: 'بشپړه پته',
      city: 'ښار',
      province: 'ولایت',
      postalCode: 'د پوستې کوډ',
      selectLocation: 'په نقشه کې موقعیت وټاکئ',
      businessDocs: 'د سوداګرۍ اسناد',
      businessLicense: 'د سوداګرۍ جواز',
      businessLicenseOptional: '* د سوداګرۍ جواز اختیاري دی',
      additionalInfo: 'اضافي جزئیات',
      operatingHours: 'د کار ساعتونه',
      deliveryRadius: 'د رسولو شعاع (کیلومتره)',
      minimumOrder: 'لږترلږه امر (افغانۍ)',
      estimatedDeliveryTime: 'د رسولو اټکل شوی وخت',
      phoneValidation: 'د افغانستان د اعتبار وړ تلیفون شمیره ولیکئ (مثال: +93 70 123 4567 یا ************)',
      requiredField: 'دا ساحه اړینه ده',
      invalidEmail: 'د اعتبار وړ ایمیل ولیکئ',
      invalidFile: 'ټاکل شوی فایل د اعتبار وړ نه دی',
      next: 'بل',
      previous: 'مخکنی',
      submit: 'غوښتنه وسپارئ',
      submitting: 'سپارل کیږي...',
      success: 'ستاسو غوښتنه په بریالیتوب سره وسپارل شوه!',
      successMessage: 'ستاسو د رستوران د ثبت غوښتنه ترلاسه شوه. زموږ ټیم به د ۲۴ ساعتونو په اوږدو کې بیاکتنه وکړي او تاسو سره به اړیکه ونیسي.',
      days: {
        saturday: 'شنبه',
        sunday: 'یکشنبه',
        monday: 'دوشنبه',
        tuesday: 'درې شنبه',
        wednesday: 'څلورشنبه',
        thursday: 'پینځشنبه',
        friday: 'جمعه'
      },
      cuisineOptions: [
        'افغاني خواړه',
        'ایراني خواړه', 
        'هندي خواړه',
        'عربي خواړه',
        'ګړندي خواړه',
        'پیزا',
        'کباب',
        'دودیز خواړه',
        'نباتي خواړه',
        'بحري خواړه'
      ]
    }
  }

  const currentContent = content[language] || content.fa

  // Phone number validation for Afghanistan
  const validateAfghanPhone = (phone: string): boolean => {
    const cleanPhone = phone.replace(/\s/g, '')
    
    // Afghanistan phone number patterns:
    // International format: +93 7X XXX XXXX, +93 07X XXX XXXX
    // National format: 07X XXX XXXX, 7X XXX XXXX
    // Valid prefixes: 70, 71, 72, 73, 74, 75, 76, 77, 78, 79 (mobile)
    // Also accepts: 07X for national format
    
    const afghanPhoneRegex = /^(\+93|0093|93)?(0?[7][0-9])\d{7}$/
    return afghanPhoneRegex.test(cleanPhone)
  }

  // Email validation
  const validateEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
  }

  // National ID number validation (13 digits in format: xxxx-xxxx-xxxxx)
  const validateNationalId = (nationalId: string): boolean => {
    const cleanId = nationalId.replace(/[-\s]/g, '')
    return /^\d{13}$/.test(cleanId)
  }

  // Format National ID number with dashes
  const formatNationalId = (value: string): string => {
    const cleanValue = value.replace(/[-\s]/g, '').replace(/\D/g, '')
    if (cleanValue.length <= 4) return cleanValue
    if (cleanValue.length <= 8) return `${cleanValue.slice(0, 4)}-${cleanValue.slice(4)}`
    return `${cleanValue.slice(0, 4)}-${cleanValue.slice(4, 8)}-${cleanValue.slice(8, 13)}`
  }

  // File validation
  const validateFile = (file: File, maxSize: number = 5): boolean => {
    const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'application/pdf']
    const maxSizeBytes = maxSize * 1024 * 1024 // Convert MB to bytes
    return validTypes.includes(file.type) && file.size <= maxSizeBytes
  }

  // Step validation
  const validateStep = (step: number): boolean => {
    const newErrors: Record<string, string> = {}

    switch (step) {
      case 1:
        if (!formData.managerName.trim()) newErrors.managerName = currentContent.requiredField
        if (!formData.managerPhone.trim()) newErrors.managerPhone = currentContent.requiredField
        else if (!validateAfghanPhone(formData.managerPhone)) newErrors.managerPhone = currentContent.phoneValidation
        if (!formData.managerEmail.trim()) newErrors.managerEmail = currentContent.requiredField
        else if (!validateEmail(formData.managerEmail)) newErrors.managerEmail = currentContent.invalidEmail
        if (!formData.nationalIdNumber.trim()) newErrors.nationalIdNumber = currentContent.requiredField
        else if (!validateNationalId(formData.nationalIdNumber)) newErrors.nationalIdNumber = 'نمبر تذکره باید ۱۳ رقم باشد (مثال: 1234-5678-90123)'
        if (!formData.nationalIdFront) newErrors.nationalIdFront = currentContent.requiredField
        if (!formData.nationalIdBack) newErrors.nationalIdBack = currentContent.requiredField
        break
      
      case 2:
        if (!formData.restaurantName.trim()) newErrors.restaurantName = currentContent.requiredField
        if (!formData.restaurantPhone.trim()) newErrors.restaurantPhone = currentContent.requiredField
        else if (!validateAfghanPhone(formData.restaurantPhone)) newErrors.restaurantPhone = currentContent.phoneValidation
        if (formData.restaurantEmail && !validateEmail(formData.restaurantEmail)) newErrors.restaurantEmail = currentContent.invalidEmail
        if (!formData.description.trim()) newErrors.description = currentContent.requiredField
        if (formData.cuisineTypes.length === 0) newErrors.cuisineTypes = currentContent.requiredField
        break
        
      case 3:
        if (!formData.address.trim()) newErrors.address = currentContent.requiredField
        if (!formData.city.trim()) newErrors.city = currentContent.requiredField
        if (!formData.province.trim()) newErrors.province = currentContent.requiredField
        if (formData.latitude === null || formData.longitude === null) newErrors.location = 'موقعیت روی نقشه انتخاب کنید'
        break
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleInputChange = (field: keyof FormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }))
    }
  }

  const handleFileChange = (field: keyof FormData, file: File | null) => {
    if (file && !validateFile(file)) {
      setErrors(prev => ({ ...prev, [field]: currentContent.invalidFile }))
      return
    }
    handleInputChange(field, file)
  }

  const handleManagerVerified = (managerName: string, managerPhone: string) => {
    // Pre-populate verified manager data
    setFormData(prev => ({
      ...prev,
      managerName,
      managerPhone
    }))
    setIsManagerVerified(true)
    setShowPreVerification(false)
  }

  const handleNext = () => {
    if (validateStep(currentStep)) {
      setCurrentStep(prev => prev + 1)
    }
  }

  const handlePrevious = () => {
    setCurrentStep(prev => prev - 1)
  }

  const handleSubmit = async () => {
    setIsSubmitting(true)
    
    try {
      // Create FormData for file upload
      const submitData = new FormData()
      
      // Add text fields
      submitData.append('managerName', formData.managerName)
      submitData.append('managerPhone', formData.managerPhone)
      submitData.append('managerEmail', formData.managerEmail)
      submitData.append('nationalIdNumber', formData.nationalIdNumber)
      submitData.append('restaurantName', formData.restaurantName)
      submitData.append('restaurantPhone', formData.restaurantPhone)
      if (formData.restaurantEmail) {
        submitData.append('restaurantEmail', formData.restaurantEmail)
      }
      submitData.append('description', formData.description)
      submitData.append('address', formData.address)
      submitData.append('city', formData.city)
      submitData.append('province', formData.province)
      if (formData.postalCode) {
        submitData.append('postalCode', formData.postalCode)
      }
      submitData.append('estimatedDeliveryTime', formData.estimatedDeliveryTime)

      // Add arrays as JSON strings
      submitData.append('cuisineTypes', JSON.stringify(formData.cuisineTypes))
      submitData.append('operatingHours', JSON.stringify(formData.operatingHours))

      // Add numbers
      if (formData.latitude !== null && formData.longitude !== null) {
        submitData.append('latitude', formData.latitude.toString())
        submitData.append('longitude', formData.longitude.toString())
      } else {
        // This should not happen due to validation, but provide fallback
        throw new Error('Location coordinates are required')
      }
      submitData.append('deliveryRadius', formData.deliveryRadius.toString())
      submitData.append('minimumOrder', formData.minimumOrder.toString())

      // Add files
      if (formData.nationalIdFront) {
        submitData.append('nationalIdFront', formData.nationalIdFront)
      }
      if (formData.nationalIdBack) {
        submitData.append('nationalIdBack', formData.nationalIdBack)
      }
      if (formData.businessLicense) {
        submitData.append('businessLicense', formData.businessLicense)
      }

      // Submit to backend API
      const response = await fetch('http://localhost:7000/api/restaurant-registration/submit', {
        method: 'POST',
        body: submitData, // FormData automatically sets correct Content-Type with boundary
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ message: 'Network error' }))
        throw new Error(errorData.message || 'Failed to submit registration')
      }

      const result = await response.json()
      console.log('Registration submitted successfully:', result)
      
      setSubmitted(true)
    } catch (error) {
      console.error('Submission error:', error)
      // Show error to user
      const errorMessage = language === 'en' 
        ? 'Error submitting registration. Please try again.' 
        : language === 'ps' 
        ? 'د ثبت نوم کې ستونزه. بیا هڅه وکړئ.'
        : 'خطا در ثبت درخواست. لطفاً دوباره تلاش کنید.'
      
      alert(errorMessage)
    } finally {
      setIsSubmitting(false)
    }
  }

  // Get current location
  const getCurrentLocation = () => {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          handleInputChange('latitude', position.coords.latitude)
          handleInputChange('longitude', position.coords.longitude)
        },
        (error) => {
          console.error('Location error:', error)
        }
      )
    }
  }

  if (submitted) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <Header />
        <main className="py-16">
          <div className="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <CheckCircleIcon className="w-16 h-16 text-green-600 mx-auto mb-6" />
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
              {currentContent.success}
            </h1>
            <p className="text-lg text-gray-600 dark:text-gray-300 mb-8">
              {currentContent.successMessage}
            </p>
            <button
              onClick={() => router.push('/')}
              className="bg-primary-600 hover:bg-primary-700 text-white px-8 py-3 rounded-lg font-semibold transition-colors"
            >
              {language === 'en' ? 'Back to Home' : language === 'ps' ? 'کور ته بیرته' : 'بازگشت به خانه'}
            </button>
          </div>
        </main>
        <Footer />
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <Header />
      
      <main>
        {/* Hero Section */}
        <section className="bg-gradient-to-br from-primary-50 to-orange-50 dark:from-gray-800 dark:to-gray-900 py-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center">
              <BuildingStorefrontIcon className="w-16 h-16 text-primary-600 mx-auto mb-6" />
              <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-6">
                {currentContent.title}
              </h1>
              <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                {currentContent.subtitle}
              </p>
            </div>
          </div>
        </section>

        {/* Progress Steps */}
        <section className="py-8 bg-white dark:bg-gray-800">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex items-center justify-between">
              {currentContent.steps.map((step, index) => (
                <div key={index} className="flex items-center">
                  <div className={`flex items-center justify-center w-10 h-10 rounded-full border-2 ${
                    index + 1 <= currentStep 
                      ? 'bg-primary-600 border-primary-600 text-white' 
                      : 'border-gray-300 text-gray-500'
                  }`}>
                    {index + 1 < currentStep ? (
                      <CheckCircleIcon className="w-6 h-6" />
                    ) : (
                      <span className="text-sm font-semibold">{index + 1}</span>
                    )}
                  </div>
                  <span className={`ml-3 rtl:mr-3 rtl:ml-0 text-sm font-medium ${
                    index + 1 <= currentStep ? 'text-primary-600' : 'text-gray-500'
                  }`}>
                    {step}
                  </span>
                  {index < currentContent.steps.length - 1 && (
                    <div className={`w-16 h-0.5 mx-4 ${
                      index + 1 < currentStep ? 'bg-primary-600' : 'bg-gray-300'
                    }`} />
                  )}
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Form Section */}
        <section className="py-16 bg-white dark:bg-gray-800">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="bg-white dark:bg-gray-700 rounded-lg shadow-lg p-8">
              
              {/* Step 1: Manager Information */}
              {currentStep === 1 && (
                <div>
                  <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
                    {currentContent.managerInfo}
                  </h2>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    <div>
                      <label className="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        {currentContent.managerName} *
                        {isManagerVerified && (
                          <CheckCircleIcon className="w-5 h-5 text-green-500 ml-2" />
                        )}
                      </label>
                      <input
                        type="text"
                        value={formData.managerName}
                        onChange={(e) => handleInputChange('managerName', e.target.value)}
                        disabled={isManagerVerified}
                        className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent ${
                          errors.managerName ? 'border-red-500' : 'border-gray-300'
                        } ${isManagerVerified ? 'bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-300' : 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white'}`}
                        placeholder={language === 'en' ? 'Enter manager full name' : language === 'ps' ? 'د مدیر بشپړ نوم ولیکئ' : 'نام و نام خانوادگی مدیر را وارد کنید'}
                      />
                      {isManagerVerified && (
                        <p className="text-green-600 text-sm mt-1 flex items-center">
                          <CheckCircleIcon className="w-4 h-4 mr-1" />
                          {language === 'en' ? 'Verified' : language === 'ps' ? 'تصدیق شوی' : 'تأیید شده'}
                        </p>
                      )}
                      {errors.managerName && <p className="text-red-500 text-sm mt-1">{errors.managerName}</p>}
                    </div>

                    <div>
                      <label className="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        {currentContent.managerPhone} *
                        {isManagerVerified && (
                          <CheckCircleIcon className="w-5 h-5 text-green-500 ml-2" />
                        )}
                      </label>
                      <input
                        type="tel"
                        value={formData.managerPhone}
                        onChange={(e) => handleInputChange('managerPhone', e.target.value)}
                        disabled={isManagerVerified}
                        className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent ${
                          errors.managerPhone ? 'border-red-500' : 'border-gray-300'
                        } ${isManagerVerified ? 'bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-300' : 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white'}`}
                        placeholder="+93 70 123 4567"
                      />
                      {isManagerVerified && (
                        <p className="text-green-600 text-sm mt-1 flex items-center">
                          <CheckCircleIcon className="w-4 h-4 mr-1" />
                          {language === 'en' ? 'Verified' : language === 'ps' ? 'تصدیق شوی' : 'تأیید شده'}
                        </p>
                      )}
                      {errors.managerPhone && <p className="text-red-500 text-sm mt-1">{errors.managerPhone}</p>}
                    </div>
                  </div>

                  {/* Email and National ID Number in one row */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        {currentContent.managerEmail} *
                      </label>
                      <input
                        type="email"
                        value={formData.managerEmail}
                        onChange={(e) => handleInputChange('managerEmail', e.target.value)}
                        className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent ${
                          errors.managerEmail ? 'border-red-500' : 'border-gray-300'
                        } bg-white dark:bg-gray-600 text-gray-900 dark:text-white`}
                        placeholder="<EMAIL>"
                      />
                      {errors.managerEmail && <p className="text-red-500 text-sm mt-1">{errors.managerEmail}</p>}
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        {currentContent.nationalIdNumber} *
                      </label>
                      <input
                        type="text"
                        value={formData.nationalIdNumber}
                        onChange={(e) => {
                          const formatted = formatNationalId(e.target.value)
                          if (formatted.replace(/[-\s]/g, '').length <= 13) {
                            handleInputChange('nationalIdNumber', formatted)
                          }
                        }}
                        className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent ${
                          errors.nationalIdNumber ? 'border-red-500' : 'border-gray-300'
                        } bg-white dark:bg-gray-600 text-gray-900 dark:text-white`}
                        placeholder={language === 'en' ? '1234-5678-90123' : language === 'ps' ? '۱۲۳۴-۵۶۷۸-۹۰۱۲۳' : '۱۲۳۴-۵۶۷۸-۹۰۱۲۳'}
                        maxLength={15} // 13 digits + 2 dashes
                      />
                      {errors.nationalIdNumber && <p className="text-red-500 text-sm mt-1">{errors.nationalIdNumber}</p>}
                    </div>
                  </div>

                  {/* National ID Upload */}
                  <div className="mb-6">
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      {currentContent.nationalId} *
                    </label>
                    <p className="text-sm text-red-600 mb-4">{currentContent.nationalIdRequired}</p>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-600 mb-2">
                          {currentContent.nationalIdFront}
                        </label>
                        <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center hover:border-primary-500 transition-colors">
                          <input
                            type="file"
                            accept="image/*,.pdf"
                            capture="environment"
                            onChange={(e) => handleFileChange('nationalIdFront', e.target.files?.[0] || null)}
                            className="hidden"
                            id="nationalIdFront"
                          />
                          <label htmlFor="nationalIdFront" className="cursor-pointer">
                            <CameraIcon className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                            <p className="text-sm text-gray-600 mb-2">
                              {formData.nationalIdFront 
                                ? formData.nationalIdFront.name 
                                : (language === 'en' ? 'Take photo or upload' : language === 'ps' ? 'عکس واخلئ یا اپلوډ کړئ' : 'عکس بگیرید یا آپلود کنید')
                              }
                            </p>
                            <p className="text-xs text-gray-500">
                              {language === 'en' ? 'Camera • Gallery • PDF' : language === 'ps' ? 'کامره • ګیلري • PDF' : 'دوربین • گالری • PDF'}
                            </p>
                          </label>
                        </div>
                        {errors.nationalIdFront && <p className="text-red-500 text-sm mt-1">{errors.nationalIdFront}</p>}
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-600 mb-2">
                          {currentContent.nationalIdBack}
                        </label>
                        <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center hover:border-primary-500 transition-colors">
                          <input
                            type="file"
                            accept="image/*,.pdf"
                            capture="environment"
                            onChange={(e) => handleFileChange('nationalIdBack', e.target.files?.[0] || null)}
                            className="hidden"
                            id="nationalIdBack"
                          />
                          <label htmlFor="nationalIdBack" className="cursor-pointer">
                            <CameraIcon className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                            <p className="text-sm text-gray-600 mb-2">
                              {formData.nationalIdBack 
                                ? formData.nationalIdBack.name 
                                : (language === 'en' ? 'Take photo or upload' : language === 'ps' ? 'عکس واخلئ یا اپلوډ کړئ' : 'عکس بگیرید یا آپلود کنید')
                              }
                            </p>
                            <p className="text-xs text-gray-500">
                              {language === 'en' ? 'Camera • Gallery • PDF' : language === 'ps' ? 'کامره • ګیلري • PDF' : 'دوربین • گالری • PDF'}
                            </p>
                          </label>
                        </div>
                        {errors.nationalIdBack && <p className="text-red-500 text-sm mt-1">{errors.nationalIdBack}</p>}
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Step 2: Restaurant Information */}
              {currentStep === 2 && (
                <div>
                  <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
                    {currentContent.restaurantInfo}
                  </h2>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        {currentContent.restaurantName} *
                      </label>
                      <input
                        type="text"
                        value={formData.restaurantName}
                        onChange={(e) => handleInputChange('restaurantName', e.target.value)}
                        className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent ${
                          errors.restaurantName ? 'border-red-500' : 'border-gray-300'
                        } bg-white dark:bg-gray-600 text-gray-900 dark:text-white`}
                        placeholder={language === 'en' ? 'Enter restaurant name' : language === 'ps' ? 'د رستوران نوم ولیکئ' : 'نام رستوران را وارد کنید'}
                      />
                      {errors.restaurantName && <p className="text-red-500 text-sm mt-1">{errors.restaurantName}</p>}
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        {currentContent.restaurantPhone} *
                      </label>
                      <input
                        type="tel"
                        value={formData.restaurantPhone}
                        onChange={(e) => handleInputChange('restaurantPhone', e.target.value)}
                        className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent ${
                          errors.restaurantPhone ? 'border-red-500' : 'border-gray-300'
                        } bg-white dark:bg-gray-600 text-gray-900 dark:text-white`}
                        placeholder="+93 70 123 4567"
                      />
                      {errors.restaurantPhone && <p className="text-red-500 text-sm mt-1">{errors.restaurantPhone}</p>}
                    </div>
                  </div>

                  <div className="mb-6">
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      {currentContent.restaurantEmail}
                    </label>
                    <input
                      type="email"
                      value={formData.restaurantEmail}
                      onChange={(e) => handleInputChange('restaurantEmail', e.target.value)}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-600 text-gray-900 dark:text-white"
                      placeholder="<EMAIL>"
                    />
                  </div>

                  <div className="mb-6">
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      {currentContent.description} *
                    </label>
                    <textarea
                      value={formData.description}
                      onChange={(e) => handleInputChange('description', e.target.value)}
                      rows={4}
                      className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent ${
                        errors.description ? 'border-red-500' : 'border-gray-300'
                      } bg-white dark:bg-gray-600 text-gray-900 dark:text-white`}
                      placeholder={language === 'en' ? 'Describe your restaurant...' : language === 'ps' ? 'خپل رستوران تشریح کړئ...' : 'رستوران خود را توصیف کنید...'}
                    />
                    {errors.description && <p className="text-red-500 text-sm mt-1">{errors.description}</p>}
                  </div>

                  <div className="mb-6">
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      {currentContent.cuisineTypes} *
                    </label>
                    <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                      {currentContent.cuisineOptions.map((cuisine, index) => (
                        <label key={index} className="flex items-center">
                          <input
                            type="checkbox"
                            checked={formData.cuisineTypes.includes(cuisine)}
                            onChange={(e) => {
                              if (e.target.checked) {
                                handleInputChange('cuisineTypes', [...formData.cuisineTypes, cuisine])
                              } else {
                                handleInputChange('cuisineTypes', formData.cuisineTypes.filter(c => c !== cuisine))
                              }
                            }}
                            className="rounded border-gray-300 text-primary-600 shadow-sm focus:ring-primary-500"
                          />
                          <span className="ml-2 rtl:mr-2 rtl:ml-0 text-sm text-gray-700 dark:text-gray-300">
                            {cuisine}
                          </span>
                        </label>
                      ))}
                    </div>
                    {errors.cuisineTypes && <p className="text-red-500 text-sm mt-1">{errors.cuisineTypes}</p>}
                  </div>
                </div>
              )}

              {/* Step 3: Location & Address */}
              {currentStep === 3 && (
                <div>
                  <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
                    {currentContent.location}
                  </h2>

                  <div className="mb-6">
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      {currentContent.fullAddress} *
                    </label>
                    <textarea
                      value={formData.address}
                      onChange={(e) => handleInputChange('address', e.target.value)}
                      rows={3}
                      className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent ${
                        errors.address ? 'border-red-500' : 'border-gray-300'
                      } bg-white dark:bg-gray-600 text-gray-900 dark:text-white`}
                      placeholder={language === 'en' ? 'Enter complete address...' : language === 'ps' ? 'بشپړه پته ولیکئ...' : 'آدرس کامل را وارد کنید...'}
                    />
                    {errors.address && <p className="text-red-500 text-sm mt-1">{errors.address}</p>}
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        {currentContent.city} *
                      </label>
                      <input
                        type="text"
                        value={formData.city}
                        onChange={(e) => handleInputChange('city', e.target.value)}
                        className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent ${
                          errors.city ? 'border-red-500' : 'border-gray-300'
                        } bg-white dark:bg-gray-600 text-gray-900 dark:text-white`}
                        placeholder={language === 'en' ? 'City' : language === 'ps' ? 'ښار' : 'شهر'}
                      />
                      {errors.city && <p className="text-red-500 text-sm mt-1">{errors.city}</p>}
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        {currentContent.province} *
                      </label>
                      <input
                        type="text"
                        value={formData.province}
                        onChange={(e) => handleInputChange('province', e.target.value)}
                        className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent ${
                          errors.province ? 'border-red-500' : 'border-gray-300'
                        } bg-white dark:bg-gray-600 text-gray-900 dark:text-white`}
                        placeholder={language === 'en' ? 'Province' : language === 'ps' ? 'ولایت' : 'استان'}
                      />
                      {errors.province && <p className="text-red-500 text-sm mt-1">{errors.province}</p>}
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        {currentContent.postalCode}
                      </label>
                      <input
                        type="text"
                        value={formData.postalCode}
                        onChange={(e) => handleInputChange('postalCode', e.target.value)}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-600 text-gray-900 dark:text-white"
                        placeholder={language === 'en' ? 'Postal Code' : language === 'ps' ? 'د پوستې کوډ' : 'کد پستی'}
                      />
                    </div>
                  </div>

                  {/* GPS Location */}
                  <div className="mb-6">
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      {currentContent.selectLocation} *
                    </label>
                    <div className="border border-gray-300 rounded-lg p-4 bg-gray-50 dark:bg-gray-600">
                      <div className="flex items-center justify-between mb-4">
                        <div className="flex items-center">
                          <MapPinIcon className="w-5 h-5 text-primary-600 ml-2 rtl:mr-2 rtl:ml-0" />
                          <span className="text-sm text-gray-600 dark:text-gray-300">
                            {formData.latitude && formData.longitude 
                              ? `${formData.latitude.toFixed(6)}, ${formData.longitude.toFixed(6)}`
                              : (language === 'en' ? 'No location selected' : language === 'ps' ? 'هیڅ ځای نه دی ټاکل شوی' : 'موقعیت انتخاب نشده')
                            }
                          </span>
                        </div>
                        <button
                          type="button"
                          onClick={getCurrentLocation}
                          className="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors"
                        >
                          {language === 'en' ? 'Get Current Location' : language === 'ps' ? 'اوسنی موقعیت ترلاسه کړئ' : 'دریافت موقعیت فعلی'}
                        </button>
                      </div>
                      <p className="text-xs text-gray-500">
                        {language === 'en' 
                          ? 'Click the button above to get your current location, or manually enter coordinates'
                          : language === 'ps'
                          ? 'د خپل اوسني موقعیت ترلاسه کولو لپاره پورتنۍ تڼۍ کلیک کړئ، یا په لاسي ډول احداثیې ولیکئ'
                          : 'برای دریافت موقعیت فعلی روی دکمه بالا کلیک کنید یا مختصات را به صورت دستی وارد کنید'
                        }
                      </p>
                    </div>
                    {errors.location && <p className="text-red-500 text-sm mt-1">{errors.location}</p>}
                  </div>
                </div>
              )}

              {/* Step 4: Business Documents */}
              {currentStep === 4 && (
                <div>
                  <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
                    {currentContent.businessDocs}
                  </h2>

                  <div className="mb-6">
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      {currentContent.businessLicense}
                    </label>
                    <p className="text-sm text-gray-600 mb-4">{currentContent.businessLicenseOptional}</p>
                    
                    <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-primary-500 transition-colors">
                      <input
                        type="file"
                        accept="image/*,.pdf"
                        capture="environment"
                        onChange={(e) => handleFileChange('businessLicense', e.target.files?.[0] || null)}
                        className="hidden"
                        id="businessLicense"
                      />
                      <label htmlFor="businessLicense" className="cursor-pointer">
                        <DocumentTextIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                        <p className="text-lg font-medium text-gray-600 mb-2">
                          {formData.businessLicense 
                            ? formData.businessLicense.name
                            : (language === 'en' ? 'Take photo or upload license' : language === 'ps' ? 'د جواز عکس واخلئ یا اپلوډ کړئ' : 'عکس بگیرید یا جواز آپلود کنید')
                          }
                        </p>
                        <p className="text-sm text-gray-500">
                          {language === 'en' 
                            ? 'Camera • Gallery • PDF • Up to 5MB'
                            : language === 'ps'
                            ? 'کامره • ګیلري • PDF • تر ۵MB پورې'
                            : 'دوربین • گالری • PDF • تا ۵ مگابایت'
                          }
                        </p>
                      </label>
                    </div>
                  </div>

                  <div className="bg-blue-50 dark:bg-blue-900 border border-blue-200 dark:border-blue-700 rounded-lg p-4">
                    <div className="flex items-start">
                      <ExclamationTriangleIcon className="w-6 h-6 text-blue-600 mt-1 ml-3 rtl:mr-3 rtl:ml-0 flex-shrink-0" />
                      <div>
                        <h3 className="text-sm font-medium text-blue-800 dark:text-blue-200 mb-1">
                          {language === 'en' ? 'Important Note' : language === 'ps' ? 'مهمه یادونه' : 'نکته مهم'}
                        </h3>
                        <p className="text-sm text-blue-700 dark:text-blue-300">
                          {language === 'en' 
                            ? 'While business license is optional for registration, having valid business documents will help speed up the approval process and increase customer trust.'
                            : language === 'ps'
                            ? 'که څه هم د سوداګرۍ جواز د ثبت لپاره اختیاري دی، د اعتبار وړ سوداګریز اسنادو لرل به د تصویب پروسه ګړندۍ کړي او د پیرودونکو باور به زیات کړي.'
                            : 'اگرچه جواز کسب‌وکار برای ثبت‌نام اختیاری است، داشتن مدارک معتبر کسب‌وکار به تسریع روند تأیید و افزایش اعتماد مشتریان کمک می‌کند.'
                          }
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Step 5: Additional Information */}
              {currentStep === 5 && (
                <div>
                  <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
                    {currentContent.additionalInfo}
                  </h2>

                  {/* Operating Hours */}
                  <div className="mb-8">
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-4">
                      {currentContent.operatingHours}
                    </label>
                    <div className="space-y-3">
                      {Object.entries(formData.operatingHours).map(([day, hours]) => (
                        <div key={day} className="flex items-center space-x-4 rtl:space-x-reverse">
                          <div className="w-24">
                            <input
                              type="checkbox"
                              checked={hours.isOpen}
                              onChange={(e) => {
                                const newHours = {
                                  ...formData.operatingHours,
                                  [day]: { ...hours, isOpen: e.target.checked }
                                }
                                handleInputChange('operatingHours', newHours)
                              }}
                              className="rounded border-gray-300 text-primary-600 shadow-sm focus:ring-primary-500 ml-2 rtl:mr-2 rtl:ml-0"
                            />
                            <span className="text-sm text-gray-700 dark:text-gray-300">
                              {currentContent.days[day as keyof typeof currentContent.days]}
                            </span>
                          </div>
                          {hours.isOpen && (
                            <div className="flex items-center space-x-2 rtl:space-x-reverse">
                              <input
                                type="time"
                                value={hours.open}
                                onChange={(e) => {
                                  const newHours = {
                                    ...formData.operatingHours,
                                    [day]: { ...hours, open: e.target.value }
                                  }
                                  handleInputChange('operatingHours', newHours)
                                }}
                                className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-600 text-gray-900 dark:text-white"
                              />
                              <span className="text-gray-500">-</span>
                              <input
                                type="time"
                                value={hours.close}
                                onChange={(e) => {
                                  const newHours = {
                                    ...formData.operatingHours,
                                    [day]: { ...hours, close: e.target.value }
                                  }
                                  handleInputChange('operatingHours', newHours)
                                }}
                                className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-600 text-gray-900 dark:text-white"
                              />
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Delivery Settings */}
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        {currentContent.deliveryRadius}
                      </label>
                      <input
                        type="number"
                        value={formData.deliveryRadius}
                        onChange={(e) => handleInputChange('deliveryRadius', parseInt(e.target.value))}
                        min="1"
                        max="50"
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-600 text-gray-900 dark:text-white"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        {currentContent.minimumOrder}
                      </label>
                      <input
                        type="number"
                        value={formData.minimumOrder}
                        onChange={(e) => handleInputChange('minimumOrder', parseInt(e.target.value))}
                        min="0"
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-600 text-gray-900 dark:text-white"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        {currentContent.estimatedDeliveryTime}
                      </label>
                      <select
                        value={formData.estimatedDeliveryTime}
                        onChange={(e) => handleInputChange('estimatedDeliveryTime', e.target.value)}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-600 text-gray-900 dark:text-white"
                      >
                        <option value="15-30">15-30 {language === 'en' ? 'minutes' : language === 'ps' ? 'دقیقې' : 'دقیقه'}</option>
                        <option value="30-45">30-45 {language === 'en' ? 'minutes' : language === 'ps' ? 'دقیقې' : 'دقیقه'}</option>
                        <option value="45-60">45-60 {language === 'en' ? 'minutes' : language === 'ps' ? 'دقیقې' : 'دقیقه'}</option>
                        <option value="60-90">60-90 {language === 'en' ? 'minutes' : language === 'ps' ? 'دقیقې' : 'دقیقه'}</option>
                      </select>
                    </div>
                  </div>
                </div>
              )}

              {/* Navigation Buttons */}
              <div className="flex justify-between pt-8 border-t border-gray-200 dark:border-gray-600">
                <button
                  type="button"
                  onClick={handlePrevious}
                  disabled={currentStep === 1}
                  className={`flex items-center px-6 py-3 rounded-lg font-semibold transition-colors ${
                    currentStep === 1
                      ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                      : 'bg-gray-600 hover:bg-gray-700 text-white'
                  }`}
                >
                  <ArrowRightIcon className="w-5 h-5 ml-2 rtl:mr-2 rtl:ml-0 rtl:rotate-180" />
                  {currentContent.previous}
                </button>

                {currentStep === 5 ? (
                  <button
                    type="button"
                    onClick={handleSubmit}
                    disabled={isSubmitting}
                    className="flex items-center bg-primary-600 hover:bg-primary-700 disabled:bg-primary-400 text-white px-8 py-3 rounded-lg font-semibold transition-colors"
                  >
                    {isSubmitting ? (
                      <>
                        <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2 rtl:ml-2 rtl:mr-0"></div>
                        {currentContent.submitting}
                      </>
                    ) : (
                      <>
                        <CheckCircleIcon className="w-5 h-5 mr-2 rtl:ml-2 rtl:mr-0" />
                        {currentContent.submit}
                      </>
                    )}
                  </button>
                ) : (
                  <button
                    type="button"
                    onClick={handleNext}
                    className="flex items-center bg-primary-600 hover:bg-primary-700 text-white px-6 py-3 rounded-lg font-semibold transition-colors"
                  >
                    {currentContent.next}
                    <ArrowLeftIcon className="w-5 h-5 mr-2 rtl:ml-2 rtl:mr-0 rtl:rotate-180" />
                  </button>
                )}
              </div>
            </div>
          </div>
        </section>
      </main>

      <Footer />

      {/* Pre-verification Modal */}
      <PreVerificationModal
        isOpen={showPreVerification}
        onClose={() => setShowPreVerification(false)}
        onVerified={handleManagerVerified}
      />
    </div>
  )
} 