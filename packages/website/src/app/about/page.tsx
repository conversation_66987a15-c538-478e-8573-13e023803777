'use client'

import Header from '@/components/Header'
import Footer from '@/components/Footer'
import { useTranslations } from '@/hooks/useTranslations'
import { 
  BuildingStorefrontIcon,
  UsersIcon,
  MapPinIcon,
  HeartIcon,
  StarIcon,
  CheckCircleIcon
} from '@heroicons/react/24/outline'

export default function AboutPage() {
  const { t, language } = useTranslations()

  const content = {
    fa: {
      title: 'درباره اذکیا',
      subtitle: 'پلتفرم پیشرو برای کشف و سفارش غذا از بهترین رستوران‌های افغانستان',
      mission: 'ماموریت ما',
      missionText: 'اذکیا با هدف ایجاد پلی میان مردم افغانستان و رستوران‌های محلی تأسیس شده است. ما معتقدیم که هر کسی باید به راحتی بتواند به غذاهای خوشمزه و باکیفیت دسترسی داشته باشد.',
      missionText2: 'از طریق پلتفرم ما، شما می‌توانید رستوران‌های نزدیک خود را کشف کنید، منوها را مرور کنید و با چند کلیک ساده سفارش خود را ثبت کنید.',
      vision: 'چشم‌انداز ما',
      visionText: 'تبدیل شدن به بزرگترین پلتفرم سفارش غذا در افغانستان و حمایت از کسب‌وکارهای محلی برای رشد و توسعه',
      values: 'ارزش‌های ما',
      valuesSubtitle: 'اصولی که ما را در مسیر خدمت‌رسانی هدایت می‌کند',
      customerFocus: 'مشتری‌مداری',
      customerFocusDesc: 'رضایت شما اولویت اول ما است',
      quality: 'کیفیت',
      qualityDesc: 'تنها با بهترین رستوران‌ها همکاری می‌کنیم',
      community: 'جامعه‌محوری',
      communityDesc: 'حمایت از کسب‌وکارهای محلی',
      reliability: 'قابلیت اعتماد',
      reliabilityDesc: 'خدمات مطمئن و قابل اعتماد',
      team: 'تیم ما',
      teamSubtitle: 'افرادی که اذکیا را ممکن می‌سازند',
      ceoRole: 'مدیر عامل',
      ceoDesc: 'با بیش از ۱۰ سال تجربه در صنعت فناوری',
      ctoRole: 'مدیر فنی',
      ctoDesc: 'متخصص توسعه نرم‌افزار و طراحی سیستم',
      marketingRole: 'مدیر بازاریابی',
      marketingDesc: 'متخصص بازاریابی دیجیتال و روابط عمومی',
      ctaTitle: 'آماده تجربه اذکیا هستید؟',
      ctaSubtitle: 'همین حالا شروع کنید و بهترین رستوران‌های اطراف خود را کشف کنید',
      viewRestaurants: 'مشاهده رستوران‌ها',
      contactUs: 'تماس با ما'
    },
    en: {
      title: 'About Azkuja',
      subtitle: 'Leading platform for discovering and ordering food from the best restaurants in Afghanistan',
      mission: 'Our Mission',
      missionText: 'Azkuja was founded with the goal of creating a bridge between the people of Afghanistan and local restaurants. We believe everyone should have easy access to delicious and quality food.',
      missionText2: 'Through our platform, you can discover nearby restaurants, browse menus, and place your order with just a few simple clicks.',
      vision: 'Our Vision',
      visionText: 'To become the largest food ordering platform in Afghanistan and support local businesses for growth and development',
      values: 'Our Values',
      valuesSubtitle: 'Principles that guide us in our service',
      customerFocus: 'Customer Focus',
      customerFocusDesc: 'Your satisfaction is our top priority',
      quality: 'Quality',
      qualityDesc: 'We only partner with the best restaurants',
      community: 'Community-Centered',
      communityDesc: 'Supporting local businesses',
      reliability: 'Reliability',
      reliabilityDesc: 'Secure and trustworthy services',
      team: 'Our Team',
      teamSubtitle: 'The people who make Azkuja possible',
      ceoRole: 'CEO',
      ceoDesc: 'With over 10 years of experience in technology industry',
      ctoRole: 'CTO',
      ctoDesc: 'Software development and system design specialist',
      marketingRole: 'Marketing Manager',
      marketingDesc: 'Digital marketing and public relations specialist',
      ctaTitle: 'Ready to Experience Azkuja?',
      ctaSubtitle: 'Start now and discover the best restaurants around you',
      viewRestaurants: 'View Restaurants',
      contactUs: 'Contact Us'
    },
    ps: {
      title: 'د اذکیا په اړه',
      subtitle: 'د افغانستان د غوره رستورانونو څخه د خوړو د موندلو او امر ورکولو مخکښ پلیټفارم',
      mission: 'زموږ موخه',
      missionText: 'اذکیا د افغانستان د خلکو او محلي رستورانونو ترمنځ د پل جوړولو په موخه تاسیس شوی. موږ باور لرو چې هرڅوک باید اسانه د خوندورو او کیفیتي خوړو ته لاسرسی ولري.',
      missionText2: 'زموږ د پلیټفارم له لارې، تاسو کولی شئ د خپل نږدې رستورانونه وپیژنئ، مینو وګورئ، او د یو څو ساده کلیکونو سره خپل امر ثبت کړئ.',
      vision: 'زموږ لیدلوری',
      visionText: 'په افغانستان کې د خوړو د امرونو ترټولو لوی پلیټفارم ته اوړول او د ودې او پراختیا لپاره د محلي سوداګرۍ ملاتړ',
      values: 'زموږ ارزښتونه',
      valuesSubtitle: 'هغه اصول چې موږ د خدماتو په لاره کې رهبري کوي',
      customerFocus: 'پیرودونکي ته پاملرنه',
      customerFocusDesc: 'ستاسو رضایت زموږ لومړنی لومړیتوب دی',
      quality: 'کیفیت',
      qualityDesc: 'موږ یوازې د غوره رستورانونو سره ګډکار یو',
      community: 'ټولنه محوره',
      communityDesc: 'د محلي سوداګرۍ ملاتړ',
      reliability: 'د اعتماد وړتیا',
      reliabilityDesc: 'خوندي او د باور وړ خدمات',
      team: 'زموږ ټیم',
      teamSubtitle: 'هغه خلک چې اذکیا ممکنه کوي',
      ceoRole: 'اجرایوي رئیس',
      ceoDesc: 'د ټیکنالوژۍ په صنعت کې د ۱۰ کلونو څخه زیاتې تجربې سره',
      ctoRole: 'تخنیکي رئیس',
      ctoDesc: 'د سافټویر پراختیا او سیسټم ډیزاین ماهر',
      marketingRole: 'د بازار موندنې مدیر',
      marketingDesc: 'د ډیجیټل بازار موندنې او عامه اړیکو ماهر',
      ctaTitle: 'د اذکیا تجربې ته چمتو یاست؟',
      ctaSubtitle: 'اوس پیل وکړئ او د ځان شاوخوا غوره رستورانونه وپیژنئ',
      viewRestaurants: 'رستورانونه وګورئ',
      contactUs: 'موږ سره اړیکه ونیسئ'
    }
  }

  const currentContent = content[language] || content.fa

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <Header />
      
      <main>
        {/* Hero Section */}
        <section className="bg-gradient-to-br from-primary-50 to-orange-50 dark:from-gray-800 dark:to-gray-900 py-20">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center">
              <h1 className="text-4xl md:text-6xl font-bold text-gray-900 dark:text-white mb-6">
                {currentContent.title}
              </h1>
              <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                {currentContent.subtitle}
              </p>
            </div>
          </div>
        </section>

        {/* Mission Section */}
        <section className="py-16 bg-white dark:bg-gray-800">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              <div>
                <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-6">
                  {currentContent.mission}
                </h2>
                <p className="text-lg text-gray-600 dark:text-gray-300 mb-6">
                  {currentContent.missionText}
                </p>
                <p className="text-lg text-gray-600 dark:text-gray-300">
                  {currentContent.missionText2}
                </p>
              </div>
              <div className="relative">
                <div className="bg-gradient-to-br from-primary-500 to-orange-500 rounded-2xl p-8 text-white">
                  <BuildingStorefrontIcon className="w-16 h-16 mb-4" />
                  <h3 className="text-2xl font-bold mb-4">{currentContent.vision}</h3>
                  <p className="text-primary-100">
                    {currentContent.visionText}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Values Section */}
        <section className="py-16 bg-gray-50 dark:bg-gray-900">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
                {currentContent.values}
              </h2>
              <p className="text-lg text-gray-600 dark:text-gray-300">
                {currentContent.valuesSubtitle}
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              <div className="text-center">
                <div className="bg-primary-100 dark:bg-primary-900 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                  <HeartIcon className="w-8 h-8 text-primary-600" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                  {currentContent.customerFocus}
                </h3>
                <p className="text-gray-600 dark:text-gray-300">
                  {currentContent.customerFocusDesc}
                </p>
              </div>

              <div className="text-center">
                <div className="bg-primary-100 dark:bg-primary-900 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                  <StarIcon className="w-8 h-8 text-primary-600" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                  {currentContent.quality}
                </h3>
                <p className="text-gray-600 dark:text-gray-300">
                  {currentContent.qualityDesc}
                </p>
              </div>

              <div className="text-center">
                <div className="bg-primary-100 dark:bg-primary-900 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                  <UsersIcon className="w-8 h-8 text-primary-600" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                  {currentContent.community}
                </h3>
                <p className="text-gray-600 dark:text-gray-300">
                  {currentContent.communityDesc}
                </p>
              </div>

              <div className="text-center">
                <div className="bg-primary-100 dark:bg-primary-900 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                  <CheckCircleIcon className="w-8 h-8 text-primary-600" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                  {currentContent.reliability}
                </h3>
                <p className="text-gray-600 dark:text-gray-300">
                  {currentContent.reliabilityDesc}
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* Team Section */}
        <section className="py-16 bg-white dark:bg-gray-800">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
                {currentContent.team}
              </h2>
              <p className="text-lg text-gray-600 dark:text-gray-300">
                {currentContent.teamSubtitle}
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              <div className="text-center">
                <div className="w-32 h-32 bg-gradient-to-br from-primary-400 to-primary-600 rounded-full mx-auto mb-4 flex items-center justify-center">
                  <span className="text-white text-2xl font-bold">
                    {language === 'en' ? 'AH' : language === 'ps' ? 'احمد' : 'احمد'}
                  </span>
                </div>
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                  {language === 'en' ? 'Ahmad Rezaei' : language === 'ps' ? 'احمد رضایي' : 'احمد رضایی'}
                </h3>
                <p className="text-primary-600 font-medium mb-2">{currentContent.ceoRole}</p>
                <p className="text-gray-600 dark:text-gray-300 text-sm">
                  {currentContent.ceoDesc}
                </p>
              </div>

              <div className="text-center">
                <div className="w-32 h-32 bg-gradient-to-br from-orange-400 to-orange-600 rounded-full mx-auto mb-4 flex items-center justify-center">
                  <span className="text-white text-2xl font-bold">
                    {language === 'en' ? 'FA' : language === 'ps' ? 'فاطمه' : 'فاطمه'}
                  </span>
                </div>
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                  {language === 'en' ? 'Fatima Ahmadi' : language === 'ps' ? 'فاطمه احمدي' : 'فاطمه احمدی'}
                </h3>
                <p className="text-primary-600 font-medium mb-2">{currentContent.ctoRole}</p>
                <p className="text-gray-600 dark:text-gray-300 text-sm">
                  {currentContent.ctoDesc}
                </p>
              </div>

              <div className="text-center">
                <div className="w-32 h-32 bg-gradient-to-br from-green-400 to-green-600 rounded-full mx-auto mb-4 flex items-center justify-center">
                  <span className="text-white text-2xl font-bold">
                    {language === 'en' ? 'AH' : language === 'ps' ? 'علي' : 'علی'}
                  </span>
                </div>
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                  {language === 'en' ? 'Ali Hosseini' : language === 'ps' ? 'علي حسیني' : 'علی حسینی'}
                </h3>
                <p className="text-primary-600 font-medium mb-2">{currentContent.marketingRole}</p>
                <p className="text-gray-600 dark:text-gray-300 text-sm">
                  {currentContent.marketingDesc}
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-16 bg-primary-600">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 className="text-3xl font-bold text-white mb-4">
              {currentContent.ctaTitle}
            </h2>
            <p className="text-xl text-primary-100 mb-8">
              {currentContent.ctaSubtitle}
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a
                href="/restaurants"
                className="bg-white text-primary-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors"
              >
                {currentContent.viewRestaurants}
              </a>
              <a
                href="/contact"
                className="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-primary-600 transition-colors"
              >
                {currentContent.contactUs}
              </a>
            </div>
          </div>
        </section>
      </main>

      <Footer />
    </div>
  )
} 