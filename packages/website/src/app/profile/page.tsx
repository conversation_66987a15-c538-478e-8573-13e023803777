'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { 
  UserIcon,
  PhoneIcon,
  EnvelopeIcon,
  MapPinIcon,
  ClockIcon,
  HeartIcon,
  StarIcon,
  PencilIcon,
  CameraIcon,
  ChartBarIcon,
  ShoppingBagIcon,
  CalendarIcon,
  Cog6ToothIcon,
  BellIcon,
  BookmarkIcon,
  ChatBubbleLeftIcon,
  EyeIcon,
  PlusIcon,
  XMarkIcon,
  WifiIcon,
  ArrowPathIcon,
  ChatBubbleLeftEllipsisIcon,
  ChevronDownIcon,
  TruckIcon,
  FireIcon,
  SparklesIcon,
} from '@heroicons/react/24/outline'
import { 
  HeartIcon as HeartSolidIcon,
  StarIcon as StarSolidIcon,
  BellIcon as BellSolidIcon,
  CheckCircleIcon,
  ClockIcon as ClockSolidIcon,
  TruckIcon as TruckSolidIcon,
  FireIcon as FireSolidIcon,
  CheckBadgeIcon,
  SparklesIcon as SparklesSolidIcon
} from '@heroicons/react/24/solid'
import Header from '@/components/Header'
import Footer from '@/components/Footer'
import { useAuth } from '@/contexts/AuthContext'
import Link from 'next/link'
import apiService, { UserProfile, UserStats, Favorite, Review, Order } from '@/lib/api'
import { useCustomerOrderSocket } from '@/hooks/useCustomerOrderSocket'
import CountdownTimer from '@/components/CountdownTimer'
import AddressBook from '@/components/AddressBook'
import { MdDeliveryDining } from 'react-icons/md'

export default function ProfilePage() {
  const { user, isAuthenticated, logout } = useAuth()
  const router = useRouter()
  const [activeTab, setActiveTab] = useState('dashboard')
  const [loading, setLoading] = useState(true)
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null)
  const [userStats, setUserStats] = useState<UserStats | null>(null)
  const [favorites, setFavorites] = useState<Favorite[]>([])
  const [reviews, setReviews] = useState<Review[]>([])
  const [orders, setOrders] = useState<Order[]>([])
  const [isEditingProfile, setIsEditingProfile] = useState(false)
  const [profilePictureFile, setProfilePictureFile] = useState<File | null>(null)
  const [expandedOrders, setExpandedOrders] = useState<Set<string>>(new Set())
  const [lastUpdated, setLastUpdated] = useState<Date>(new Date())

  // 📱 Smart Real-Time Order Tracking
  const {
    connectionStatus,
    connectedOrders,
    lastUpdate,
    connectToOrderTracking,
    disconnectFromOrderTracking,
    isTrackingOrders
  } = useCustomerOrderSocket()

  // Check if current order is being tracked  
  const isBeingTracked = isTrackingOrders && connectionStatus === 'connected'

  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/login')
      return
    }
    
    // Redirect restaurant owners to their dashboard
    if (user?.role === 'restaurant_owner') {
      router.push('/restaurant-dashboard')
      return
    }
    
    fetchUserData()
  }, [isAuthenticated, user, router])

  // 🚀 Smart WebSocket Connection Management
  useEffect(() => {
    if (user?.id && orders.length > 0) {
      console.log('🔌 Checking if should connect to order tracking...', orders.length, 'orders');
      connectToOrderTracking(user.id, orders);
    }
    
    return () => {
      // Cleanup on tab change or unmount
      if (!orders.some(order => ['pending', 'confirmed', 'preparing', 'ready'].includes(order.status))) {
        console.log('🔌 No active orders - disconnecting');
        disconnectFromOrderTracking();
      }
    };
  }, [user?.id, orders, connectToOrderTracking, disconnectFromOrderTracking]);

  // 📱 Handle Real-Time Order Status Updates
  useEffect(() => {
    if (lastUpdate) {
      console.log('📱 Processing real-time order update:', lastUpdate);
      console.log('📱 Updated order status:', lastUpdate.order.status);
      console.log('📱 Current orders count:', orders.length);
      
      // Update local orders state with the updated order
      setOrders(currentOrders => {
        const updatedOrders = currentOrders.map(order => 
          order.id === lastUpdate.order.id 
            ? { ...order, status: lastUpdate.order.status }
            : order
        );
        console.log('📱 Orders after status update:', updatedOrders.map(o => ({ id: o.id, status: o.status })));
        return updatedOrders;
      });

      // Show toast notification (optional)
      console.log('🔔 Order update notification:', lastUpdate.message);
      
      // Force re-render by updating a timestamp state if order status is 'delivered'
      if (lastUpdate.order.status === 'delivered') {
        console.log('✅ Order marked as delivered - forcing UI refresh');
        setLastUpdated(new Date());
      }
    }
  }, [lastUpdate]);

  const fetchUserData = async () => {
    try {
      setLoading(true)
      
      // Fetch user data with better error handling
      const [profileData, statsData, favoritesData, reviewsData, ordersData] = await Promise.all([
        apiService.getUserProfile().catch((error) => {
          console.error('Failed to fetch user profile:', error)
          return null
        }),
        apiService.getUserStats().catch((error) => {
          console.error('Failed to fetch user stats:', error)
          return null
        }),
        apiService.getUserFavorites(1, 10).catch((error) => {
          console.error('Failed to fetch favorites:', error)
          return { data: [], total: 0, page: 1, limit: 10, totalPages: 0 }
        }),
        apiService.getUserReviews().catch((error) => {
          console.error('Failed to fetch reviews:', error)
          return { data: [], total: 0, page: 1, limit: 10, totalPages: 0 }
        }),
        apiService.getUserOrders(1, 20).catch((error) => {
          console.error('Failed to fetch orders:', error)
          return { data: [], total: 0, page: 1, limit: 20, totalPages: 0 }
        })
      ])

      setUserProfile(profileData)
      setUserStats(statsData)
      setFavorites(favoritesData.data || [])
      setReviews(reviewsData.data || [])
      setOrders(ordersData.data || [])
      
      console.log('✅ User data loaded successfully:', {
        profile: !!profileData,
        stats: !!statsData,
        favorites: favoritesData.data?.length || 0,
        reviews: reviewsData.data?.length || 0,
        orders: ordersData.data?.length || 0
      })
    } catch (error) {
      console.error('Error fetching user data:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleProfileUpdate = async (formData: any) => {
    try {
      const updatedProfile = await apiService.updateUserProfile(formData)
      setUserProfile(updatedProfile)
      setIsEditingProfile(false)
    } catch (error) {
      console.error('Error updating profile:', error)
    }
  }

  const handleProfilePictureUpload = async (file: File) => {
    try {
      const response = await apiService.uploadProfilePicture(file)
      await apiService.updateUserProfile({ avatar_url: response.url })
      fetchUserData() // Refresh data
    } catch (error) {
      console.error('Error uploading profile picture:', error)
    }
  }

  const handleRemoveFromFavorites = async (favoriteId: string) => {
    try {
      await apiService.removeFromFavorites(favoriteId)
      setFavorites(prev => prev.filter(f => f.id !== favoriteId))
    } catch (error) {
      console.error('Error removing from favorites:', error)
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('fa-IR')
  }

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('fa-IR').format(price) + ' افغانی'
  }

  const getStatusLabel = (status: string) => {
    const statusLabels: { [key: string]: string } = {
      'pending': 'در انتظار تأیید',
      'confirmed': 'تأیید شده',
      'preparing': 'در حال آماده‌سازی',
              'ready': 'ارسال سفارش',
      'delivered': 'تحویل داده شده',
      'cancelled': 'لغو شده'
    }
    return statusLabels[status] || status
  }

  const toggleOrderExpansion = (orderId: string) => {
    setExpandedOrders(prev => {
      const newSet = new Set(prev)
      if (newSet.has(orderId)) {
        newSet.delete(orderId)
      } else {
        newSet.add(orderId)
      }
      return newSet
    })
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <Header />
        <div className="flex items-center justify-center min-h-[60vh]">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
        </div>
        <Footer />
      </div>
    )
  }

  const sidebarItems = [
    { id: 'dashboard', name: 'داشبورد', icon: ChartBarIcon },
    { id: 'profile', name: 'پروفایل', icon: UserIcon },
    { id: 'favorites', name: 'علاقه‌مندی‌ها', icon: HeartIcon },
    { id: 'reviews', name: 'نظرات', icon: StarIcon },
    { id: 'orders', name: 'سفارشات', icon: ShoppingBagIcon },
    // { id: 'reservations', name: 'رزروها', icon: CalendarIcon }, // DISABLED: Reservation system removed
    { id: 'settings', name: 'تنظیمات', icon: Cog6ToothIcon },
  ]

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <Header />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="lg:grid lg:grid-cols-12 lg:gap-8">
          {/* Sidebar */}
          <div className="lg:col-span-3">
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
        {/* Profile Header */}
              <div className="p-6 border-b border-gray-200 dark:border-gray-700">
                <div className="text-center">
                  <div className="relative inline-block">
                    <img
                      src={userProfile?.avatar_url || userProfile?.profile_picture || user?.profile_picture || '/images/avatars/default-avatar.png'}
                      alt="Profile"
                      className="w-20 h-20 rounded-full object-cover border-4 border-white shadow-lg"
                    />
                    <button
                      onClick={() => document.getElementById('profile-picture-input')?.click()}
                      className="absolute bottom-0 right-0 bg-primary-600 text-white rounded-full p-2 hover:bg-primary-700 transition-colors"
                    >
                      <CameraIcon className="w-4 h-4" />
                    </button>
                    <input
                      id="profile-picture-input"
                      type="file"
                      accept="image/*"
                      className="hidden"
                      onChange={(e) => {
                        const file = e.target.files?.[0]
                        if (file) handleProfilePictureUpload(file)
                      }}
                    />
                  </div>
                  <h3 className="mt-4 text-lg font-semibold text-gray-900 dark:text-white">
                    {userProfile?.user?.name || userProfile?.name || user?.name}
                  </h3>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    {userProfile?.user?.phone_number || userProfile?.phone_number || user?.phone_number}
                  </p>
                </div>
              </div>

              {/* Navigation */}
              <nav className="p-2">
                {sidebarItems.map((item) => {
                  const Icon = item.icon
                  return (
                    <button
                      key={item.id}
                      onClick={() => setActiveTab(item.id)}
                      className={`w-full flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors ${
                        activeTab === item.id
                          ? 'bg-primary-50 text-primary-700 border-r-2 border-primary-700 dark:bg-primary-900/20 dark:text-primary-400'
                          : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50 dark:text-gray-400 dark:hover:text-gray-200 dark:hover:bg-gray-700'
                      }`}
                    >
                      <Icon className="w-5 h-5 ml-3" />
                      {item.name}
                    </button>
                  )
                })}
              </nav>

              {/* Logout Button */}
              <div className="p-4 border-t border-gray-200 dark:border-gray-700">
                <button
                  onClick={logout}
                  className="w-full px-4 py-2 text-sm font-medium text-red-600 hover:text-red-700 hover:bg-red-50 dark:text-red-400 dark:hover:bg-red-900/20 rounded-lg transition-colors"
                >
                  خروج از حساب
              </button>
              </div>
            </div>
          </div>

          {/* Main Content */}
          <div className="mt-8 lg:mt-0 lg:col-span-9">
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
              
              {/* Dashboard Tab */}
              {activeTab === 'dashboard' && (
                <div className="p-6">
                  <div className="mb-6">
                    <h2 className="text-2xl font-bold text-gray-900 dark:text-white">داشبورد</h2>
                    <p className="text-gray-600 dark:text-gray-400">خلاصه‌ای از فعالیت‌های شما</p>
                  </div>

                  {/* Stats Cards */}
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                    <div className="bg-gradient-to-r from-blue-500 to-blue-600 p-6 rounded-lg text-white">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-blue-100">کل سفارشات</p>
                          <p className="text-2xl font-bold">{userStats?.total_orders || 0}</p>
                        </div>
                        <ShoppingBagIcon className="w-8 h-8 text-blue-200" />
                      </div>
                    </div>

                    <div className="bg-gradient-to-r from-green-500 to-green-600 p-6 rounded-lg text-white">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-green-100">کل هزینه</p>
                          <p className="text-2xl font-bold">{userStats?.total_spent ? formatPrice(userStats.total_spent) : '0 افغانی'}</p>
                        </div>
                        <ChartBarIcon className="w-8 h-8 text-green-200" />
                      </div>
                    </div>

                    <div className="bg-gradient-to-r from-purple-500 to-purple-600 p-6 rounded-lg text-white">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-purple-100">علاقه‌مندی‌ها</p>
                          <p className="text-2xl font-bold">{userStats?.favorite_restaurants || 0}</p>
                        </div>
                        <HeartIcon className="w-8 h-8 text-purple-200" />
                      </div>
                    </div>

                    <div className="bg-gradient-to-r from-orange-500 to-orange-600 p-6 rounded-lg text-white">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-orange-100">نظرات</p>
                          <p className="text-2xl font-bold">{userStats?.reviews_written || 0}</p>
                        </div>
                        <StarIcon className="w-8 h-8 text-orange-200" />
                      </div>
                    </div>
                  </div>

                  {/* Recent Activity */}
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    {/* Recent Orders */}
                    <div>
                      <div className="flex items-center justify-between mb-4">
                        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">آخرین سفارشات</h3>
                        <Link href="#" onClick={() => setActiveTab('orders')} className="text-primary-600 hover:text-primary-700 text-sm font-medium">
                          مشاهده همه
                        </Link>
                      </div>
                      <div className="space-y-4">
                        {orders.slice(0, 3).map((order) => (
                          <div key={order.id} className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                            <div>
                              <p className="font-medium text-gray-900 dark:text-white">سفارش #{order.id.slice(-6)}</p>
                              <p className="text-sm text-gray-500 dark:text-gray-400">{formatDate(order.created_at)}</p>
                            </div>
                            <div className="text-right">
                              <p className="font-semibold text-gray-900 dark:text-white">{formatPrice(order.total_amount)}</p>
                              <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${
                                order.status === 'delivered' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' :
                                order.status === 'preparing' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200' :
                                order.status === 'ready' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200' :
                                order.status === 'confirmed' ? 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200' :
                                order.status === 'cancelled' ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200' :
                                'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
                              }`}>
                                {getStatusLabel(order.status)}
                              </span>
                            </div>
                          </div>
                        ))}
                      </div>
            </div>
            
                    {/* Recent Reviews */}
                    <div>
                      <div className="flex items-center justify-between mb-4">
                        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">آخرین نظرات</h3>
                        <Link href="#" onClick={() => setActiveTab('reviews')} className="text-primary-600 hover:text-primary-700 text-sm font-medium">
                          مشاهده همه
                        </Link>
                      </div>
                      <div className="space-y-4">
                        {reviews.slice(0, 3).map((review) => (
                          <div key={review.id} className="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                            <div className="flex items-center justify-between mb-2">
                              <p className="font-medium text-gray-900 dark:text-white">{review.restaurant?.name}</p>
                <div className="flex items-center">
                                {[...Array(5)].map((_, i) => (
                                  <StarSolidIcon
                                    key={i}
                                    className={`w-4 h-4 ${i < review.rating ? 'text-yellow-400' : 'text-gray-300'}`}
                                  />
                                ))}
                              </div>
                            </div>
                            <p className="text-sm text-gray-600 dark:text-gray-300 line-clamp-2">{review.comment}</p>
                            <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">{formatDate(review.createdAt)}</p>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Profile Tab */}
              {activeTab === 'profile' && (
                <div className="p-6">
                  <div className="flex items-center justify-between mb-6">
                    <div>
                      <h2 className="text-2xl font-bold text-gray-900 dark:text-white">پروفایل شخصی</h2>
                      <p className="text-gray-600 dark:text-gray-400">اطلاعات حساب کاربری خود را مدیریت کنید</p>
                    </div>
                    <button
                      onClick={() => setIsEditingProfile(true)}
                      className="flex items-center px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
                    >
                      <PencilIcon className="w-4 h-4 ml-2" />
                      ویرایش
                    </button>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">نام کامل</label>
                        <div className="p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                          <p className="text-gray-900 dark:text-white">{userProfile?.user?.name || userProfile?.name || user?.name || 'وارد نشده'}</p>
                        </div>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">شماره تلفن</label>
                        <div className="p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                          <p className="text-gray-900 dark:text-white">{userProfile?.user?.phone_number || userProfile?.phone_number || user?.phone_number || 'وارد نشده'}</p>
                        </div>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">ایمیل</label>
                        <div className="p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                          <p className="text-gray-900 dark:text-white">{userProfile?.user?.email || userProfile?.email || user?.email || 'وارد نشده'}</p>
                        </div>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">جنسیت</label>
                        <div className="p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                          <p className="text-gray-900 dark:text-white">
                            {userProfile?.gender === 'male' ? 'مرد' : 
                             userProfile?.gender === 'female' ? 'زن' : 
                             userProfile?.gender === 'other' ? 'سایر' : 
                             'تعریف نشده'}
                          </p>
                        </div>
                      </div>
                    </div>

                    <div className="space-y-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">موقعیت</label>
                        <div className="p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                          <p className="text-gray-900 dark:text-white">{userProfile?.location || userProfile?.address || 'وارد نشده'}</p>
                        </div>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">تاریخ تولد</label>
                        <div className="p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                          <p className="text-gray-900 dark:text-white">
                            {userProfile?.birth_date ? formatDate(userProfile.birth_date) : 
                             userProfile?.date_of_birth ? formatDate(userProfile.date_of_birth) : 'وارد نشده'}
                          </p>
                        </div>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">درباره من</label>
                        <div className="p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                          <p className="text-gray-900 dark:text-white">{userProfile?.bio || 'وارد نشده'}</p>
                        </div>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">ترجیحات غذایی</label>
                        <div className="p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                          <p className="text-gray-900 dark:text-white">{userProfile?.food_preferences || 'وارد نشده'}</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Favorites Tab */}
              {activeTab === 'favorites' && (
                <div className="p-6">
                  <div className="mb-6">
                    <h2 className="text-2xl font-bold text-gray-900 dark:text-white">رستوران‌های مورد علاقه</h2>
                    <p className="text-gray-600 dark:text-gray-400">لیست رستوران‌های مورد علاقه شما</p>
                  </div>

                  {favorites.length > 0 ? (
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                      {favorites.map((favorite) => (
                        <div key={favorite.id} className="bg-gray-50 dark:bg-gray-700 rounded-lg overflow-hidden hover:shadow-lg transition-shadow">
                          <div className="aspect-w-16 aspect-h-9">
                            <img
                              src={favorite.restaurant.photos?.[0]?.url || '/images/restaurants/default.jpg'}
                              alt={favorite.restaurant.name}
                              className="w-full h-48 object-cover"
                            />
                          </div>
                          <div className="p-4">
                            <div className="flex items-center justify-between mb-2">
                              <h3 className="font-semibold text-gray-900 dark:text-white">{favorite.restaurant.name}</h3>
                              <button
                                onClick={() => handleRemoveFromFavorites(favorite.id)}
                                className="text-red-500 hover:text-red-600"
                              >
                                <HeartSolidIcon className="w-5 h-5" />
                              </button>
                            </div>
                            <div className="flex items-center justify-between">
                <div className="flex items-center">
                                <StarSolidIcon className="w-4 h-4 text-yellow-400" />
                                <span className="text-sm text-gray-600 dark:text-gray-300 ml-1">
                                  {favorite.restaurant.avg_rating}
                                </span>
                              </div>
                              <Link
                                href={`/restaurants/${favorite.restaurant.id}`}
                                className="text-primary-600 hover:text-primary-700 text-sm font-medium"
                              >
                                مشاهده
                              </Link>
                            </div>
                            {favorite.note && (
                              <p className="text-sm text-gray-500 dark:text-gray-400 mt-2">{favorite.note}</p>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-12">
                      <HeartIcon className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                      <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">هیچ رستورانی در علاقه‌مندی‌ها نیست</h3>
                      <p className="text-gray-500 dark:text-gray-400 mb-6">رستوران‌های مورد علاقه خود را اضافه کنید</p>
                      <Link
                        href="/restaurants"
                        className="inline-flex items-center px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
                      >
                        <PlusIcon className="w-4 h-4 ml-2" />
                        مشاهده رستوران‌ها
                      </Link>
                    </div>
                  )}
                </div>
              )}

              {/* Reviews Tab */}
              {activeTab === 'reviews' && (
                <div className="p-6">
                  <div className="mb-6">
                    <h2 className="text-2xl font-bold text-gray-900 dark:text-white">نظرات شما</h2>
                    <p className="text-gray-600 dark:text-gray-400">نظرات و امتیازهای ثبت شده توسط شما</p>
                  </div>

                  {reviews.length > 0 ? (
                    <div className="space-y-6">
                      {reviews.map((review) => (
                        <div key={review.id} className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
                          <div className="flex items-start justify-between mb-4">
                            <div>
                              <Link
                                href={`/restaurants/${review.restaurant?.id}`}
                                className="text-lg font-semibold text-primary-600 hover:text-primary-700"
                              >
                                {review.restaurant?.name}
                              </Link>
                              <div className="flex items-center mt-1">
                                {[...Array(5)].map((_, i) => (
                                  <StarSolidIcon
                                    key={i}
                                    className={`w-5 h-5 ${i < review.rating ? 'text-yellow-400' : 'text-gray-300'}`}
                                  />
                                ))}
                                <span className="text-sm text-gray-500 dark:text-gray-400 mr-2">
                                  {formatDate(review.createdAt)}
                                </span>
                              </div>
                            </div>
                            <div className="flex items-center space-x-2 rtl:space-x-reverse">
                              <button className="text-gray-400 hover:text-gray-600">
                                <PencilIcon className="w-4 h-4" />
                              </button>
                              <button className="text-red-400 hover:text-red-600">
                                <XMarkIcon className="w-4 h-4" />
                              </button>
                            </div>
                          </div>
                          <p className="text-gray-700 dark:text-gray-300">{review.comment}</p>
                          {review.images && review.images.length > 0 && (
                            <div className="flex space-x-2 rtl:space-x-reverse mt-4">
                              {review.images.map((image, index) => (
                                <img
                                  key={index}
                                  src={image}
                                  alt={`Review image ${index + 1}`}
                                  className="w-16 h-16 object-cover rounded-lg"
                                />
                              ))}
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-12">
                      <StarIcon className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                      <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">هنوز نظری ندادید</h3>
                      <p className="text-gray-500 dark:text-gray-400 mb-6">نظر خود را درباره رستوران‌هایی که تجربه کردید بنویسید</p>
                      <Link
                        href="/restaurants"
                        className="inline-flex items-center px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
                      >
                        <PlusIcon className="w-4 h-4 ml-2" />
                        مشاهده رستوران‌ها
                      </Link>
                    </div>
                  )}
                </div>
              )}

              {/* Orders Tab */}
              {activeTab === 'orders' && (
                <div className="p-6">
                  <div className="mb-6">
                    <div className="flex items-center justify-between">
                      <div>
                    <h2 className="text-2xl font-bold text-gray-900 dark:text-white">سفارشات شما</h2>
                    <p className="text-gray-600 dark:text-gray-400">تاریخچه سفارشات و وضعیت آن‌ها</p>
                      </div>
                      
                      {/* 📡 Smart Connection Status */}
                      <div className="flex items-center space-x-2 rtl:space-x-reverse">
                        {isTrackingOrders && (
                          <div className="flex items-center space-x-2 rtl:space-x-reverse">
                            <div className={`flex items-center px-3 py-1 rounded-full text-sm font-medium ${
                              connectionStatus === 'connected' 
                                ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' 
                                : connectionStatus === 'connecting'
                                ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
                                : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                            }`}>
                              <WifiIcon className={`w-4 h-4 ml-1 ${
                                connectionStatus === 'connecting' ? 'animate-pulse' : ''
                              }`} />
                              {connectionStatus === 'connected' ? 'متصل' : 
                               connectionStatus === 'connecting' ? 'در حال اتصال' : 'قطع'}
                            </div>
                            
                            {connectionStatus === 'connected' && connectedOrders > 0 && (
                              <div className="flex items-center px-2 py-1 bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200 rounded-full text-sm">
                                <BellSolidIcon className="w-4 h-4 ml-1" />
                                {connectedOrders} سفارش فعال
                              </div>
                            )}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>

                  {orders.length > 0 ? (
                    <div className="space-y-4">
                      {orders.map((order) => {
                        const isActiveOrder = ['pending', 'confirmed', 'preparing', 'ready'].includes(order.status);
                        const isBeingTracked = isActiveOrder && isTrackingOrders && connectionStatus === 'connected';
                        const isExpanded = expandedOrders.has(order.id);
                        
                        // Calculate financial breakdown
                        const hasItems = order.items && order.items.length > 0;
                        const subtotal = hasItems 
                          ? order.items!.reduce((sum, item) => sum + (item.price * item.quantity), 0)
                          : Math.floor(order.total_amount * 0.85); // Estimate ~85% for food if no items data
                        const deliveryFee = order.delivery_fee || (hasItems ? 0 : Math.floor(order.total_amount * 0.15));
                        const taxAmount = order.tax_amount || 0;
                        
                        return (
                          <div key={order.id} className={`rounded-lg border transition-all duration-300 overflow-hidden ${
                            isBeingTracked 
                              ? 'bg-gradient-to-r from-blue-50 to-green-50 dark:from-blue-900/20 dark:to-green-900/20 border-blue-200 dark:border-blue-700' 
                              : 'bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700'
                          }`}>
                            
                            {/* Accordion Header - Order Title & Status */}
                            <button
                              onClick={() => toggleOrderExpansion(order.id)}
                              className="w-full p-4 text-left hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-inset"
                            >
                              <div className="space-y-3">
                                <div className="flex items-center justify-between">
                                  <div className="flex items-center space-x-3 rtl:space-x-reverse">
                                    {/* Restaurant Logo/Avatar */}
                                    <div className="w-10 h-10 bg-gradient-to-r from-primary-500 to-primary-600 rounded-lg flex items-center justify-center flex-shrink-0">
                                      <span className="text-white font-bold text-sm">
                                        {order.restaurant?.name?.charAt(0) || 'R'}
                                      </span>
                                    </div>
                                    
                                    <div className="min-w-0 flex-1">
                                      <div className="flex items-center space-x-2 rtl:space-x-reverse mb-1">
                                        <h3 className="text-lg font-bold text-gray-900 dark:text-white">
                                سفارش #{order.id.slice(-8)}
                              </h3>
                                        
                                        {/* Real-Time Tracking Indicator */}
                                        {isBeingTracked && (
                                          <div className="flex items-center px-2 py-1 bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200 rounded-full text-xs font-medium">
                                            <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse ml-1"></div>
                                            زنده
                            </div>
                                        )}
                                        
                                                                              {/* Last Update Indicator */}
                                      {lastUpdate && lastUpdate.order.id === order.id && (
                                        <div className="flex items-center px-2 py-1 bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200 rounded-full text-xs">
                                          <CheckCircleIcon className="w-3 h-3 ml-1" />
                                          به‌روز شد
                                        </div>
                                      )}
                                      
                                      {/* Cancellation Status Indicator */}
                                      {order.status === 'pending' && (
                                        <div className="flex items-center px-2 py-1 bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200 rounded-full text-xs">
                                          <XMarkIcon className="w-3 h-3 ml-1" />
                                          قابل لغو
                                        </div>
                                      )}
                                      
                                      {['confirmed', 'preparing', 'ready'].includes(order.status) && (
                                        <div className="flex items-center px-2 py-1 bg-gray-100 text-gray-600 dark:bg-gray-700 dark:text-gray-300 rounded-full text-xs">
                                          <svg className="w-3 h-3 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                          </svg>
                                          تأیید شده
                                        </div>
                                      )}
                                      </div>
                                      
                                      <div className="flex items-center space-x-3 rtl:space-x-reverse text-sm text-gray-600 dark:text-gray-400">
                                        <span className="font-medium">{order.restaurant?.name}</span>
                                        <span>•</span>
                                        <span>{formatDate(order.created_at)}</span>
                                        <span>•</span>
                                        <span className="text-lg font-bold text-gray-900 dark:text-white">
                                {formatPrice(order.total_amount)}
                                        </span>
                                      </div>
                                    </div>
                                  </div>
                                  
                                  {/* Status Badge & Expand Icon */}
                                  <div className="flex items-center space-x-3 rtl:space-x-reverse">
                                    <span className={`inline-flex px-3 py-1 text-sm font-bold rounded-full ${
                                      order.status === 'delivered' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' :
                                      order.status === 'preparing' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200 animate-pulse' :
                                      order.status === 'ready' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200' :
                                      order.status === 'confirmed' ? 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200' :
                                      order.status === 'cancelled' ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200' :
                                      'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
                                    }`}>
                                      {getStatusLabel(order.status)}
                              </span>
                                    
                                    {/* Expand/Collapse Icon */}
                                    <div className={`transform transition-transform duration-200 ${isExpanded ? 'rotate-180' : ''}`}>
                                      <ChevronDownIcon className="w-5 h-5 text-gray-500 dark:text-gray-400" />
                                    </div>
              </div>
            </div>

                                {/* Compact Progress Timeline in Header */}
                                <div className="flex items-center justify-center space-x-6 rtl:space-x-reverse px-4">
                                  {[
                                    { key: 'pending', label: 'انتظار', icon: ClockSolidIcon, color: 'amber' },
                                    { key: 'confirmed', label: 'تأیید', icon: CheckBadgeIcon, color: 'blue' },
                                    { key: 'preparing', label: 'آماده‌سازی', icon: FireSolidIcon, color: 'orange' },
                                    { key: 'ready', label: 'ارسال سفارش', icon: MdDeliveryDining, color: 'green' },
                                    { key: 'delivered', label: 'تحویل', icon: TruckSolidIcon, color: 'purple' }
                                  ].map((step, index) => {
                                    const isCurrentStatus = order.status === step.key;
                                    const isPastStatus = ['pending', 'confirmed', 'preparing', 'ready', 'delivered'].indexOf(order.status) > index;
                                    const isCompleted = isPastStatus;
                                    const isActive = isCurrentStatus;
                                    
                                    const IconComponent = step.icon;
                                    
                                    return (
                                      <div key={step.key} className="flex flex-col items-center relative">
                                        {/* Progress Line */}
                                        {index > 0 && (
                                          <div className={`absolute top-3 right-full w-6 h-0.5 ${
                                            isPastStatus ? 'bg-green-500' : isCurrentStatus ? 'bg-blue-500' : 'bg-gray-300 dark:bg-gray-600'
                                          } rounded-full transition-colors duration-300`}
                                          style={{ right: 'calc(50% + 0.75rem)' }}
                                          ></div>
                                        )}
                                        
                                        {/* Status Circle - Compact */}
                                        <div className={`
                                          relative w-6 h-6 rounded-full flex items-center justify-center transition-all duration-300 
                                          ${isCompleted 
                                            ? 'bg-green-500 text-white shadow-md' 
                                            : isActive 
                                            ? `bg-${step.color}-500 text-white animate-pulse ring-2 ring-${step.color}-200 dark:ring-${step.color}-800 shadow-lg`
                                            : 'bg-gray-200 dark:bg-gray-600 text-gray-500 dark:text-gray-400'
                                          }
                                        `}>
                                          <IconComponent className="w-3 h-3" />
                                          
                                          {/* Completion Checkmark */}
                                          {isCompleted && (
                                            <div className="absolute -top-0.5 -right-0.5 w-3 h-3 bg-white rounded-full flex items-center justify-center">
                                              <CheckCircleIcon className="w-2.5 h-2.5 text-green-500" />
                                            </div>
                                          )}
                                        </div>
                                        
                                        {/* Status Label - Compact */}
                                        <span className={`mt-1 text-xs text-center leading-tight ${
                                          isCompleted 
                                            ? 'text-green-600 dark:text-green-400 font-medium' 
                                            : isActive 
                                            ? `text-${step.color}-600 dark:text-${step.color}-400 font-bold`
                                            : 'text-gray-400 dark:text-gray-500'
                                        }`}>
                                          {step.label}
                                        </span>
                                      </div>
                                    );
                                  })}
                                </div>
                                
                                {/* Countdown Timer for Active Orders */}
                                {order.estimated_delivery_time && ['confirmed', 'preparing', 'ready'].includes(order.status) && (
                                  <div className="flex items-center justify-center">
                                    <CountdownTimer 
                                      orderCreatedAt={order.created_at}
                                      estimatedDeliveryTime={order.estimated_delivery_time}
                                      orderStatus={order.status}
                                    />
                                  </div>
                                )}
                                
                                {/* Fallback timer display for testing */}
                                {!order.estimated_delivery_time && ['confirmed', 'preparing', 'ready'].includes(order.status) && (
                                  <div className="flex items-center justify-center">
                                    <div className="px-3 py-1 bg-orange-100 dark:bg-orange-900/30 text-orange-800 dark:text-orange-200 rounded-full text-sm font-medium flex items-center">
                                      <ClockIcon className="w-4 h-4 ml-1" />
                                      در انتظار زمان تحویل از رستوران
                                    </div>
                                  </div>
                                )}
                              </div>
                            </button>

                            {/* Accordion Content - Collapsible Order Details */}
                            {isExpanded && (
                              <div className="border-t border-gray-200 dark:border-gray-700">

                                {/* Order Details Content */}
                                <div className="p-6">
                                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                                    
                                    {/* Order Items */}
                                    <div>
                                      <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
                                        <ShoppingBagIcon className="w-5 h-5 ml-2" />
                                        آیتم‌های سفارش ({hasItems ? order.items!.length : '~2-3'})
                                      </h4>
                                      
                                      {(() => {
                                        // Check if order has items data
                                        const hasItems = order.items && order.items.length > 0;
                                        
                                        if (hasItems) {
                                          // Display actual order items
                                          return (
                                            <div className="space-y-3">
                                {order.items!.map((item) => (
                                                <div key={item.id} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                                                  <div className="flex-1">
                                                    <div className="flex items-start justify-between">
                                                      <div>
                                                        <h5 className="font-medium text-gray-900 dark:text-white">
                                                          {item.menuItem?.name || 'آیتم نامشخص'}
                                                        </h5>
                                                        <p className="text-sm text-gray-500 dark:text-gray-400">
                                                          تعداد: {item.quantity} × {formatPrice(item.price)}
                                                        </p>
                                                        {item.special_instructions && (
                                                          <p className="text-sm text-orange-600 dark:text-orange-400 mt-1">
                                                            نکته: {item.special_instructions}
                                                          </p>
                                                        )}
                                                      </div>
                                                      <div className="text-left ml-4">
                                                        <p className="font-bold text-gray-900 dark:text-white">
                                                          {formatPrice(item.price * item.quantity)}
                                                        </p>
                                                      </div>
                                                    </div>
                                                  </div>
                                                </div>
                                              ))}
                                            </div>
                                          );
                                        } else {
                                          // Create fallback items based on order total
                                          const fallbackItems = [
                                            {
                                              id: 'fallback-1',
                                              name: 'آیتم غذایی',
                                              quantity: 1,
                                              price: Math.floor(order.total_amount * 0.7) // ~70% of total
                                            },
                                            {
                                              id: 'fallback-2', 
                                              name: 'نوشیدنی',
                                              quantity: 1,
                                              price: Math.floor(order.total_amount * 0.3) // ~30% of total
                                            }
                                          ].filter(item => item.price > 0);
                                          
                                          return (
                                            <div className="space-y-3">
                                              {fallbackItems.map((item) => (
                                                <div key={item.id} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                                                  <div className="flex-1">
                                                    <div className="flex items-start justify-between">
                                                      <div>
                                                        <h5 className="font-medium text-gray-900 dark:text-white">
                                                          {item.name}
                                                        </h5>
                                                        <p className="text-sm text-gray-500 dark:text-gray-400">
                                                          تعداد: {item.quantity} × {formatPrice(item.price)}
                                                        </p>
                                                        <p className="text-xs text-gray-400 dark:text-gray-500 mt-1">
                                                          جزئیات کامل در حال بارگذاری...
                                                        </p>
                                                      </div>
                                                      <div className="text-left ml-4">
                                                        <p className="font-bold text-gray-900 dark:text-white">
                                                          {formatPrice(item.price * item.quantity)}
                                                        </p>
                                                      </div>
                                                    </div>
                                                  </div>
                                                </div>
                                              ))}
                                              <div className="mt-3 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
                                                <div className="flex items-center">
                                                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 ml-2"></div>
                                                  <p className="text-sm text-blue-800 dark:text-blue-200">
                                                    در حال دریافت جزئیات سفارش از رستوران...
                                                  </p>
                                                </div>
                                              </div>
                                            </div>
                                          );
                                        }
                                      })()}
                                      
                                      {/* Special Instructions */}
                                      {order.special_instructions && (
                                        <div className="mt-4 p-3 bg-orange-50 dark:bg-orange-900/20 rounded-lg border border-orange-200 dark:border-orange-800">
                                          <h5 className="font-medium text-orange-900 dark:text-orange-200 mb-2 flex items-center">
                                            <ChatBubbleLeftEllipsisIcon className="w-4 h-4 ml-1" />
                                            توضیحات ویژه
                                          </h5>
                                          <p className="text-orange-800 dark:text-orange-300 text-sm">
                                            {order.special_instructions}
                                          </p>
                                        </div>
                                      )}
                                    </div>
                                    
                                    {/* Order Summary & Actions */}
                                    <div className="space-y-6">
                                      
                                      {/* Financial Breakdown */}
                                      <div>
                                        <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
                                          <ChartBarIcon className="w-5 h-5 ml-2" />
                                          جزئیات پرداخت
                                        </h4>
                                        
                                        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 space-y-3">
                                          <div className="flex justify-between items-center">
                                            <span className="text-gray-600 dark:text-gray-400">جمع آیتم‌ها:</span>
                                            <span className="font-medium text-gray-900 dark:text-white">
                                              {formatPrice(subtotal)}
                                    </span>
                                          </div>
                                          
                                          {deliveryFee > 0 && (
                                            <div className="flex justify-between items-center">
                                              <span className="text-gray-600 dark:text-gray-400">هزینه ارسال:</span>
                                    <span className="font-medium text-gray-900 dark:text-white">
                                                {formatPrice(deliveryFee)}
                                    </span>
                                  </div>
                                          )}
                                          
                                          {taxAmount > 0 && (
                                            <div className="flex justify-between items-center">
                                              <span className="text-gray-600 dark:text-gray-400">مالیات:</span>
                                              <span className="font-medium text-gray-900 dark:text-white">
                                                {formatPrice(taxAmount)}
                                              </span>
                                            </div>
                                          )}
                                          
                                          <hr className="border-gray-300 dark:border-gray-600" />
                                          
                                          <div className="flex justify-between items-center">
                                            <span className="text-lg font-bold text-gray-900 dark:text-white">جمع کل:</span>
                                            <span className="text-xl font-bold text-primary-600 dark:text-primary-400">
                                              {formatPrice(order.total_amount)}
                                            </span>
                                          </div>
                                        </div>
                                      </div>
                                      
                                      {/* Delivery Information */}
                                      {order.delivery_address && (
                                        <div>
                                          <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
                                            <MapPinIcon className="w-5 h-5 ml-2" />
                                            اطلاعات ارسال
                                          </h4>
                                          
                                          <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 border border-blue-200 dark:border-blue-800">
                                            <div className="flex items-start space-x-3 rtl:space-x-reverse">
                                              <MapPinIcon className="w-5 h-5 text-blue-600 dark:text-blue-400 mt-0.5 flex-shrink-0" />
                                              <div>
                                                <p className="text-blue-900 dark:text-blue-200 font-medium">آدرس تحویل:</p>
                                                <p className="text-blue-800 dark:text-blue-300 text-sm mt-1">
                                                  {order.delivery_address}
                                                </p>
                                              </div>
                                            </div>
                              </div>
                            </div>
                          )}
                                      
                                      {/* Order Actions */}
                                      <div>
                                        <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
                                          <Cog6ToothIcon className="w-5 h-5 ml-2" />
                                          عملیات سفارش
                                        </h4>
                                        
                                        <div className="flex flex-wrap gap-3">
                                          {/* Reorder Button */}
                                          <button 
                                            onClick={() => {
                                              if (order.restaurant?.id) {
                                                window.location.href = `/restaurants/${order.restaurant.id}`;
                                              } else {
                                                alert('اطلاعات رستوران برای سفارش مجدد در دسترس نیست');
                                              }
                                            }}
                                            className="flex items-center px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors shadow-md hover:shadow-lg"
                                          >
                                            <ArrowPathIcon className="w-4 h-4 ml-1" />
                                            سفارش مجدد
                                          </button>
                                          
                                          {/* Review Button (for delivered orders) */}
                                          {order.status === 'delivered' && (
                                            <button 
                                              onClick={() => {
                                                if (order.restaurant?.id) {
                                                  window.location.href = `/restaurants/${order.restaurant.id}#reviews`;
                                                } else {
                                                  alert('اطلاعات رستوران برای ثبت نظر در دسترس نیست');
                                                }
                                              }}
                                              className="flex items-center px-4 py-2 bg-yellow-500 text-white rounded-lg hover:bg-yellow-600 transition-colors shadow-md hover:shadow-lg"
                                            >
                                              <StarIcon className="w-4 h-4 ml-1" />
                                              نظر دهید
                                            </button>
                                          )}
                                          
                                          {/* Cancel Button (only for pending orders) */}
                                          {order.status === 'pending' && (
                                            <button 
                                              onClick={async () => {
                                                if (confirm('آیا از لغو این سفارش اطمینان دارید؟\n\nتوجه: پس از تأیید رستوران امکان لغو وجود نخواهد داشت.')) {
                                                  try {
                                                    await apiService.cancelOrder(order.id);
                                                    fetchUserData();
                                                    alert('سفارش با موفقیت لغو شد');
                                                  } catch (error: any) {
                                                    console.error('Error canceling order:', error);
                                                    
                                                    // Handle specific error messages from backend
                                                    if (error?.response?.data?.message) {
                                                      if (error.response.data.message.includes('after restaurant confirmation')) {
                                                        alert('امکان لغو سفارش پس از تأیید رستوران وجود ندارد.\nبرای انصراف لطفاً با رستوران تماس بگیرید.');
                                                      } else {
                                                        alert(error.response.data.message);
                                                      }
                                                    } else {
                                                      alert('خطا در لغو سفارش. لطفاً دوباره تلاش کنید.');
                                                    }
                                                    
                                                    // Refresh data to get latest order status
                                                    fetchUserData();
                                                  }
                                                }
                                              }}
                                              className="flex items-center px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors shadow-md hover:shadow-lg"
                                            >
                                              <XMarkIcon className="w-4 h-4 ml-1" />
                                              لغو سفارش
                                            </button>
                                          )}
                                          
                                          {/* Disabled Cancel Button (for confirmed+ orders) */}
                                          {['confirmed', 'preparing', 'ready'].includes(order.status) && (
                                            <div className="relative group">
                                              <button 
                                                disabled
                                                className="flex items-center px-4 py-2 bg-gray-300 text-gray-500 rounded-lg cursor-not-allowed shadow-md opacity-50"
                                              >
                                                <XMarkIcon className="w-4 h-4 ml-1" />
                                                لغو سفارش
                                              </button>
                                              
                                              {/* Tooltip */}
                                              <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-800 text-white text-xs rounded-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 whitespace-nowrap z-10">
                                                <div className="text-center">
                                                  <p>امکان لغو سفارش پس از تأیید رستوران وجود ندارد</p>
                                                  <p className="text-gray-300 mt-1">برای انصراف با رستوران تماس بگیرید</p>
                        </div>
                                                {/* Tooltip arrow */}
                                                <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-800"></div>
                                              </div>
                                            </div>
                                          )}
                                          
                                          {/* Support Button */}
                                          <button 
                                            onClick={() => {
                                              const phone = order.restaurant?.phone;
                                              if (phone) {
                                                if (confirm(`تماس با رستوران ${order.restaurant?.name}؟\nشماره: ${phone}`)) {
                                                  window.open(`tel:${phone}`, '_self');
                                                }
                                              } else {
                                                alert('شماره تماس رستوران در دسترس نیست. لطفاً با پشتیبانی تماس بگیرید: 093123456789');
                                              }
                                            }}
                                            className="flex items-center px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors shadow-md hover:shadow-lg"
                                          >
                                            <PhoneIcon className="w-4 h-4 ml-1" />
                                            تماس با رستوران
                                          </button>
                                          
                                          {/* Track Order (for active orders) */}
                                          {isActiveOrder && (
                                            <button 
                                              onClick={() => {
                                                alert(`سفارش شما در مرحله "${getStatusLabel(order.status)}" است.\n${order.estimated_delivery_time ? `زمان تحویل: ${order.estimated_delivery_time}` : 'زمان دقیق به زودی اعلام می‌شود.'}`);
                                              }}
                                              className="flex items-center px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors shadow-md hover:shadow-lg"
                                            >
                                              <ClockIcon className="w-4 h-4 ml-1" />
                                              پیگیری سفارش
                                            </button>
                                          )}
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            )}
                          </div>
                        );
                      })}
                    </div>
                  ) : (
                    <div className="text-center py-12">
                      <ShoppingBagIcon className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                      <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">هیچ سفارشی ندادید</h3>
                      <p className="text-gray-500 dark:text-gray-400 mb-6">اولین سفارش خود را ثبت کنید</p>
                      <Link
                        href="/restaurants"
                        className="inline-flex items-center px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
                      >
                        <PlusIcon className="w-4 h-4 ml-2" />
                        سفارش دهید
                      </Link>
                    </div>
                  )}
                </div>
              )}

                      {/* Reservations Tab - DISABLED */}
        {/* {activeTab === 'reservations' && (
                <div className="p-6">
                  <div className="mb-6">
                    <h2 className="text-2xl font-bold text-gray-900 dark:text-white">رزروهای شما</h2>
                    <p className="text-gray-600 dark:text-gray-400">رزروهای میز و وضعیت آن‌ها</p>
                  </div>

                  <div className="text-center py-12">
                    <CalendarIcon className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">سیستم رزرو به زودی</h3>
                    <p className="text-gray-500 dark:text-gray-400">امکان رزرو میز به زودی اضافه خواهد شد</p>
                  </div>
                </div>
              )} */}

              {/* Settings Tab */}
              {activeTab === 'settings' && (
                <div className="p-6">
                  <div className="mb-6">
                    <h2 className="text-2xl font-bold text-gray-900 dark:text-white">تنظیمات</h2>
                    <p className="text-gray-600 dark:text-gray-400">تنظیمات حساب کاربری و اعلان‌ها</p>
                  </div>

                  <div className="space-y-6">
                    <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
                      <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">اعلان‌ها</h3>
                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="font-medium text-gray-900 dark:text-white">اعلان‌های ایمیل</p>
                            <p className="text-sm text-gray-500 dark:text-gray-400">دریافت اعلان‌ها از طریق ایمیل</p>
                          </div>
                          <button className="relative inline-flex h-6 w-11 items-center rounded-full bg-gray-200 transition-colors focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2">
                            <span className="inline-block h-4 w-4 transform rounded-full bg-white shadow-lg transition-transform translate-x-1" />
                          </button>
                        </div>
                        
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="font-medium text-gray-900 dark:text-white">اعلان‌های پیامکی</p>
                            <p className="text-sm text-gray-500 dark:text-gray-400">دریافت اعلان‌ها از طریق پیامک</p>
                          </div>
                          <button className="relative inline-flex h-6 w-11 items-center rounded-full bg-primary-600 transition-colors focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2">
                            <span className="inline-block h-4 w-4 transform rounded-full bg-white shadow-lg transition-transform translate-x-6" />
                          </button>
                        </div>
                      </div>
                    </div>

                    <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
                      <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">حریم خصوصی</h3>
                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="font-medium text-gray-900 dark:text-white">نمایش پروفایل</p>
                            <p className="text-sm text-gray-500 dark:text-gray-400">نمایش پروفایل برای سایر کاربران</p>
                          </div>
                          <select className="rounded-lg border-gray-300 dark:border-gray-600 dark:bg-gray-800 dark:text-white">
                            <option value="public">عمومی</option>
                            <option value="private">خصوصی</option>
                          </select>
                        </div>
                      </div>
                    </div>
                  </div>
              </div>
              )}

            </div>
            </div>
          </div>
        </div>

      {/* Profile Edit Modal */}
      {isEditingProfile && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white">ویرایش پروفایل</h3>
                <button
                  onClick={() => setIsEditingProfile(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <XMarkIcon className="w-6 h-6" />
                </button>
          </div>

              <form onSubmit={(e) => {
                e.preventDefault()
                const formData = new FormData(e.currentTarget)
                const data = Object.fromEntries(formData.entries())
                
                // Map form data to backend structure
                const mappedData = {
                  bio: data.bio as string,
                  location: data.location as string,
                  birth_date: data.birth_date as string,
                  gender: data.gender as string,
                  food_preferences: data.food_preferences as string,
                  website: data.website as string,
                  language: data.language as string,
                }
                
                handleProfileUpdate(mappedData)
              }} className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      موقعیت/شهر
                    </label>
                    <input
                      type="text"
                      name="location"
                      defaultValue={userProfile?.location || ''}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      تاریخ تولد
                    </label>
                    <input
                      type="date"
                      name="birth_date"
                      defaultValue={userProfile?.birth_date || ''}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      جنسیت
                    </label>
                    <select
                      name="gender"
                      defaultValue={userProfile?.gender || 'prefer_not_to_say'}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                    >
                      <option value="prefer_not_to_say">تعریف نمی‌کنم</option>
                      <option value="male">مرد</option>
                      <option value="female">زن</option>
                      <option value="other">سایر</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      زبان
                    </label>
                    <select
                      name="language"
                      defaultValue={userProfile?.language || 'fa-IR'}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                    >
                      <option value="fa-IR">فارسی</option>
                      <option value="ps-AF">پشتو</option>
                      <option value="en-US">انگلیسی</option>
                    </select>
                  </div>

                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      وب‌سایت
                    </label>
                    <input
                      type="url"
                      name="website"
                      defaultValue={userProfile?.website || ''}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                      placeholder="https://example.com"
                    />
                  </div>

                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      درباره من
                    </label>
                    <textarea
                      name="bio"
                      rows={4}
                      defaultValue={userProfile?.bio || ''}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                    />
                  </div>

                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      ترجیحات غذایی
                    </label>
                    <textarea
                      name="food_preferences"
                      rows={3}
                      defaultValue={userProfile?.food_preferences || ''}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                      placeholder="مثال: گیاه‌خوار، بدون مغز، بدون تند"
                    />
                  </div>
                  </div>

                <div className="flex justify-end space-x-3 rtl:space-x-reverse pt-6 border-t">
                  <button
                    type="button"
                    onClick={() => setIsEditingProfile(false)}
                    className="px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                  >
                    انصراف
                  </button>
                      <button
                        type="submit"
                    className="px-6 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
                      >
                        ذخیره تغییرات
                      </button>
                    </div>
                </form>
              </div>
                </div>
              </div>
            )}

      <Footer />
    </div>
  )
} 