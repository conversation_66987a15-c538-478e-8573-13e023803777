import { io, Socket } from 'socket.io-client';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Platform } from 'react-native';

import { 
  WebSocketMessage, 
  ConnectionStatus, 
  OrderUpdate, 
  ChatMessage, 
  PushNotification 
} from '../lib/api';

export interface WebSocketEventHandlers {
  onOrderUpdate?: (update: OrderUpdate) => void;
  onChatMessage?: (message: ChatMessage) => void;
  onNotification?: (notification: PushNotification) => void;
  onConnect?: () => void;
  onDisconnect?: (reason: string) => void;
  onReconnect?: () => void;
  onError?: (error: Error) => void;
}

class WebSocketService {
  private socket: Socket | null = null;
  private connectionStatus: ConnectionStatus = {
    connected: false,
    reconnecting: false,
    reconnectAttempts: 0
  };
  private eventHandlers: WebSocketEventHandlers = {};
  private userId: string | null = null;
  private currentOrderId: string | null = null;
  private reconnectTimer: ReturnType<typeof setTimeout> | null = null;
  private heartbeatTimer: ReturnType<typeof setInterval> | null = null;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000; // Start with 1 second
  private maxReconnectDelay = 30000; // Max 30 seconds

  private readonly WEBSOCKET_URL = __DEV__ 
    ? 'http://localhost:7000' 
    : 'https://api.azkuja.com';

  constructor() {
    this.loadStoredSettings();
  }

  private async loadStoredSettings() {
    try {
      const stored = await AsyncStorage.getItem('websocket_settings');
      if (stored) {
        const settings = JSON.parse(stored);
        this.userId = settings.userId;
      }
    } catch (error) {
      console.error('Error loading WebSocket settings:', error);
    }
  }

  private async saveSettings() {
    try {
      const settings = {
        userId: this.userId,
        lastConnected: new Date().toISOString()
      };
      await AsyncStorage.setItem('websocket_settings', JSON.stringify(settings));
    } catch (error) {
      console.error('Error saving WebSocket settings:', error);
    }
  }

  // Initialize WebSocket connection
  connect(userId: string, handlers: WebSocketEventHandlers = {}) {
    if (this.socket?.connected) {
      console.log('🔌 WebSocket already connected');
      return;
    }

    this.userId = userId;
    this.eventHandlers = handlers;
    this.connectionStatus.reconnectAttempts = 0;

    console.log(`🔌 Connecting to WebSocket: ${this.WEBSOCKET_URL}`);

    try {
      this.socket = io(this.WEBSOCKET_URL, {
        transports: ['websocket', 'polling'],
        timeout: 10000,
        forceNew: false,
        autoConnect: true,
        reconnection: true,
        reconnectionAttempts: this.maxReconnectAttempts,
        reconnectionDelay: this.reconnectDelay,
        reconnectionDelayMax: this.maxReconnectDelay,
        query: {
          userId,
          platform: Platform.OS,
          version: '1.0.0'
        }
      });

      this.setupEventListeners();
      this.saveSettings();

    } catch (error) {
      console.error('❌ Error creating WebSocket connection:', error);
      this.eventHandlers.onError?.(error as Error);
    }
  }

  private setupEventListeners() {
    if (!this.socket) return;

    // Connection events
    this.socket.on('connect', () => {
      console.log('✅ WebSocket connected:', this.socket?.id);
      this.connectionStatus = {
        connected: true,
        reconnecting: false,
        lastConnected: new Date().toISOString(),
        reconnectAttempts: 0
      };

      this.clearReconnectTimer();
      this.startHeartbeat();
      
      // Join user room for personal notifications
      if (this.userId) {
        this.socket?.emit('joinCustomerOrders', { userId: this.userId });
      }

      this.eventHandlers.onConnect?.();
    });

    this.socket.on('disconnect', (reason) => {
      console.log('❌ WebSocket disconnected:', reason);
      this.connectionStatus.connected = false;
      this.stopHeartbeat();
      
      this.eventHandlers.onDisconnect?.(reason);

      // Auto-reconnect for certain disconnect reasons
      if (reason === 'io server disconnect' || reason === 'transport close') {
        this.scheduleReconnect();
      }
    });

    this.socket.on('connect_error', (error) => {
      console.error('❌ WebSocket connection error:', error);
      this.connectionStatus.connected = false;
      this.connectionStatus.reconnectAttempts++;
      
      this.eventHandlers.onError?.(error as Error);
      this.scheduleReconnect();
    });

    this.socket.on('reconnect', (attemptNumber) => {
      console.log(`🔄 WebSocket reconnected after ${attemptNumber} attempts`);
      this.connectionStatus.reconnecting = false;
      this.connectionStatus.reconnectAttempts = 0;
      
      this.eventHandlers.onReconnect?.();
    });

    this.socket.on('reconnect_attempt', (attemptNumber) => {
      console.log(`🔄 WebSocket reconnect attempt ${attemptNumber}`);
      this.connectionStatus.reconnecting = true;
      this.connectionStatus.reconnectAttempts = attemptNumber;
    });

    this.socket.on('reconnect_failed', () => {
      console.error('❌ WebSocket reconnection failed after max attempts');
      this.connectionStatus.reconnecting = false;
      this.eventHandlers.onError?.(new Error('Failed to reconnect after maximum attempts'));
    });

    // Custom event handlers
    this.socket.on('orderStatusUpdated', (data: { order: any; message: string }) => {
      console.log('📱 Order status update received:', data);
      
      const orderUpdate: OrderUpdate = {
        id: Date.now().toString(),
        order_id: data.order.id,
        status: data.order.status,
        message: data.message,
        timestamp: new Date().toISOString(),
        estimated_time: data.order.estimated_delivery_time
      };

      this.eventHandlers.onOrderUpdate?.(orderUpdate);
    });

    this.socket.on('newChatMessage', (message: ChatMessage) => {
      console.log('💬 New chat message received:', message);
      this.eventHandlers.onChatMessage?.(message);
    });

    this.socket.on('notification', (notification: PushNotification) => {
      console.log('🔔 New notification received:', notification);
      this.eventHandlers.onNotification?.(notification);
    });

    // System events
    this.socket.on('systemMessage', (data: { type: string; message: string }) => {
      console.log('🔧 System message:', data);
    });

    this.socket.on('pong', (data) => {
      console.log('🏓 Pong received:', data);
    });

    // Debug: Log all events in development
    if (__DEV__) {
      this.socket.onAny((event, ...args) => {
        console.log(`🔄 WebSocket event [${event}]:`, args);
      });
    }
  }

  private scheduleReconnect() {
    if (this.reconnectTimer || this.connectionStatus.reconnectAttempts >= this.maxReconnectAttempts) {
      return;
    }

    const delay = Math.min(
      this.reconnectDelay * Math.pow(2, this.connectionStatus.reconnectAttempts),
      this.maxReconnectDelay
    );

    console.log(`⏰ Scheduling reconnect in ${delay}ms (attempt ${this.connectionStatus.reconnectAttempts + 1})`);
    
    this.reconnectTimer = setTimeout(() => {
      this.clearReconnectTimer();
      this.manualReconnect();
    }, delay);
  }

  private clearReconnectTimer() {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }
  }

  private startHeartbeat() {
    this.stopHeartbeat();
    
    this.heartbeatTimer = setInterval(() => {
      if (this.socket?.connected) {
        this.socket.emit('ping', { timestamp: Date.now() });
      }
    }, 30000); // Ping every 30 seconds
  }

  private stopHeartbeat() {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer);
      this.heartbeatTimer = null;
    }
  }

  // Manual reconnection
  manualReconnect() {
    if (!this.userId) {
      console.error('❌ Cannot reconnect: No user ID');
      return;
    }

    if (this.socket?.connected) {
      console.log('🔌 Already connected');
      return;
    }

    console.log('🔄 Manual reconnect initiated');
    this.connectionStatus.reconnecting = true;
    
    if (this.socket) {
      this.socket.disconnect();
      this.socket.removeAllListeners();
    }

    // Reconnect with same settings
    setTimeout(() => {
      this.connect(this.userId!, this.eventHandlers);
    }, 1000);
  }

  // Join order tracking
  joinOrderTracking(orderId: string) {
    if (!this.socket?.connected) {
      console.warn('⚠️ Cannot join order tracking: Not connected');
      return;
    }

    this.currentOrderId = orderId;
    this.socket.emit('joinOrderTracking', { orderId, userId: this.userId });
    console.log(`📦 Joined order tracking for order: ${orderId}`);
  }

  // Leave order tracking
  leaveOrderTracking(orderId: string) {
    if (!this.socket?.connected) {
      return;
    }

    this.socket.emit('leaveOrderTracking', { orderId, userId: this.userId });
    console.log(`📦 Left order tracking for order: ${orderId}`);
    
    if (this.currentOrderId === orderId) {
      this.currentOrderId = null;
    }
  }

  // Send chat message
  sendChatMessage(orderId: string, message: string, messageType: ChatMessage['message_type'] = 'text') {
    if (!this.socket?.connected) {
      console.warn('⚠️ Cannot send message: Not connected');
      return Promise.reject(new Error('Not connected'));
    }

    return new Promise<void>((resolve, reject) => {
      this.socket?.emit('sendChatMessage', {
        orderId,
        message,
        messageType,
        userId: this.userId
      }, (response: { success: boolean; error?: string }) => {
        if (response.success) {
          resolve();
        } else {
          reject(new Error(response.error || 'Failed to send message'));
        }
      });
    });
  }

  // Update event handlers
  updateEventHandlers(handlers: Partial<WebSocketEventHandlers>) {
    this.eventHandlers = { ...this.eventHandlers, ...handlers };
  }

  // Get connection status
  getConnectionStatus(): ConnectionStatus {
    return { ...this.connectionStatus };
  }

  // Check if connected
  isConnected(): boolean {
    return this.socket?.connected || false;
  }

  // Disconnect
  disconnect() {
    console.log('🔌 Disconnecting WebSocket');
    
    this.clearReconnectTimer();
    this.stopHeartbeat();
    
    if (this.currentOrderId) {
      this.leaveOrderTracking(this.currentOrderId);
    }

    if (this.socket) {
      this.socket.removeAllListeners();
      this.socket.disconnect();
      this.socket = null;
    }

    this.connectionStatus = {
      connected: false,
      reconnecting: false,
      reconnectAttempts: 0
    };

    this.userId = null;
    this.currentOrderId = null;
    this.eventHandlers = {};
  }

  // Send custom event
  emit(event: string, data: any) {
    if (!this.socket?.connected) {
      console.warn(`⚠️ Cannot emit ${event}: Not connected`);
      return;
    }

    this.socket.emit(event, data);
  }

  // Listen to custom event
  on(event: string, handler: (...args: any[]) => void) {
    if (!this.socket) {
      console.warn(`⚠️ Cannot listen to ${event}: Socket not initialized`);
      return;
    }

    this.socket.on(event, handler);
  }

  // Remove event listener
  off(event: string, handler?: (...args: any[]) => void) {
    if (!this.socket) {
      return;
    }

    if (handler) {
      this.socket.off(event, handler);
    } else {
      this.socket.removeAllListeners(event);
    }
  }
}

// Export singleton instance
export const webSocketService = new WebSocketService();
export default webSocketService; 