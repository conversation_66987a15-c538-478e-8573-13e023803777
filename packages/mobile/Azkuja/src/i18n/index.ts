import i18n from 'i18next';
import { initReactI18next, useTranslation as useReactI18nextTranslation } from 'react-i18next';
import * as Localization from 'expo-localization';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Import translations
import { dari } from './translations/dari';
import { pashto } from './translations/pashto';
import { english } from './translations/english';

// Language configurations
export const languages = {
  'fa-AF': {
    name: 'دری',
    nativeName: 'دری',
    code: 'fa-AF',
    isRTL: true,
  },
  'ps-AF': {
    name: 'پښتو',
    nativeName: 'پښتو', 
    code: 'ps-AF',
    isRTL: true,
  },
  'en-US': {
    name: 'English',
    nativeName: 'English',
    code: 'en-US',
    isRTL: false,
  },
} as const;

export type SupportedLanguage = keyof typeof languages;
export type LanguageConfig = typeof languages[SupportedLanguage];

// Translation resources
const resources = {
  'fa-AF': { translation: dari },
  'ps-AF': { translation: pashto },
  'en-US': { translation: english },
};

// Get default language - Always start with Dari for Afghan users
const getDefaultLanguage = (): SupportedLanguage => {
  // Always default to Dari (can be changed by user later)
  return 'fa-AF';
};

// Initialize i18n
i18n
  .use(initReactI18next)
  .init({
    resources,
    lng: getDefaultLanguage(),
    fallbackLng: 'fa-AF', // Default to Dari
    
    interpolation: {
      escapeValue: false, // React already does escaping
    },
    
    react: {
      useSuspense: false, // Important for React Native
    },
    
    // Cache configuration
    saveMissing: false,
    debug: __DEV__,
  });

// Load saved language after initialization
const loadSavedLanguage = async () => {
  try {
    const stored = await AsyncStorage.getItem('app_language');
    if (stored && stored in languages && stored !== i18n.language) {
      console.log('Loading saved language:', stored);
      await i18n.changeLanguage(stored);
    }
  } catch (error) {
    console.warn('Failed to load saved language:', error);
  }
};

// Load saved language on startup
loadSavedLanguage();

// Language management functions
export const changeLanguage = async (language: SupportedLanguage): Promise<void> => {
  try {
    console.log('Changing language to:', language);
    await i18n.changeLanguage(language);
    await AsyncStorage.setItem('app_language', language);
    console.log('Language changed successfully to:', language);
  } catch (error) {
    console.error('Failed to change language:', error);
  }
};

export const getCurrentLanguage = (): SupportedLanguage => {
  return (i18n.language as SupportedLanguage) || 'fa-AF';
};

export const getCurrentLanguageConfig = (): LanguageConfig => {
  const currentLang = getCurrentLanguage();
  return languages[currentLang] || languages['fa-AF'];
};

export const isRTL = (): boolean => {
  const config = getCurrentLanguageConfig();
  return config.isRTL;
};

// Custom hook for translations with proper typing
export const useTranslation = () => {
  const { t, i18n: i18nInstance, ...rest } = useReactI18nextTranslation();
  
  const currentLanguage = getCurrentLanguage();
  const currentConfig = getCurrentLanguageConfig();
  
  return {
    t: (key: string, options?: any): string => {
      try {
        const result = t(key, options);
        return typeof result === 'string' ? result : key;
      } catch (error) {
        console.warn(`Translation error for key: ${key}`, error);
        return key;
      }
    },
    i18n: i18nInstance,
    isRTL: currentConfig.isRTL,
    language: currentLanguage,
    currentLanguage,
    currentLanguageConfig: currentConfig,
    ...rest,
  };
};

// Translation function for use outside of components
export const translate = (key: string, options?: any): string => {
  try {
    const result = i18n.t(key, options);
    return String(result);
  } catch (error) {
    console.warn(`Translation error for key: ${key}`, error);
    return key;
  }
};

export default i18n; 