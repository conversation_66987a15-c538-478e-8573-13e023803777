export const english = {
  // Common
  common: {
    loading: 'Loading...',
    error: 'Error',
    success: 'Success',
    cancel: 'Cancel',
    save: 'Save',
    delete: 'Delete',
    edit: 'Edit',
    back: 'Back',
    next: 'Next',
    previous: 'Previous',
    search: 'Search',
    filter: 'Filter',
    sort: 'Sort',
    yes: 'Yes',
    no: 'No',
    ok: 'OK',
    done: 'Done',
    close: 'Close',
    open: 'Open',
    submit: 'Submit',
    refresh: 'Refresh',
    confirm: 'Confirm',
    retry: 'Retry',
    comingSoon: 'Coming Soon...',
    select: 'Select',
    clear: 'Clear',
    apply: 'Apply',
    reset: 'Reset',
  },

  // Navigation
  navigation: {
    home: 'Home',
    explore: 'Explore',
    orders: 'Orders',
    profile: 'Profile',
    restaurants: 'Restaurants',
    menu: 'Menu',
    cart: 'Cart',
    favorites: 'Favorites',
    reservations: 'Reservations',
    notifications: 'Notifications',
    settings: 'Settings',
    search: 'Search',
  },

  // Authentication
  auth: {
    login: 'Login',
    logout: 'Logout',
    register: 'Register',
    email: 'Email',
    password: 'Password',
    confirmPassword: 'Confirm Password',
    forgotPassword: 'Forgot Password?',
    resetPassword: 'Reset Password',
    firstName: 'First Name',
    lastName: 'Last Name',
    phoneNumber: 'Phone Number',
    loginSuccess: 'Login successful',
    loginError: 'Login failed',
    registerSuccess: 'Registration successful',
    registerError: 'Registration failed',
    invalidCredentials: 'Invalid credentials',
    emailRequired: 'Email is required',
    passwordRequired: 'Password is required',
    passwordTooShort: 'Password must be at least 6 characters',
    passwordsDoNotMatch: 'Passwords do not match',
  },

  // Restaurant
  restaurant: {
    restaurant: 'Restaurant',
    restaurants: 'Restaurants',
    menu: 'Menu',
    categories: 'Categories',
    popular: 'Popular',
    nearYou: 'Near You',
    topRated: 'Top Rated',
    fastDelivery: 'Fast Delivery',
    newRestaurants: 'New Restaurants',
    cuisineType: 'Cuisine Type',
    deliveryTime: 'Delivery Time',
    deliveryFee: 'Delivery Fee',
    minimumOrder: 'Minimum Order',
    rating: 'Rating',
    reviews: 'Reviews',
    openNow: 'Open Now',
    closed: 'Closed',
    distance: 'Distance',
    name: 'Restaurant Name',
    cuisine: 'Cuisine Type',
    info: 'Info',
    featured: 'Featured',
    new: 'New',
  },

  // Food & Menu
  food: {
    addToCart: 'Add to Cart',
    removeFromCart: 'Remove from Cart',
    quantity: 'Quantity',
    price: 'Price',
    discount: 'Discount',
    total: 'Total',
    subtotal: 'Subtotal',
    tax: 'Tax',
    description: 'Description',
    ingredients: 'Ingredients',
    allergies: 'Allergies',
    spicyLevel: 'Spicy Level',
    mild: 'Mild',
    medium: 'Medium',
    hot: 'Hot',
    veryHot: 'Very Hot',
    vegetarian: 'Vegetarian',
    vegan: 'Vegan',
    glutenFree: 'Gluten Free',
    halal: 'Halal',
  },

  // Orders
  orders: {
    order: 'Order',
    orders: 'Orders',
    orderHistory: 'Order History',
    activeOrders: 'Active Orders',
    orderDetails: 'Order Details',
    orderNumber: 'Order Number',
    orderStatus: 'Order Status',
    orderDate: 'Order Date',
    deliveryAddress: 'Delivery Address',
    paymentMethod: 'Payment Method',
    trackOrder: 'Track Order',
    cancelOrder: 'Cancel Order',
    reorder: 'Reorder',
    orderConfirmed: 'Order Confirmed',
    preparing: 'Preparing',
    onTheWay: 'On the Way',
    delivered: 'Delivered',
    cancelled: 'Cancelled',
    myOrders: 'My Orders',
    pending: 'Pending',
    confirmed: 'Confirmed',
    ready: 'Ready',
  },

  // Payment
  payment: {
    payment: 'Payment',
    paymentMethod: 'Payment Method',
    cash: 'Cash',
    card: 'Card',
    wallet: 'Wallet',
    payOnDelivery: 'Pay on Delivery',
    payNow: 'Pay Now',
    paymentSuccess: 'Payment Successful',
    paymentFailed: 'Payment Failed',
    amount: 'Amount',
    currency: 'Currency',
    afghani: 'Afghani',
  },

  // Profile
  profile: {
    profile: 'Profile',
    personalInfo: 'Personal Information',
    addresses: 'Addresses',
    paymentMethods: 'Payment Methods',
    notifications: 'Notifications',
    language: 'Language',
    darkMode: 'Dark Mode',
    aboutUs: 'About Us',
    helpSupport: 'Help & Support',
    contactUs: 'Contact Us',
    termsConditions: 'Terms & Conditions',
    privacyPolicy: 'Privacy Policy',
    version: 'Version',
    myProfile: 'My Profile',
    logout: 'Logout',
    deleteAccount: 'Delete Account',
  },

  // Time & Date
  time: {
    now: 'Now',
    today: 'Today',
    yesterday: 'Yesterday',
    tomorrow: 'Tomorrow',
    minutes: 'Minutes',
    hours: 'Hours',
    days: 'Days',
    weeks: 'Weeks',
    months: 'Months',
    years: 'Years',
    am: 'AM',
    pm: 'PM',
  },

  // Errors
  errors: {
    networkError: 'Network Error',
    serverError: 'Server Error',
    notFound: 'Not Found',
    unauthorized: 'Unauthorized',
    forbidden: 'Forbidden',
    validation: 'Validation Error',
    unknown: 'Unknown Error',
    goBack: 'Go Back',
  },

  // Settings
  settings: {
    settings: 'Settings',
    general: 'General',
    account: 'Account',
    privacy: 'Privacy',
    security: 'Security',
    notifications: 'Notifications',
    language: 'Language',
    theme: 'Theme',
    location: 'Location',
    permissions: 'Permissions',
  },

  // Location
  location: {
    currentLocation: 'Current Location',
    deliveryTo: 'Deliver to',
    address: 'Address',
    city: 'City',
    area: 'Area',
    landmark: 'Landmark',
    saveLocation: 'Save Location',
    locationPermission: 'Location Permission',
    enableLocation: 'Enable Location',
  },
} as const; 