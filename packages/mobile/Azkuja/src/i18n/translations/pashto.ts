export const pashto = {
  // Common
  common: {
    loading: 'بارویونکي...',
    error: 'ستونزه',
    success: 'بریالیتوب',
    cancel: 'لغوه',
    save: 'ساتل',
    delete: 'ړنګول',
    edit: 'سمول',
    back: 'بیرته',
    next: 'راتلونکی',
    previous: 'پخوانی',
    search: 'پلټنه',
    filter: 'چاڼه',
    sort: 'ډول',
    yes: 'هو',
    no: 'نه',
    ok: 'سمه ده',
    done: 'بشپړ',
    close: 'بندول',
    open: 'پرانیستل',
    submit: 'وړاندې',
    refresh: 'تازه کول',
    confirm: 'تصدیق',
    retry: 'بیا هڅه وکړئ',
    comingSoon: 'ډېر ژر راځي...',
    select: 'ټاکل',
    clear: 'پاکول',
    apply: 'پلي کول',
    reset: 'بیرته تنظیم',
  },

  // Navigation
  navigation: {
    home: 'کور',
    explore: 'پلټل',
    orders: 'امرونه',
    profile: 'پېژنلیک',
    restaurants: 'خوړن ځایونه',
    menu: 'لیسټ',
    cart: 'پیرودونو ټوکری',
    favorites: 'مینه لرونکي',
    reservations: 'ځای ساتنه',
    notifications: 'خبرتیاوې',
    settings: 'امیتونه',
  },

  // Authentication
  auth: {
    login: 'ننوتل',
    logout: 'وتل',
    register: 'نوملیکنه',
    email: 'بریښنالیک',
    password: 'پټنوم',
    confirmPassword: 'د پټنوم تصدیق',
    forgotPassword: 'پټنوم مو هیر شوی؟',
    resetPassword: 'پټنوم بیا ګورول',
    firstName: 'نوم',
    lastName: 'د پلار نوم',
    phoneNumber: 'د تلیفون شمیره',
    loginSuccess: 'په بریالیتوب سره ننوتلاست',
    loginError: 'د ننوتلو ستونزه',
    registerSuccess: 'بریالۍ نوملیکنه',
    registerError: 'د نوملیکنې ستونزه',
    invalidCredentials: 'دا معلومات سم نه دي',
    emailRequired: 'بریښنالیک اړین دی',
    passwordRequired: 'پټنوم اړین دی',
    passwordTooShort: 'پټنوم باید لږترلږه ۶ توري ولري',
    passwordsDoNotMatch: 'پټنومونه سم نه دي',
  },

  // Restaurant
  restaurant: {
    restaurant: 'خوړن ځای',
    restaurants: 'خوړن ځایونه',
    menu: 'لیسټ',
    categories: 'ډولونه',
    popular: 'مشهور',
    nearYou: 'ستاسو سره نږدې',
    topRated: 'لوړ درجه',
    fastDelivery: 'ګړندۍ رسول',
    newRestaurants: 'نوي خوړن ځایونه',
    cuisineType: 'د خوړو ډول',
    deliveryTime: 'د رسولو وخت',
    deliveryFee: 'د رسولو فیس',
    minimumOrder: 'لږترلږه امر',
    rating: 'درجه ورکول',
    reviews: 'نظرونه',
    openNow: 'اوس پرانیستی',
    closed: 'تړلی',
    distance: 'واټن',
  },

  // Food & Menu
  food: {
    addToCart: 'ټوکرۍ ته ورزیاتول',
    removeFromCart: 'له ټوکرۍ څخه لیرې کول',
    quantity: 'اندازه',
    price: 'قیمت',
    discount: 'تخفیف',
    total: 'ټولې',
    subtotal: 'د برخې ټولې',
    tax: 'مالیه',
    description: 'تشریحات',
    ingredients: 'جوړونکي توکي',
    allergies: 'حساسیتونه',
    spicyLevel: 'د تندۍ کچه',
    mild: 'نرم',
    medium: 'منځنی',
    hot: 'تند',
    veryHot: 'ډېر تند',
    vegetarian: 'نباتي',
    vegan: 'ويګان',
    glutenFree: 'له ګلوټن پرته',
    halal: 'حلال',
  },

  // Orders
  orders: {
    order: 'امر',
    orders: 'امرونه',
    orderHistory: 'د امرونو پېښلیک',
    activeOrders: 'فعاله امرونه',
    orderDetails: 'د امر تفصیلات',
    orderNumber: 'د امر شمیره',
    orderStatus: 'د امر حالت',
    orderDate: 'د امر نیټه',
    deliveryAddress: 'د رسولو پته',
    paymentMethod: 'د تادیاتو لاره',
    trackOrder: 'د امر تعقیب',
    cancelOrder: 'امر لغوه کول',
    reorder: 'بیا امر',
    orderConfirmed: 'امر تصدیق شو',
    preparing: 'په چمتو کولو کې',
    onTheWay: 'په لاره کې',
    delivered: 'رسیدلی',
    cancelled: 'لغوه شوی',
  },

  // Payment
  payment: {
    payment: 'تادیات',
    paymentMethod: 'د تادیاتو لاره',
    cash: 'نغدي',
    card: 'کارټ',
    wallet: 'بټوه',
    payOnDelivery: 'د رسولو پر وخت تادیات',
    payNow: 'اوس ورکړئ',
    paymentSuccess: 'بریالي تادیات',
    paymentFailed: 'ناکام تادیات',
    amount: 'اندازه',
    currency: 'پیسې',
    afghani: 'افغانۍ',
  },

  // Profile
  profile: {
    profile: 'پېژنلیک',
    personalInfo: 'شخصي معلومات',
    addresses: 'پتې',
    paymentMethods: 'د تادیاتو لارې',
    notifications: 'خبرتیاوې',
    language: 'ژبه',
    darkMode: 'تیاره حالت',
    aboutUs: 'زموږ په اړه',
    helpSupport: 'مرسته او ملاتړ',
    contactUs: 'موږ سره اړیکه',
    termsConditions: 'شرایط او قوانین',
    privacyPolicy: 'د محرمیت پالیسي',
    version: 'بڼه',
  },

  // Time & Date
  time: {
    now: 'اوس',
    today: 'نن',
    yesterday: 'پرون',
    tomorrow: 'سبا',
    minutes: 'دقیقې',
    hours: 'ساعتونه',
    days: 'ورځې',
    weeks: 'اونۍ',
    months: 'میاشتې',
    years: 'کلونه',
    am: 'سهار',
    pm: 'ماښام',
  },

  // Errors
  errors: {
    networkError: 'د شبکې ستونزه',
    serverError: 'د سرور ستونزه',
    notFound: 'ونه موندل شو',
    unauthorized: 'غیر مجاز',
    forbidden: 'منع',
    validation: 'د تصدیق ستونزه',
    unknown: 'نامعلومه ستونزه',
    retry: 'بیا هڅه',
    goBack: 'بیرته لاړ شه',
  },

  // Settings
  settings: {
    settings: 'امیتونه',
    general: 'عمومي',
    account: 'حساب',
    privacy: 'محرمیت',
    security: 'امنیت',
    notifications: 'خبرتیاوې',
    language: 'ژبه',
    theme: 'ښکلا',
    location: 'ځای',
    permissions: 'اجازې',
  },

  // Location
  location: {
    currentLocation: 'اوسنی ځای',
    deliveryTo: 'رسول دلته',
    address: 'پته',
    city: 'ښار',
    area: 'سیمه',
    landmark: 'نښه',
    saveLocation: 'ځای ساتل',
    locationPermission: 'د ځای اجازه',
    enableLocation: 'ځای فعالول',
  },
} as const; 