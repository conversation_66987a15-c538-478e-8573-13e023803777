export const dari = {
  // Common
  common: {
    loading: 'در حال بارگذاری...',
    error: 'خطا',
    success: 'موفقیت',
    cancel: 'لغو',
    save: 'ذخیره',
    delete: 'حذف',
    edit: 'ویرایش',
    back: 'برگشت',
    next: 'بعدی',
    previous: 'قبلی',
    search: 'جستجو',
    filter: 'فیلتر',
    sort: 'مرتب سازی',
    yes: 'بله',
    no: 'خیر',
    ok: 'تأیید',
    done: 'تمام',
    close: 'بستن',
    open: 'باز کردن',
    submit: 'ارسال',
    refresh: 'تازه سازی',
    confirm: 'تأیید',
    retry: 'دوباره تلاش کنید',
    comingSoon: 'به زودی...',
    select: 'انتخاب',
    deselect: 'لغو انتخاب',
    clear: 'پاک کردن',
    apply: 'اعمال',
    reset: 'بازنشانی',
    settings: 'تنظیمات',
    help: 'کمک',
    about: 'درباره',
    version: 'نسخه',
    update: 'به‌روزرسانی',
    download: 'دانلود',
    upload: 'بارگذاری',
    share: 'اشتراک‌گذاری',
    copy: 'کپی',
    paste: 'چسباندن',
    cut: 'برش',
    undo: 'برگشت',
    redo: 'تکرار',
  },

  // Navigation
  navigation: {
    home: 'خانه',
    explore: 'کاوش',
    orders: 'سفارشات',
    profile: 'پروفایل',
    restaurants: 'رستوران‌ها',
    menu: 'منو',
    cart: 'سبد خرید',
    favorites: 'علاقه‌مندی‌ها',
    reservations: 'رزرو',
    notifications: 'اعلانات',
    settings: 'تنظیمات',
    search: 'جستجو',
  },

  // Authentication
  auth: {
    login: 'ورود',
    logout: 'خروج',
    register: 'ثبت نام',
    email: 'ایمیل',
    password: 'رمز عبور',
    confirmPassword: 'تأیید رمز عبور',
    forgotPassword: 'رمز عبور را فراموش کرده‌اید؟',
    resetPassword: 'بازنشانی رمز عبور',
    firstName: 'نام',
    lastName: 'نام خانوادگِی',
    phoneNumber: 'شماره تلفن',
    loginSuccess: 'با موفقیت وارد شدید',
    loginError: 'ورود ناموفق',
    registerSuccess: 'ثبت نام موفق',
    registerError: 'ثبت نام ناموفق',
    invalidCredentials: 'اطلاعات وارد شده صحیح نیست',
    emailRequired: 'ایمیل الزامی است',
    passwordRequired: 'رمز عبور الزامی است',
    passwordTooShort: 'رمز عبور باید حداقل ۶ کاراکتر باشد',
    passwordsDoNotMatch: 'رمزهای عبور مطابقت ندارند',
    // Phone Login specific
    welcomeBack: 'خوش آمدید',
    enterVerificationCode: 'کد تأیید را وارد کنید',
    verificationCode: 'کد تأیید',
    enterPhoneNumber: 'شماره تلفن خود را وارد کنید',
    sendCode: 'ارسال کد',
    verifyAndLogin: 'تأیید و ورود',
    changePhoneNumber: 'تغییر شماره تلفن',
    dontHaveAccount: 'حساب کاربری ندارید؟',
    phoneNumberPlaceholder: '۰۷۸۱۲۳۴۵۶۷',
    verificationCodePlaceholder: '۱۲۳۴۵۶',
    phoneRequired: 'شماره تلفن الزامی است',
    invalidPhoneFormat: 'شماره تلفن باید ۱۰ رقم باشد و با ۰۷ شروع شود (مثال: ۰۷۸۱۲۳۴۵۶۷)',
    verificationCodeRequired: 'کد تأیید الزامی است',
    invalidVerificationCode: 'کد تأیید نامعتبر',
    verificationCodeMustBe6Digits: 'کد تأیید باید ۶ رقم باشد',
    secureAfghanAuth: '🇦🇫 تأیید هویت امن افغانستان',
    phoneFormatHelp: '📞 فورمت: ۰۷ + ۸ رقم (مثال: ۰۷۸۱۲۳۴۵۶۷)',
    testVerificationCode: '🧪 کد تأیید تستی: ۱۲۳۴۵۶',
    verificationCodeSentTo: 'کد تأیید به {{phone}} ارسال شد',
    back: 'برگشت',
  },

  // Restaurant
  restaurant: {
    restaurant: 'رستوران',
    restaurants: 'رستوران‌ها',
    menu: 'منو',
    categories: 'دسته‌بندی‌ها',
    popular: 'محبوب',
    nearYou: 'نزدیک شما',
    topRated: 'بالاترین امتیاز',
    fastDelivery: 'تحویل سریع',
    newRestaurants: 'رستوران‌های جدید',
    cuisineType: 'نوع غذا',
    deliveryTime: 'زمان تحویل',
    deliveryFee: 'هزینه تحویل',
    minimumOrder: 'حداقل سفارش',
    rating: 'امتیاز',
    reviews: 'نظرات',
    openNow: 'اکنون باز',
    closed: 'بسته',
    distance: 'فاصله',
    name: 'نام رستوران',
    cuisine: 'نوع غذا',
    info: 'اطلاعات',
    featured: 'ویژه',
    new: 'جدید',
  },

  // Food & Menu
  food: {
    addToCart: 'افزودن به سبد',
    removeFromCart: 'حذف از سبد',
    quantity: 'تعداد',
    price: 'قیمت',
    discount: 'تخفیف',
    total: 'مجموع',
    subtotal: 'جمع جزء',
    tax: 'مالیات',
    description: 'توضیحات',
    ingredients: 'مواد تشکیل دهنده',
    allergies: 'آلرژی‌ها',
    spicyLevel: 'سطح تندی',
    mild: 'ملایم',
    medium: 'متوسط',
    hot: 'تند',
    veryHot: 'خیلی تند',
    vegetarian: 'گیاهی',
    vegan: 'وگان',
    glutenFree: 'بدون گلوتن',
    halal: 'حلال',
  },

  // Orders
  orders: {
    order: 'سفارش',
    orders: 'سفارشات',
    orderHistory: 'تاریخچه سفارشات',
    activeOrders: 'سفارشات فعال',
    orderDetails: 'جزئیات سفارش',
    orderNumber: 'شماره سفارش',
    orderStatus: 'وضعیت سفارش',
    orderDate: 'تاریخ سفارش',
    deliveryAddress: 'آدرس تحویل',
    paymentMethod: 'روش پرداخت',
    trackOrder: 'پیگیری سفارش',
    cancelOrder: 'لغو سفارش',
    reorder: 'سفارش مجدد',
    orderConfirmed: 'سفارش تأیید شد',
    preparing: 'در حال آماده سازی',
    onTheWay: 'در راه',
    delivered: 'تحویل داده شد',
    cancelled: 'لغو شد',
    myOrders: 'سفارشات من',
    pending: 'در انتظار',
    confirmed: 'تأیید شده',
    ready: 'آماده',
  },

  // Payment
  payment: {
    payment: 'پرداخت',
    paymentMethod: 'روش پرداخت',
    cash: 'نقدی',
    card: 'کارت',
    wallet: 'کیف پول',
    payOnDelivery: 'پرداخت در محل تحویل',
    payNow: 'پرداخت کنید',
    paymentSuccess: 'پرداخت موفق',
    paymentFailed: 'پرداخت ناموفق',
    amount: 'مبلغ',
    currency: 'واحد پول',
    afghani: 'افغانی',
  },

  // Profile
  profile: {
    profile: 'پروفایل',
    personalInfo: 'اطلاعات شخصی',
    addresses: 'آدرس‌ها',
    paymentMethods: 'روش‌های پرداخت',
    notifications: 'اعلانات',
    language: 'زبان',
    darkMode: 'حالت تاریک',
    aboutUs: 'درباره ما',
    helpSupport: 'کمک و پشتیبانی',
    contactUs: 'تماس با ما',
    termsConditions: 'شرایط و قوانین',
    privacyPolicy: 'سیاست حفظ حریم خصوصی',
    version: 'نسخه',
    myProfile: 'پروفایل من',
    about: 'درباره',
    logout: 'خروج',
    deleteAccount: 'حذف حساب کاربری',
  },

  // Time & Date
  time: {
    now: 'اکنون',
    today: 'امروز',
    yesterday: 'دیروز',
    tomorrow: 'فردا',
    minutes: 'دقیقه',
    hours: 'ساعت',
    days: 'روز',
    weeks: 'هفته',
    months: 'ماه',
    years: 'سال',
    am: 'ق.ظ',
    pm: 'ب.ظ',
  },

  // Errors
  errors: {
    networkError: 'خطای شبکه',
    serverError: 'خطای سرور',
    notFound: 'یافت نشد',
    unauthorized: 'غیر مجاز',
    forbidden: 'ممنوع',
    validation: 'خطای اعتبارسنجی',
    unknown: 'خطای نامشخص',
    retry: 'تلاش مجدد',
    goBack: 'برگشت',
  },

  // Settings
  settings: {
    settings: 'تنظیمات',
    general: 'عمومی',
    account: 'حساب کاربری',
    privacy: 'حریم خصوصی',
    security: 'امنیت',
    notifications: 'اعلانات',
    language: 'زبان',
    theme: 'تم',
    location: 'موقعیت',
    permissions: 'مجوزها',
  },

  // Location
  location: {
    currentLocation: 'موقعیت فعلی',
    deliveryTo: 'تحویل به',
    address: 'آدرس',
    city: 'شهر',
    area: 'منطقه',
    landmark: 'نشانه',
    saveLocation: 'ذخیره مکان',
    locationPermission: 'مجوز موقعیت مکانی',
    enableLocation: 'فعال سازی موقعیت',
  },

  // Onboarding
  onboarding: {
    // ... existing code ...
  },
} as const; 