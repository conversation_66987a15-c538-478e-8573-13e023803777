import React from 'react';
import { Text as RNText, TextProps, TextStyle } from 'react-native';
import { useTranslation } from '../i18n';
import { fonts } from '../theme';

interface GlobalTextProps extends TextProps {
  variant?: 'heading' | 'body' | 'caption' | 'button';
  weight?: 'light' | 'regular' | 'medium' | 'semiBold' | 'bold';
}

// Global Text component that automatically applies YekanBakh fonts
export const GlobalText: React.FC<GlobalTextProps> = ({ 
  style, 
  variant = 'body',
  weight,
  children, 
  ...props 
}) => {
  const { isRTL } = useTranslation();

  // Auto-select font family based on language and variant
  const getFontFamily = (): string => {
    if (isRTL) {
      // For RTL languages (Dari/Pashto), use YekanBakh
      if (weight) {
        return fonts.yekanBakh[weight] || fonts.yekanBakh.regular;
      }
      
      switch (variant) {
        case 'heading':
          return fonts.yekanBakh.bold;
        case 'button':
          return fonts.yekanBakh.semiBold;
        case 'caption':
          return fonts.yekanBakh.regular;
        default:
          return fonts.yekanBakh.regular;
      }
    }
    
    // For LTR languages (English), use system fonts
    return fonts.system.regular;
  };

  const globalStyle: TextStyle = {
    fontFamily: getFontFamily(),
    textAlign: isRTL ? 'right' : 'left',
    writingDirection: isRTL ? 'rtl' : 'ltr',
  };

  const combinedStyle = [globalStyle, style];

  return (
    <RNText style={combinedStyle} {...props}>
      {children}
    </RNText>
  );
};

// Export as default for easy importing
export default GlobalText;

// Also export as Text so it can replace React Native Text imports
export { GlobalText as Text }; 