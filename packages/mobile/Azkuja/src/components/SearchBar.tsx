import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Modal,
  FlatList,
  Dimensions,
  Platform,
  Alert
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';

import { GlobalText } from './GlobalText';
import { useSearch } from '../contexts/SearchContext';
import { SearchSuggestion } from '../lib/api';

const { width } = Dimensions.get('window');

interface SearchBarProps {
  placeholder?: string;
  showVoiceSearch?: boolean;
  showAdvancedButton?: boolean;
  onSearchPress?: () => void;
  style?: any;
  autoFocus?: boolean;
}

type RootStackParamList = {
  AdvancedSearch: undefined;
};

type SearchBarNavigationProp = StackNavigationProp<RootStackParamList>;

export const SearchBar: React.FC<SearchBarProps> = ({
  placeholder = 'جستجوی رستوران، غذا...',
  showVoiceSearch = true,
  showAdvancedButton = true,
  onSearchPress,
  style,
  autoFocus = false
}) => {
  const navigation = useNavigation<SearchBarNavigationProp>();
  const {
    state,
    setQuery,
    performSearch,
    loadSuggestions,
    showSuggestions,
    hideSuggestions,
    searchByQuery
  } = useSearch();

  const [localQuery, setLocalQuery] = useState(state.currentQuery);
  const [isVoiceRecording, setIsVoiceRecording] = useState(false);
  const inputRef = useRef<TextInput>(null);

  useEffect(() => {
    if (localQuery !== state.currentQuery) {
      setLocalQuery(state.currentQuery);
    }
  }, [state.currentQuery]);

  // Load suggestions when query changes
  useEffect(() => {
    if (localQuery.length > 0) {
      const timer = setTimeout(() => {
        loadSuggestions(localQuery);
      }, 300);
      return () => clearTimeout(timer);
    }
  }, [localQuery, loadSuggestions]);

  const handleQueryChange = (text: string) => {
    setLocalQuery(text);
    if (text.length > 0) {
      showSuggestions();
    } else {
      hideSuggestions();
    }
  };

  const handleSearch = async () => {
    if (localQuery.trim()) {
      setQuery(localQuery);
      hideSuggestions();
      
      if (onSearchPress) {
        onSearchPress();
      } else {
        await searchByQuery(localQuery);
      }
    }
  };

  const handleSuggestionPress = (suggestion: SearchSuggestion) => {
    setLocalQuery(suggestion.text);
    setQuery(suggestion.text);
    hideSuggestions();
    
    if (onSearchPress) {
      onSearchPress();
    } else {
      searchByQuery(suggestion.text);
    }
  };

  const handleVoiceSearch = () => {
    // Voice search implementation would go here
    // For now, show a placeholder alert
    Alert.alert(
      'جستجوی صوتی',
      'قابلیت جستجوی صوتی به زودی اضافه خواهد شد',
      [{ text: 'باشه', style: 'default' }]
    );
  };

  const handleAdvancedSearch = () => {
    navigation.navigate('AdvancedSearch');
  };

  const renderSuggestion = ({ item }: { item: SearchSuggestion }) => (
    <TouchableOpacity
      style={styles.suggestionItem}
      onPress={() => handleSuggestionPress(item)}
    >
      <View style={styles.suggestionIcon}>
        <Text style={styles.suggestionEmoji}>{item.icon}</Text>
      </View>
      <View style={styles.suggestionContent}>
        <GlobalText variant="body" style={styles.suggestionText}>
          {item.text}
        </GlobalText>
        {item.subtitle && (
          <GlobalText variant="caption" style={styles.suggestionSubtitle}>
            {item.subtitle}
          </GlobalText>
        )}
      </View>
      <Ionicons name="arrow-forward" size={16} color="#9CA3AF" />
    </TouchableOpacity>
  );

  const renderRecentSearches = () => {
    if (state.searchHistory.length === 0) return null;

    return (
      <View style={styles.recentSection}>
        <View style={styles.recentHeader}>
          <GlobalText variant="body" style={styles.recentTitle}>
            جستجوهای اخیر
          </GlobalText>
          <TouchableOpacity onPress={() => {}}>
            <GlobalText variant="caption" style={styles.clearText}>
              پاک کردن
            </GlobalText>
          </TouchableOpacity>
        </View>
        {state.searchHistory.slice(0, 3).map((item) => (
          <TouchableOpacity
            key={item.id}
            style={styles.recentItem}
            onPress={() => handleSuggestionPress({
              id: item.id,
              type: 'restaurant',
              text: item.query,
              subtitle: `${item.resultCount} نتیجه`
            })}
          >
            <Ionicons name="time" size={16} color="#9CA3AF" />
            <GlobalText variant="body" style={styles.recentText}>
              {item.query}
            </GlobalText>
          </TouchableOpacity>
        ))}
      </View>
    );
  };

  const renderPopularSearches = () => {
    if (state.popularSearches.length === 0) return null;

    return (
      <View style={styles.popularSection}>
        <GlobalText variant="body" style={styles.popularTitle}>
          جستجوهای محبوب
        </GlobalText>
        <View style={styles.popularContainer}>
          {state.popularSearches.slice(0, 6).map((item) => (
            <TouchableOpacity
              key={item.id}
              style={styles.popularChip}
              onPress={() => handleSuggestionPress({
                id: item.id,
                type: 'restaurant',
                text: item.query,
                subtitle: `${item.count} جستجو`
              })}
            >
              <GlobalText variant="caption" style={styles.popularText}>
                {item.query}
              </GlobalText>
            </TouchableOpacity>
          ))}
        </View>
      </View>
    );
  };

  return (
    <View style={[styles.container, style]}>
      <View style={styles.searchContainer}>
        <View style={styles.searchBox}>
          <Ionicons name="search" size={20} color="#9CA3AF" />
          <TextInput
            ref={inputRef}
            style={styles.searchInput}
            placeholder={placeholder}
            value={localQuery}
            onChangeText={handleQueryChange}
            onFocus={showSuggestions}
            onSubmitEditing={handleSearch}
            returnKeyType="search"
            autoFocus={autoFocus}
          />
          
          {localQuery.length > 0 && (
            <TouchableOpacity
              style={styles.clearButton}
              onPress={() => {
                setLocalQuery('');
                setQuery('');
                hideSuggestions();
              }}
            >
              <Ionicons name="close-circle" size={20} color="#9CA3AF" />
            </TouchableOpacity>
          )}
          
          {showVoiceSearch && (
            <TouchableOpacity
              style={[
                styles.voiceButton,
                { backgroundColor: isVoiceRecording ? '#EF4444' : 'transparent' }
              ]}
              onPress={handleVoiceSearch}
            >
              <Ionicons
                name="mic"
                size={20}
                color={isVoiceRecording ? '#FFFFFF' : '#9CA3AF'}
              />
            </TouchableOpacity>
          )}
        </View>

        {showAdvancedButton && (
          <TouchableOpacity
            style={styles.advancedButton}
            onPress={handleAdvancedSearch}
          >
            <Ionicons name="options" size={20} color="#e6034b" />
            <GlobalText variant="caption" style={styles.advancedText}>
              پیشرفته
            </GlobalText>
          </TouchableOpacity>
        )}
      </View>

      {/* Suggestions Modal */}
      <Modal
        visible={state.showSuggestions}
        transparent
        animationType="fade"
        onRequestClose={hideSuggestions}
      >
        <TouchableOpacity
          style={styles.modalOverlay}
          activeOpacity={1}
          onPress={hideSuggestions}
        >
          <View style={styles.suggestionsContainer}>
            <FlatList
              data={state.suggestions}
              renderItem={renderSuggestion}
              keyExtractor={(item) => item.id}
              showsVerticalScrollIndicator={false}
              ListHeaderComponent={
                localQuery.length === 0 ? (
                  <View>
                    {renderRecentSearches()}
                    {renderPopularSearches()}
                  </View>
                ) : null
              }
              ListEmptyComponent={
                localQuery.length === 0 ? null : (
                  <View style={styles.emptyContainer}>
                    <Ionicons name="search" size={32} color="#9CA3AF" />
                    <GlobalText variant="body" style={styles.emptyText}>
                      پیشنهادی یافت نشد
                    </GlobalText>
                  </View>
                )
              }
            />
          </View>
        </TouchableOpacity>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  searchBox: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F9FAFB',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    marginLeft: 12,
    color: '#374151',
  },
  clearButton: {
    padding: 4,
    marginLeft: 8,
  },
  voiceButton: {
    padding: 4,
    marginLeft: 8,
    borderRadius: 12,
  },
  advancedButton: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    backgroundColor: '#FEF2F2',
  },
  advancedText: {
    color: '#e6034b',
    fontSize: 10,
    marginTop: 2,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-start',
    paddingTop: Platform.OS === 'ios' ? 100 : 80,
  },
  suggestionsContainer: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    marginHorizontal: 16,
    maxHeight: 400,
    padding: 8,
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
      },
      android: {
        elevation: 5,
      },
    }),
  },
  suggestionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
  },
  suggestionIcon: {
    width: 32,
    height: 32,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#F3F4F6',
    borderRadius: 16,
    marginRight: 12,
  },
  suggestionEmoji: {
    fontSize: 16,
  },
  suggestionContent: {
    flex: 1,
  },
  suggestionText: {
    fontSize: 16,
    color: '#374151',
  },
  suggestionSubtitle: {
    fontSize: 14,
    color: '#9CA3AF',
    marginTop: 2,
  },
  recentSection: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  recentHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  recentTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#374151',
  },
  clearText: {
    color: '#e6034b',
    fontSize: 12,
  },
  recentItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
  },
  recentText: {
    marginLeft: 8,
    color: '#6B7280',
  },
  popularSection: {
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  popularTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#374151',
    marginBottom: 8,
  },
  popularContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  popularChip: {
    backgroundColor: '#F3F4F6',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  popularText: {
    color: '#6B7280',
    fontSize: 12,
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 32,
  },
  emptyText: {
    color: '#9CA3AF',
    marginTop: 8,
  },
}); 