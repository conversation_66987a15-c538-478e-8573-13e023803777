import React from 'react';
import { View, ActivityIndicator, StyleSheet } from 'react-native';
import { Text } from './GlobalText';
import { colors } from '../theme';
import { useTranslation } from '../i18n';

export const LoadingScreen = () => {
  const { t } = useTranslation();

  return (
    <View style={styles.container}>
      <ActivityIndicator size="large" color={colors.primary[500]} />
      <Text variant="body" style={styles.loadingText}>
        {t('common.loading')}
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.background.default,
  },
  loadingText: {
    marginTop: 16,
    color: colors.text.secondary,
  },
}); 