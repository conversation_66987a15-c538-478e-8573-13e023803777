import React from 'react';
import { Text as RNText, TextProps as RNTextProps, TextStyle } from 'react-native';
import { useTranslation } from '../../i18n';
import { fonts } from '../../theme';

export interface TextProps extends RNTextProps {
  variant?: 'heading1' | 'heading2' | 'heading3' | 'body' | 'caption' | 'button';
  weight?: 'light' | 'regular' | 'medium' | 'semiBold' | 'bold';
}

export const Text: React.FC<TextProps> = ({ 
  style, 
  variant = 'body', 
  weight,
  children, 
  ...props 
}) => {
  const { isRTL } = useTranslation();

  // Determine font family based on language and weight
  const getFontFamily = (): string => {
    // For RTL languages (Dari/Pashto), use YekanBakh
    if (isRTL) {
      if (weight) {
        return fonts.yekanBakh[weight] || fonts.yekanBakh.regular;
      }
      
      // Default font weights based on variant
      switch (variant) {
        case 'heading1':
        case 'heading2':
        case 'heading3':
          return fonts.yekanBakh.bold;
        case 'button':
          return fonts.yekanBakh.semiBold;
        case 'caption':
          return fonts.yekanBakh.regular;
        default:
          return fonts.yekanBakh.regular;
      }
    }
    
    // For English, use system fonts
    return fonts.system.regular;
  };

  const defaultStyle: TextStyle = {
    fontFamily: getFontFamily(),
    textAlign: isRTL ? 'right' : 'left',
    writingDirection: isRTL ? 'rtl' : 'ltr',
  };

  const combinedStyle = [defaultStyle, style];

  return (
    <RNText style={combinedStyle} {...props}>
      {children}
    </RNText>
  );
}; 