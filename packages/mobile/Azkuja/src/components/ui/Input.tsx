import React, { useState } from 'react';
import {
  TextInput,
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
} from 'react-native';
import { colors, typography, spacing, borderRadius, fonts } from '../../theme';
import { useTranslation } from '../../i18n';

export interface InputProps {
  label?: string;
  placeholder?: string;
  value: string;
  onChangeText: (text: string) => void;
  error?: string;
  disabled?: boolean;
  type?: 'text' | 'email' | 'password' | 'phone';
  testID?: string;
}

export const Input: React.FC<InputProps> = ({
  label,
  placeholder,
  value,
  onChangeText,
  error,
  disabled = false,
  type = 'text',
  testID,
}) => {
  const { isRTL } = useTranslation();
  const [isFocused, setIsFocused] = useState(false);
  const [isPasswordVisible, setIsPasswordVisible] = useState(false);

  const getInputConfig = () => {
    switch (type) {
      case 'email':
        return {
          keyboardType: 'email-address' as const,
          autoCapitalize: 'none' as const,
        };
      case 'password':
        return {
          secureTextEntry: !isPasswordVisible,
          autoCapitalize: 'none' as const,
        };
      case 'phone':
        return {
          keyboardType: 'phone-pad' as const,
        };
      default:
        return {};
    }
  };

  const togglePasswordVisibility = () => {
    setIsPasswordVisible(!isPasswordVisible);
  };

  const getBorderColor = () => {
    if (error) return colors.error[500];
    if (isFocused) return colors.primary[500];
    return colors.border.light;
  };

  return (
    <View style={styles.container}>
      {label && (
        <Text style={[styles.label, { textAlign: isRTL ? 'right' : 'left' }]}>
          {label}
        </Text>
      )}
      
      <View style={[
        styles.inputContainer,
        {
          borderColor: getBorderColor(),
          flexDirection: isRTL ? 'row-reverse' : 'row',
        }
      ]}>
        <TextInput
          style={[
            styles.input,
            { textAlign: isRTL ? 'right' : 'left' }
          ]}
          value={value}
          onChangeText={onChangeText}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
          placeholder={placeholder}
          placeholderTextColor={colors.text.hint}
          editable={!disabled}
          testID={testID}
          {...getInputConfig()}
        />

        {type === 'password' && (
          <TouchableOpacity
            onPress={togglePasswordVisibility}
            style={styles.iconContainer}
          >
            <Text style={styles.passwordToggle}>
              {isPasswordVisible ? '🙈' : '👁️'}
            </Text>
          </TouchableOpacity>
        )}
      </View>

      {error && (
        <Text style={[styles.error, { textAlign: isRTL ? 'right' : 'left' }]}>
          {error}
        </Text>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: spacing[4],
  },
  label: {
    fontSize: typography.body.small.fontSize,
    fontFamily: fonts.yekanBakh.medium,
    fontWeight: '500',
    marginBottom: spacing[1],
    color: colors.text.secondary,
  },
  inputContainer: {
    borderWidth: 1,
    borderRadius: borderRadius.md,
    paddingHorizontal: spacing[3],
    backgroundColor: colors.background.paper,
    height: 48,
    alignItems: 'center',
  },
  input: {
    flex: 1,
    fontSize: typography.body.medium.fontSize,
    fontFamily: fonts.yekanBakh.regular,
    color: colors.text.primary,
  },
  iconContainer: {
    padding: spacing[1],
  },
  passwordToggle: {
    fontSize: 16,
  },
  error: {
    fontSize: typography.body.small.fontSize,
    fontFamily: fonts.yekanBakh.regular,
    color: colors.error[500],
    marginTop: spacing[1],
  },
}); 