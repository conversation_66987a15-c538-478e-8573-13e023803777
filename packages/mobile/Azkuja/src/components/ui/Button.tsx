import React from 'react';
import {
  TouchableOpacity,
  Text,
  StyleSheet,
  ViewStyle,
  TextStyle,
  ActivityIndicator,
  View,
} from 'react-native';
import { colors, typography, spacing, borderRadius, shadows, fonts } from '../../theme';
import { useTranslation } from '../../i18n';

export interface ButtonProps {
  title: string;
  onPress: () => void;
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger';
  size?: 'small' | 'medium' | 'large';
  disabled?: boolean;
  loading?: boolean;
  fullWidth?: boolean;
  icon?: React.ReactNode;
  iconPosition?: 'left' | 'right';
  style?: ViewStyle;
  textStyle?: TextStyle;
  testID?: string;
}

export const Button: React.FC<ButtonProps> = ({
  title,
  onPress,
  variant = 'primary',
  size = 'medium',
  disabled = false,
  loading = false,
  fullWidth = false,
  icon,
  iconPosition = 'left',
  style,
  textStyle,
  testID,
}) => {
  const { isRTL } = useTranslation();

  // Get variant styles
  const getVariantStyles = () => {
    switch (variant) {
      case 'primary':
        return {
          backgroundColor: disabled ? colors.neutral[300] : colors.primary[500],
          borderWidth: 0,
        };
      case 'secondary':
        return {
          backgroundColor: disabled ? colors.neutral[200] : colors.secondary[500],
          borderWidth: 0,
        };
      case 'outline':
        return {
          backgroundColor: 'transparent',
          borderWidth: 1,
          borderColor: disabled ? colors.neutral[300] : colors.primary[500],
        };
      case 'ghost':
        return {
          backgroundColor: 'transparent',
          borderWidth: 0,
        };
      case 'danger':
        return {
          backgroundColor: disabled ? colors.neutral[300] : colors.error[500],
          borderWidth: 0,
        };
      default:
        return {
          backgroundColor: colors.primary[500],
          borderWidth: 0,
        };
    }
  };

  // Get text color based on variant
  const getTextColor = () => {
    if (disabled) {
      return colors.text.disabled;
    }

    switch (variant) {
      case 'primary':
      case 'secondary':
      case 'danger':
        return colors.text.onPrimary;
      case 'outline':
        return colors.primary[500];
      case 'ghost':
        return colors.primary[500];
      default:
        return colors.text.onPrimary;
    }
  };

  // Get size styles
  const getSizeStyles = () => {
    switch (size) {
      case 'small':
        return {
          height: 32,
          paddingHorizontal: spacing[3],
          fontSize: typography.button.small.fontSize,
        };
      case 'medium':
        return {
          height: 40,
          paddingHorizontal: spacing[4],
          fontSize: typography.button.medium.fontSize,
        };
      case 'large':
        return {
          height: 48,
          paddingHorizontal: spacing[6],
          fontSize: typography.button.large.fontSize,
        };
      default:
        return {
          height: 40,
          paddingHorizontal: spacing[4],
          fontSize: typography.button.medium.fontSize,
        };
    }
  };

  const variantStyles = getVariantStyles();
  const sizeStyles = getSizeStyles();
  const textColor = getTextColor();

  const containerStyle = [
    styles.container,
    {
      ...variantStyles,
      height: sizeStyles.height,
      paddingHorizontal: sizeStyles.paddingHorizontal,
      ...(fullWidth ? { width: '100%' as const } : {}),
      opacity: disabled ? 0.6 : 1,
      flexDirection: (isRTL ? 'row-reverse' : 'row') as 'row' | 'row-reverse',
    },
    style,
  ];

  const textStyles = [
    styles.text,
    {
      color: textColor,
      fontSize: sizeStyles.fontSize,
      fontFamily: fonts.yekanBakh.semiBold, // Use YekanBakh font for buttons
      textAlign: (isRTL ? 'right' : 'left') as 'left' | 'right',
    },
    textStyle,
  ];

  const renderContent = () => {
    if (loading) {
      return (
        <View style={styles.loadingContainer}>
          <ActivityIndicator
            size="small"
            color={textColor}
            style={styles.loadingIndicator}
          />
          <Text style={textStyles}>{title}</Text>
        </View>
      );
    }

    if (icon) {
      const iconContent = (
        <View style={styles.iconContainer}>
          {icon}
        </View>
      );

      const textContent = <Text style={textStyles}>{title}</Text>;

      return (
        <View style={styles.contentContainer}>
          {iconPosition === 'left' && !isRTL ? iconContent : null}
          {iconPosition === 'right' && isRTL ? iconContent : null}
          {textContent}
          {iconPosition === 'right' && !isRTL ? iconContent : null}
          {iconPosition === 'left' && isRTL ? iconContent : null}
        </View>
      );
    }

    return <Text style={textStyles}>{title}</Text>;
  };

  return (
    <TouchableOpacity
      style={containerStyle}
      onPress={onPress}
      disabled={disabled || loading}
      activeOpacity={0.7}
      testID={testID}
      accessibilityRole="button"
      accessibilityState={{ disabled: disabled || loading }}
      accessibilityLabel={title}
    >
      {renderContent()}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    borderRadius: borderRadius.md,
    alignItems: 'center',
    justifyContent: 'center',
    ...shadows.sm,
  },
  text: {
    fontWeight: '600',
    textAlign: 'center',
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingIndicator: {
    marginRight: spacing[2],
  },
  contentContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  iconContainer: {
    marginHorizontal: spacing[1],
  },
}); 