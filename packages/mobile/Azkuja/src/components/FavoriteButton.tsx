import React, { useState, useEffect } from 'react';
import { TouchableOpacity, StyleSheet, Alert } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useFavorites } from '../contexts/FavoritesContext';

interface FavoriteButtonProps {
  restaurantId: string;
  restaurantName?: string;
  size?: number;
  style?: any;
  showFeedback?: boolean;
}

export const FavoriteButton: React.FC<FavoriteButtonProps> = ({
  restaurantId,
  restaurantName = 'رستوران',
  size = 24,
  style,
  showFeedback = true,
}) => {
  const { isFavorite, addToFavorites, removeFromFavorites } = useFavorites();
  const [isToggling, setIsToggling] = useState(false);
  const [favoriteStatus, setFavoriteStatus] = useState(false);

  // Update local state when favorites change
  useEffect(() => {
    setFavoriteStatus(isFavorite(restaurantId));
  }, [isFavorite, restaurantId]);

  const handleToggleFavorite = async () => {
    if (isToggling) return; // Prevent double-taps

    setIsToggling(true);
    
    try {
      if (favoriteStatus) {
        // Remove from favorites
        await removeFromFavorites(restaurantId);
        
        if (showFeedback) {
          Alert.alert(
            'حذف شد',
            `${restaurantName} از علاقه‌مندی‌هایتان حذف شد`,
            [{ text: 'باشه', style: 'default' }]
          );
        }
      } else {
        // Add to favorites
        await addToFavorites(restaurantId);
        
        if (showFeedback) {
          Alert.alert(
            'اضافه شد',
            `${restaurantName} به علاقه‌مندی‌هایتان اضافه شد`,
            [{ text: 'باشه', style: 'default' }]
          );
        }
      }
    } catch (error) {
      console.error('Error toggling favorite:', error);
      
      // Show error message
      Alert.alert(
        'خطا',
        favoriteStatus 
          ? 'خطا در حذف از علاقه‌مندی‌ها. لطفاً دوباره تلاش کنید.'
          : 'خطا در اضافه کردن به علاقه‌مندی‌ها. لطفاً دوباره تلاش کنید.',
        [{ text: 'باشه', style: 'default' }]
      );
    } finally {
      setIsToggling(false);
    }
  };

  return (
    <TouchableOpacity
      style={[styles.button, style]}
      onPress={handleToggleFavorite}
      disabled={isToggling}
      activeOpacity={0.7}
    >
      <Ionicons
        name={favoriteStatus ? 'heart' : 'heart-outline'}
        size={size}
        color={favoriteStatus ? '#e6034b' : '#757575'}
      />
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  button: {
    padding: 8,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
    elevation: 2,
  },
}); 