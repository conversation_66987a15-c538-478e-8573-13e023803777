import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import apiService, { Favorite } from '../lib/api';
import { useAuth } from './AuthContext';

interface FavoritesContextType {
  favorites: Favorite[];
  isLoading: boolean;
  isFavorite: (restaurantId: string) => boolean;
  addToFavorites: (restaurantId: string) => Promise<void>;
  removeFromFavorites: (restaurantId: string) => Promise<void>;
  refreshFavorites: () => Promise<void>;
}

const FavoritesContext = createContext<FavoritesContextType | undefined>(undefined);

interface FavoritesProviderProps {
  children: ReactNode;
}

export const FavoritesProvider: React.FC<FavoritesProviderProps> = ({ children }) => {
  const [favorites, setFavorites] = useState<Favorite[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { user, isAuthenticated } = useAuth();

  // Load favorites when user is authenticated
  useEffect(() => {
    if (isAuthenticated && user) {
      loadFavorites();
    } else {
      setFavorites([]);
      setIsLoading(false);
    }
  }, [isAuthenticated, user]);

  const loadFavorites = async () => {
    try {
      console.log('🌟 FavoritesContext: Loading favorites...');
      setIsLoading(true);
      const userFavorites = await apiService.getFavorites();
      console.log('🌟 FavoritesContext: Loaded', userFavorites.length, 'favorites');
      setFavorites(userFavorites);
    } catch (error) {
      console.error('💥 FavoritesContext: Error loading favorites:', error);
      setFavorites([]);
    } finally {
      setIsLoading(false);
    }
  };

  const isFavorite = (restaurantId: string): boolean => {
    return favorites.some(fav => fav.restaurantId === restaurantId);
  };

  const addToFavorites = async (restaurantId: string): Promise<void> => {
    try {
      console.log('❤️ FavoritesContext: Adding restaurant to favorites:', restaurantId);
      
      // Optimistic update
      const tempFavorite: Favorite = {
        id: `temp_${Date.now()}`,
        userId: user?.id || '',
        restaurantId,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };
      setFavorites(prev => [...prev, tempFavorite]);

      // Call API
      const newFavorite = await apiService.addToFavorites(restaurantId);
      
      // Replace temp favorite with real one
      setFavorites(prev => 
        prev.map(fav => 
          fav.id === tempFavorite.id ? newFavorite : fav
        )
      );
      
      console.log('✅ FavoritesContext: Successfully added to favorites');
    } catch (error) {
      console.error('💥 FavoritesContext: Error adding to favorites:', error);
      
      // Revert optimistic update
      setFavorites(prev => 
        prev.filter(fav => fav.restaurantId !== restaurantId)
      );
      
      throw error;
    }
  };

  const removeFromFavorites = async (restaurantId: string): Promise<void> => {
    // Store current favorites before making changes
    const previousFavorites = favorites;
    
    try {
      console.log('💔 FavoritesContext: Removing restaurant from favorites:', restaurantId);
      
      // Optimistic update
      setFavorites(prev => 
        prev.filter(fav => fav.restaurantId !== restaurantId)
      );

      // Call API
      await apiService.removeFromFavorites(restaurantId);
      
      console.log('✅ FavoritesContext: Successfully removed from favorites');
    } catch (error) {
      console.error('💥 FavoritesContext: Error removing from favorites:', error);
      
      // Revert optimistic update
      setFavorites(previousFavorites);
      
      throw error;
    }
  };

  const refreshFavorites = async (): Promise<void> => {
    await loadFavorites();
  };

  // Log state changes
  useEffect(() => {
    console.log('🔄 FavoritesContext: State changed - Favorites count:', favorites.length, 'Loading:', isLoading);
  }, [favorites, isLoading]);

  const value: FavoritesContextType = {
    favorites,
    isLoading,
    isFavorite,
    addToFavorites,
    removeFromFavorites,
    refreshFavorites,
  };

  return (
    <FavoritesContext.Provider value={value}>
      {children}
    </FavoritesContext.Provider>
  );
};

export const useFavorites = (): FavoritesContextType => {
  const context = useContext(FavoritesContext);
  if (context === undefined) {
    throw new Error('useFavorites must be used within a FavoritesProvider');
  }
  return context;
}; 