import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import apiService, { User, RegisterDto, LoginDto, VerifyOtpDto, AuthResponse } from '../lib/api';
import webSocketService from '../services/WebSocketService';

// Mock mode - set to false to use real backend API
const MOCK_MODE = true;

// Custom error types
export class UserNotFoundError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'UserNotFoundError';
  }
}

interface AuthContextType {
  user: User | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  hasCompletedOnboarding: boolean;
  login: (data: LoginDto) => Promise<{ message: string }>;
  register: (data: RegisterDto) => Promise<{ message: string }>;
  verifyOtp: (data: VerifyOtpDto) => Promise<AuthResponse>;
  logout: () => Promise<void>;
  refreshUser: () => Promise<void>;
  completeOnboarding: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

// Mock user data for testing
const mockUser: User = {
  id: '1',
  phone_number: '**********',
  name: 'احمد احمدی',
  email: '<EMAIL>',
  role: 'customer',
  created_at: new Date().toISOString(),
  updated_at: new Date().toISOString(),
};

// Mock registered users database for testing
const mockRegisteredUsers = [
  '**********',
  '**********',
  '**********'
];

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [hasCompletedOnboarding, setHasCompletedOnboarding] = useState(false);

  // Initialize auth state on app start
  useEffect(() => {
    const initializeAuth = async () => {
      console.log('🚀 AuthProvider: Starting initialization...');
      
      try {
        if (MOCK_MODE) {
          // In mock mode, check if user data exists in storage
          console.log('📱 AuthProvider: Mock mode - checking for existing auth state');
          try {
            const savedUser = await AsyncStorage.getItem('user');
            const savedToken = await AsyncStorage.getItem('accessToken');
            const onboardingCompleted = await AsyncStorage.getItem('onboardingCompleted');
            
            if (savedUser && savedToken) {
              console.log('✅ AuthProvider: Found saved user data, restoring session');
              const parsedUser = JSON.parse(savedUser);
              setUser(parsedUser);
              console.log('👤 AuthProvider: User restored:', parsedUser.name);
              
              // Connect to WebSocket when user is restored
              webSocketService.connect(parsedUser.id, {
                onConnect: () => console.log('🔌 WebSocket connected for restored user'),
                onDisconnect: (reason) => console.log('❌ WebSocket disconnected:', reason),
                onError: (error) => console.error('❌ WebSocket error:', error)
              });
            } else {
              console.log('❌ AuthProvider: No saved user data found');
            }
            
            // Set onboarding completion state
            setHasCompletedOnboarding(onboardingCompleted === 'true');
            console.log('📋 AuthProvider: Onboarding completed:', onboardingCompleted === 'true');
          } catch (error) {
            console.warn('⚠️ AuthProvider: Failed to load saved auth data:', error);
          }
        } else {
          // Real API mode - check for authentication and onboarding state
          console.log('🌐 AuthProvider: Real API mode - checking auth state');
          
          try {
            const onboardingCompleted = await AsyncStorage.getItem('onboardingCompleted');
            setHasCompletedOnboarding(onboardingCompleted === 'true');
            console.log('📋 AuthProvider: Onboarding completed:', onboardingCompleted === 'true');
          } catch (error) {
            console.warn('⚠️ AuthProvider: Failed to load onboarding state:', error);
          }
          
          const isAuth = await apiService.isAuthenticated();
          console.log('🔐 AuthProvider: Is authenticated:', isAuth);
          
          if (isAuth) {
            const currentUser = await apiService.getCurrentUser();
            console.log('👤 AuthProvider: Current user:', currentUser ? currentUser.name : 'null');
            setUser(currentUser);
            
            // Connect to WebSocket when user is authenticated
            if (currentUser) {
              webSocketService.connect(currentUser.id, {
                onConnect: () => console.log('🔌 WebSocket connected for authenticated user'),
                onDisconnect: (reason) => console.log('❌ WebSocket disconnected:', reason),
                onError: (error) => console.error('❌ WebSocket error:', error)
              });
            }
          } else {
            console.log('❌ AuthProvider: No authenticated user found');
          }
        }
      } catch (error) {
        console.error('💥 AuthProvider: Initialization error:', error);
        setUser(null);
      } finally {
        setIsLoading(false);
        console.log('🏁 AuthProvider: Initialization complete');
      }
    };

    initializeAuth();
  }, []);

  const login = async (data: LoginDto): Promise<{ message: string }> => {
    console.log('📞 AuthProvider.login: Starting login process...');
    console.log('📞 AuthProvider.login: Phone number:', data.phone_number);
    console.log('📞 AuthProvider.login: Phone number length:', data.phone_number?.length);
    console.log('📞 AuthProvider.login: Phone number type:', typeof data.phone_number);
    
    // DON'T set isLoading here - it causes AppNavigator to re-render and reset state
    // setIsLoading(true);
    
    try {
      if (MOCK_MODE) {
        console.log('🎭 AuthProvider.login: Mock mode active');
        
        // Simulate API delay
        console.log('⏳ AuthProvider.login: Simulating API delay...');
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        console.log('🔍 AuthProvider.login: Validating phone number...');
        console.log('🔍 AuthProvider.login: Phone exists?', !!data.phone_number);
        console.log('🔍 AuthProvider.login: Regex test result:', data.phone_number?.match(/^07\d{8}$/));
        
        if (!data.phone_number || !data.phone_number.match(/^07\d{8}$/)) {
          const error = 'شماره تلفن باید ۱۰ رقم باشد و با ۰۷ شروع شود (مثال: **********)';
          console.error('❌ AuthProvider.login: Validation failed:', error);
          throw new Error(error);
        }

        console.log('✅ AuthProvider.login: Phone validation passed');
        console.log('🔍 AuthProvider.login: Checking if user is registered...');
        console.log('🔍 AuthProvider.login: Mock registered users:', mockRegisteredUsers);
        console.log('🔍 AuthProvider.login: Is user in database?', mockRegisteredUsers.includes(data.phone_number));

        // Check if user is registered in mock database
        if (!mockRegisteredUsers.includes(data.phone_number)) {
          const error = `شماره ${data.phone_number} ثبت نام نشده است. لطفاً ابتدا ثبت نام کنید.`;
          console.error('❌ AuthProvider.login: User not found:', error);
          throw new UserNotFoundError(error);
        }
        
        console.log('✅ AuthProvider.login: User found in database, creating response...');
        
        const response = { 
          message: `کد تأیید به ${data.phone_number} ارسال شد. کد تستی: 123456` 
        };
        
        console.log('✅ AuthProvider.login: Mock login successful');
        console.log('📨 AuthProvider.login: Response:', response);
        console.log('🚀 AuthProvider.login: About to return response');
        
        return response;
      } else {
        console.log('🌐 AuthProvider.login: Real API mode - connecting to backend');
        
        // Add basic phone validation for real API mode too
        if (!data.phone_number || !data.phone_number.match(/^07\d{8}$/)) {
          const error = 'شماره تلفن باید ۱۰ رقم باشد و با ۰۷ شروع شود (مثال: **********)';
          console.error('❌ AuthProvider.login: Validation failed:', error);
          throw new Error(error);
        }

        const response = await apiService.login(data);
        console.log('✅ AuthProvider.login: API login successful');
        return response;
      }
    } catch (error) {
      console.error('💥 AuthProvider.login: Login error:', error);
      
      // Handle network/connectivity errors
      if (error instanceof TypeError && error.message.includes('fetch')) {
        const networkError = 'خطا در اتصال به سرور. لطفاً اتصال اینترنت خود را بررسی کنید.';
        console.error('🌐 AuthProvider.login: Network error:', networkError);
        throw new Error(networkError);
      }

      // Handle API errors that might indicate user not found
      if (error instanceof Error && error.message.includes('404')) {
        const userNotFoundError = `شماره ${data.phone_number} ثبت نام نشده است. لطفاً ابتدا ثبت نام کنید.`;
        console.error('👤 AuthProvider.login: User not found (API):', userNotFoundError);
        throw new UserNotFoundError(userNotFoundError);
      }
      
      // Re-throw UserNotFoundError as-is to preserve error type
      if (error instanceof UserNotFoundError) {
        throw error;
      }
      throw error;
    } finally {
      // DON'T set isLoading false here - we never set it to true
      // setIsLoading(false);
      console.log('🏁 AuthProvider.login: Login process complete');
    }
  };

  const register = async (data: RegisterDto): Promise<{ message: string }> => {
    console.log('📝 AuthProvider.register: Starting registration...');
    console.log('📝 AuthProvider.register: Data:', { ...data, email: data.email || 'not provided' });
    // DON'T set isLoading here - it causes AppNavigator to re-render and reset state
    // setIsLoading(true);
    try {
      if (MOCK_MODE) {
        console.log('🎭 AuthProvider.register: Mock mode');
        
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 1500));

        // Validation
        if (!data.phone_number || !data.phone_number.match(/^07\d{8}$/)) {
          const error = 'شماره تلفن باید ۱۰ رقم باشد و با ۰۷ شروع شود (مثال: **********)';
          console.error('❌ AuthProvider.register: Phone validation failed:', error);
          throw new Error(error);
        }

        if (!data.name || !data.name.trim()) {
          const error = 'نام و نام خانوادگی الزامی است';
          console.error('❌ AuthProvider.register: Name validation failed:', error);
          throw new Error(error);
        }

        // Check if user is already registered
        if (mockRegisteredUsers.includes(data.phone_number)) {
          const error = `شماره ${data.phone_number} قبلاً ثبت نام شده است. لطفاً وارد شوید.`;
          console.error('❌ AuthProvider.register: User already exists:', error);
          throw new Error(error);
        }

        // Add user to mock database
        mockRegisteredUsers.push(data.phone_number);
        console.log('📝 AuthProvider.register: Added user to mock database');
        
        const response = { 
          message: `کد تأیید به ${data.phone_number} ارسال شد. کد تستی: 123456` 
        };
        
        console.log('✅ AuthProvider.register: Mock registration successful');
        return response;
      } else {
        console.log('🌐 AuthProvider.register: Real API mode - connecting to backend');
        
        // Add basic validation for real API mode
        if (!data.phone_number || !data.phone_number.match(/^07\d{8}$/)) {
          const error = 'شماره تلفن باید ۱۰ رقم باشد و با ۰۷ شروع شود (مثال: **********)';
          console.error('❌ AuthProvider.register: Phone validation failed:', error);
          throw new Error(error);
        }

        if (!data.name || !data.name.trim()) {
          const error = 'نام و نام خانوادگی الزامی است';
          console.error('❌ AuthProvider.register: Name validation failed:', error);
          throw new Error(error);
        }

        const response = await apiService.register(data);
        console.log('✅ AuthProvider.register: API registration successful');
        return response;
      }
    } catch (error) {
      console.error('💥 AuthProvider.register: Registration error:', error);
      
      // Handle network/connectivity errors
      if (error instanceof TypeError && error.message.includes('fetch')) {
        const networkError = 'خطا در اتصال به سرور. لطفاً اتصال اینترنت خود را بررسی کنید.';
        console.error('🌐 AuthProvider.register: Network error:', networkError);
        throw new Error(networkError);
      }

      // Handle API errors for existing users
      if (error instanceof Error && (error.message.includes('409') || error.message.includes('already exists'))) {
        const existingUserError = `شماره ${data.phone_number} قبلاً ثبت نام شده است. لطفاً وارد شوید.`;
        console.error('👤 AuthProvider.register: User already exists (API):', existingUserError);
        throw new Error(existingUserError);
      }

      throw error;
    } finally {
      // DON'T set isLoading false here - we never set it to true
      // setIsLoading(false);
    }
  };

  const verifyOtp = async (data: VerifyOtpDto): Promise<AuthResponse> => {
    console.log('🔐 AuthProvider.verifyOtp: Starting OTP verification...');
    console.log('🔐 AuthProvider.verifyOtp: Phone:', data.phone_number, 'OTP:', data.otp_code);
    console.log('🔐 AuthProvider.verifyOtp: OTP length:', data.otp_code?.length);
    console.log('🔐 AuthProvider.verifyOtp: OTP type:', typeof data.otp_code);
    
    setIsLoading(true);
    try {
      if (MOCK_MODE) {
        console.log('🎭 AuthProvider.verifyOtp: Mock mode');
        
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 800));
        
        // Only accept OTP code 123456
        console.log('🔍 AuthProvider.verifyOtp: Checking OTP - received:', data.otp_code, 'expected: 123456');
        if (data.otp_code !== '123456') {
          const error = 'کد تأیید نامعتبر است. کد صحیح: 123456';
          console.error('❌ AuthProvider.verifyOtp: Invalid OTP:', error);
          throw new Error(error);
        }
        
        // Create mock user with the phone number used
        const mockUserWithPhone = {
          ...mockUser,
          phone_number: data.phone_number,
        };
        
        // In mock mode, persist user data to AsyncStorage
        const token = 'mock_token_' + Date.now();
        try {
          await AsyncStorage.setItem('accessToken', token);
          await AsyncStorage.setItem('user', JSON.stringify(mockUserWithPhone));
          console.log('💾 AuthProvider.verifyOtp: Saved user data to storage');
        } catch (error) {
          console.warn('⚠️ AuthProvider.verifyOtp: Failed to save auth data:', error);
        }
        
        console.log('👤 AuthProvider.verifyOtp: Setting user state:', mockUserWithPhone.name);
        setUser(mockUserWithPhone);
        console.log('👤 AuthProvider.verifyOtp: User state set, current user:', mockUserWithPhone);
        
        // Connect to WebSocket after successful authentication
        webSocketService.connect(mockUserWithPhone.id, {
          onConnect: () => console.log('🔌 WebSocket connected after login'),
          onDisconnect: (reason) => console.log('❌ WebSocket disconnected:', reason),
          onError: (error) => console.error('❌ WebSocket error:', error)
        });
        
        const response = {
          user: mockUserWithPhone,
          accessToken: token,
        };
        
        console.log('✅ AuthProvider.verifyOtp: Mock OTP verification successful');
        console.log('📤 AuthProvider.verifyOtp: Returning response:', response);
        return response;
      } else {
        console.log('🌐 AuthProvider.verifyOtp: Real API mode - connecting to backend');
        
        // Add basic validation for real API mode
        if (!data.otp_code || data.otp_code.length !== 6) {
          const error = 'کد تأیید باید ۶ رقم باشد';
          console.error('❌ AuthProvider.verifyOtp: OTP validation failed:', error);
          throw new Error(error);
        }

        const response = await apiService.verifyOtp(data);
        setUser(response.user);
        console.log('✅ AuthProvider.verifyOtp: API OTP verification successful');
        
        // Connect to WebSocket after successful authentication
        webSocketService.connect(response.user.id, {
          onConnect: () => console.log('🔌 WebSocket connected after login'),
          onDisconnect: (reason) => console.log('❌ WebSocket disconnected:', reason),
          onError: (error) => console.error('❌ WebSocket error:', error)
        });
        return response;
      }
    } catch (error) {
      console.error('💥 AuthProvider.verifyOtp: OTP verification error:', error);
      
      // Handle network/connectivity errors
      if (error instanceof TypeError && error.message.includes('fetch')) {
        const networkError = 'خطا در اتصال به سرور. لطفاً اتصال اینترنت خود را بررسی کنید.';
        console.error('🌐 AuthProvider.verifyOtp: Network error:', networkError);
        throw new Error(networkError);
      }

      // Handle API errors for invalid OTP
      if (error instanceof Error && (error.message.includes('401') || error.message.includes('400') || error.message.includes('invalid'))) {
        const invalidOtpError = 'کد تأیید نامعتبر است. لطفاً دوباره امتحان کنید.';
        console.error('🔐 AuthProvider.verifyOtp: Invalid OTP (API):', invalidOtpError);
        throw new Error(invalidOtpError);
      }

      throw error;
    } finally {
      setIsLoading(false);
      console.log('🏁 AuthProvider.verifyOtp: OTP verification complete');
    }
  };

  const logout = async (): Promise<void> => {
    console.log('🚪 AuthProvider.logout: Starting logout...');
    setIsLoading(true);
    try {
      if (MOCK_MODE) {
        console.log('🎭 AuthProvider.logout: Mock mode');
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 500));
        
        // Clear stored data in mock mode
        try {
          await AsyncStorage.removeItem('accessToken');
          await AsyncStorage.removeItem('user');
          console.log('🗑️ AuthProvider.logout: Cleared stored auth data');
        } catch (error) {
          console.warn('⚠️ AuthProvider.logout: Failed to clear auth data:', error);
        }
        
        setUser(null);
        
        // Disconnect WebSocket on logout
        webSocketService.disconnect();
        console.log('🔌 WebSocket disconnected on logout');
        
        console.log('✅ AuthProvider.logout: Mock logout successful');
      } else {
        await apiService.logout();
        setUser(null);
        
        // Disconnect WebSocket on logout
        webSocketService.disconnect();
        console.log('🔌 WebSocket disconnected on logout');
        
        console.log('✅ AuthProvider.logout: API logout successful');
      }
    } catch (error) {
      console.error('💥 AuthProvider.logout: Logout error:', error);
      // Even if logout fails on backend, clear local state
      setUser(null);
      
      // Always disconnect WebSocket on logout, even if logout fails
      webSocketService.disconnect();
      console.log('🔌 WebSocket disconnected on logout (error fallback)');
    } finally {
      setIsLoading(false);
    }
  };

  const refreshUser = async (): Promise<void> => {
    console.log('🔄 AuthProvider.refreshUser: Refreshing user...');
    try {
      if (MOCK_MODE) {
        // In mock mode, just keep the current user
        console.log('🎭 AuthProvider.refreshUser: Mock mode - keeping current user');
        return;
      } else {
        const currentUser = await apiService.getCurrentUser();
        setUser(currentUser);
        console.log('✅ AuthProvider.refreshUser: User refreshed');
      }
    } catch (error) {
      console.error('💥 AuthProvider.refreshUser: Refresh error:', error);
      setUser(null);
    }
  };

  const completeOnboarding = async (): Promise<void> => {
    console.log('📋 AuthProvider.completeOnboarding: Marking onboarding as completed');
    try {
      await AsyncStorage.setItem('onboardingCompleted', 'true');
      setHasCompletedOnboarding(true);
      console.log('✅ AuthProvider.completeOnboarding: Onboarding marked as completed');
    } catch (error) {
      console.error('💥 AuthProvider.completeOnboarding: Error saving onboarding state:', error);
    }
  };

  // Log state changes
  useEffect(() => {
    console.log('🔄 AuthProvider: State changed - User:', user ? `${user.name} (${user.phone_number})` : 'null', 'Loading:', isLoading);
  }, [user, isLoading]);

  const value: AuthContextType = {
    user,
    isLoading,
    isAuthenticated: !!user,
    hasCompletedOnboarding,
    login,
    register,
    verifyOtp,
    logout,
    refreshUser,
    completeOnboarding,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}; 