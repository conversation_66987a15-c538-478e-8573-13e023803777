import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import apiService, { MenuItem, Restaurant, Order, PaymentMethod } from '../lib/api';

export interface CartItem {
  id: string;
  menuItem: MenuItem;
  quantity: number;
  special_instructions?: string;
  restaurant: Restaurant;
}

interface CartContextType {
  cartItems: CartItem[];
  restaurant: Restaurant | null;
  isLoading: boolean;
  addToCart: (menuItem: MenuItem, restaurant: Restaurant, quantity?: number, specialInstructions?: string) => Promise<void>;
  removeFromCart: (itemId: string) => Promise<void>;
  updateCartItem: (itemId: string, quantity: number, specialInstructions?: string) => Promise<void>;
  clearCart: () => Promise<void>;
  getCartTotal: () => number;
  getCartItemCount: () => number;
  createOrder: (deliveryAddress?: string, specialInstructions?: string, paymentMethod?: PaymentMethod) => Promise<Order>;
}

const CartContext = createContext<CartContextType | undefined>(undefined);

interface CartProviderProps {
  children: ReactNode;
}

export const CartProvider: React.FC<CartProviderProps> = ({ children }) => {
  const [cartItems, setCartItems] = useState<CartItem[]>([]);
  const [restaurant, setRestaurant] = useState<Restaurant | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  // Load cart from storage on app start
  useEffect(() => {
    loadCartFromStorage();
  }, []);

  // Save cart to storage whenever it changes
  useEffect(() => {
    saveCartToStorage();
  }, [cartItems, restaurant]);

  const loadCartFromStorage = async () => {
    try {
      const [savedCartItems, savedRestaurant] = await Promise.all([
        AsyncStorage.getItem('cartItems'),
        AsyncStorage.getItem('cartRestaurant')
      ]);

      if (savedCartItems) {
        setCartItems(JSON.parse(savedCartItems));
      }
      if (savedRestaurant) {
        setRestaurant(JSON.parse(savedRestaurant));
      }

      console.log('🛒 CartProvider: Loaded cart from storage');
    } catch (error) {
      console.error('💥 CartProvider: Error loading cart from storage:', error);
    }
  };

  const saveCartToStorage = async () => {
    try {
      await Promise.all([
        AsyncStorage.setItem('cartItems', JSON.stringify(cartItems)),
        AsyncStorage.setItem('cartRestaurant', JSON.stringify(restaurant))
      ]);
    } catch (error) {
      console.error('💥 CartProvider: Error saving cart to storage:', error);
    }
  };

  const addToCart = async (
    menuItem: MenuItem, 
    restaurantData: Restaurant, 
    quantity = 1, 
    specialInstructions?: string
  ): Promise<void> => {
    console.log('🛒 CartProvider: Adding item to cart:', menuItem.name);

    try {
      setIsLoading(true);

      // Check if this is from a different restaurant
      if (restaurant && restaurant.id !== restaurantData.id) {
        // Clear cart if switching restaurants
        console.log('🔄 CartProvider: Switching restaurants, clearing cart');
        setCartItems([]);
      }

      // Set the restaurant
      setRestaurant(restaurantData);

      // Check if item already exists in cart
      const existingItemIndex = cartItems.findIndex(item => item.menuItem.id === menuItem.id);

      if (existingItemIndex >= 0) {
        // Update existing item
        const updatedItems = [...cartItems];
        updatedItems[existingItemIndex] = {
          ...updatedItems[existingItemIndex],
          quantity: updatedItems[existingItemIndex].quantity + quantity,
          special_instructions: specialInstructions || updatedItems[existingItemIndex].special_instructions
        };
        setCartItems(updatedItems);
        console.log('✅ CartProvider: Updated existing item quantity');
      } else {
        // Add new item
        const newCartItem: CartItem = {
          id: `${menuItem.id}_${Date.now()}`,
          menuItem,
          quantity,
          special_instructions: specialInstructions,
          restaurant: restaurantData
        };
        setCartItems(prev => [...prev, newCartItem]);
        console.log('✅ CartProvider: Added new item to cart');
      }
    } catch (error) {
      console.error('💥 CartProvider: Error adding item to cart:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const removeFromCart = async (itemId: string): Promise<void> => {
    console.log('🗑️ CartProvider: Removing item from cart:', itemId);

    try {
      setIsLoading(true);
      const updatedItems = cartItems.filter(item => item.id !== itemId);
      setCartItems(updatedItems);

      // Clear restaurant if no items left
      if (updatedItems.length === 0) {
        setRestaurant(null);
      }

      console.log('✅ CartProvider: Item removed from cart');
    } catch (error) {
      console.error('💥 CartProvider: Error removing item from cart:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const updateCartItem = async (
    itemId: string, 
    quantity: number, 
    specialInstructions?: string
  ): Promise<void> => {
    console.log('📝 CartProvider: Updating cart item:', itemId);

    try {
      setIsLoading(true);

      if (quantity <= 0) {
        await removeFromCart(itemId);
        return;
      }

      const updatedItems = cartItems.map(item =>
        item.id === itemId
          ? { ...item, quantity, special_instructions: specialInstructions }
          : item
      );

      setCartItems(updatedItems);
      console.log('✅ CartProvider: Cart item updated');
    } catch (error) {
      console.error('💥 CartProvider: Error updating cart item:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const clearCart = async (): Promise<void> => {
    console.log('🧹 CartProvider: Clearing cart');

    try {
      setIsLoading(true);
      setCartItems([]);
      setRestaurant(null);
      await AsyncStorage.multiRemove(['cartItems', 'cartRestaurant']);
      console.log('✅ CartProvider: Cart cleared');
    } catch (error) {
      console.error('💥 CartProvider: Error clearing cart:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const getCartTotal = (): number => {
    return cartItems.reduce((total, item) => {
      return total + (item.menuItem.price * item.quantity);
    }, 0);
  };

  const getCartItemCount = (): number => {
    return cartItems.reduce((count, item) => count + item.quantity, 0);
  };

  const createOrder = async (
    deliveryAddress?: string, 
    specialInstructions?: string,
    paymentMethod?: PaymentMethod
  ): Promise<Order> => {
    console.log('📦 CartProvider: Creating order');

    if (!restaurant) {
      throw new Error('No restaurant selected');
    }

    if (cartItems.length === 0) {
      throw new Error('Cart is empty');
    }

    try {
      setIsLoading(true);

      const orderItems = cartItems.map(item => ({
        menuItemId: item.menuItem.id,
        quantity: item.quantity,
        special_instructions: item.special_instructions
      }));

      const orderData = {
        restaurantId: restaurant.id,
        items: orderItems,
        delivery_address: deliveryAddress,
        special_instructions: specialInstructions
      };

      const order = await apiService.createOrder(orderData);
      
      // Clear cart after successful order
      await clearCart();
      
      console.log('✅ CartProvider: Order created successfully:', order.id);
      return order;
    } catch (error) {
      console.error('💥 CartProvider: Error creating order:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const value: CartContextType = {
    cartItems,
    restaurant,
    isLoading,
    addToCart,
    removeFromCart,
    updateCartItem,
    clearCart,
    getCartTotal,
    getCartItemCount,
    createOrder,
  };

  return <CartContext.Provider value={value}>{children}</CartContext.Provider>;
};

export const useCart = (): CartContextType => {
  const context = useContext(CartContext);
  if (context === undefined) {
    throw new Error('useCart must be used within a CartProvider');
  }
  return context;
}; 