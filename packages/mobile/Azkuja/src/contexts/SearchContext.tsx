import React, { createContext, useContext, useReducer, useCallback, useEffect } from 'react';
import { 
  SearchFilters, 
  SearchSuggestion, 
  SearchHistory, 
  PopularSearch, 
  CuisineOption, 
  LocationSuggestion, 
  AdvancedSearchRequest, 
  AdvancedSearchResponse,
  Restaurant,
  MenuItem
} from '../lib/api';
import apiService from '../lib/api';

// Search Context Types
interface SearchState {
  // Current search
  currentQuery: string;
  currentFilters: SearchFilters;
  searchResults: {
    restaurants: Restaurant[];
    menuItems: MenuItem[];
    totalResults: number;
    searchTime: number;
  };
  
  // Search UI state
  isSearching: boolean;
  showSuggestions: boolean;
  showFilters: boolean;
  
  // Suggestions and history
  suggestions: SearchSuggestion[];
  searchHistory: SearchHistory[];
  popularSearches: PopularSearch[];
  
  // Filter options
  cuisineOptions: CuisineOption[];
  locationSuggestions: LocationSuggestion[];
  
  // Error state
  error: string | null;
}

type SearchAction =
  | { type: 'SET_QUERY'; payload: string }
  | { type: 'SET_FILTERS'; payload: Partial<SearchFilters> }
  | { type: 'SET_SEARCH_RESULTS'; payload: SearchState['searchResults'] }
  | { type: 'SET_SEARCHING'; payload: boolean }
  | { type: 'SET_SHOW_SUGGESTIONS'; payload: boolean }
  | { type: 'SET_SHOW_FILTERS'; payload: boolean }
  | { type: 'SET_SUGGESTIONS'; payload: SearchSuggestion[] }
  | { type: 'SET_SEARCH_HISTORY'; payload: SearchHistory[] }
  | { type: 'SET_POPULAR_SEARCHES'; payload: PopularSearch[] }
  | { type: 'SET_CUISINE_OPTIONS'; payload: CuisineOption[] }
  | { type: 'SET_LOCATION_SUGGESTIONS'; payload: LocationSuggestion[] }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'CLEAR_SEARCH'; }
  | { type: 'RESET_FILTERS'; };

interface SearchContextType {
  state: SearchState;
  
  // Search actions
  setQuery: (query: string) => void;
  setFilters: (filters: Partial<SearchFilters>) => void;
  performSearch: (saveToHistory?: boolean) => Promise<void>;
  clearSearch: () => void;
  resetFilters: () => void;
  
  // Suggestions
  loadSuggestions: (query: string) => Promise<void>;
  setSuggestions: (suggestions: SearchSuggestion[]) => void;
  showSuggestions: () => void;
  hideSuggestions: () => void;
  
  // History
  loadSearchHistory: () => Promise<void>;
  clearSearchHistory: () => Promise<void>;
  
  // Popular searches
  loadPopularSearches: () => Promise<void>;
  
  // Filter options
  loadCuisineOptions: () => Promise<void>;
  loadLocationSuggestions: (query: string) => Promise<void>;
  
  // UI state
  setShowFilters: (show: boolean) => void;
  
  // Quick search actions
  searchByQuery: (query: string) => Promise<void>;
  searchByCuisine: (cuisine: string) => Promise<void>;
  searchByLocation: (location: string) => Promise<void>;
  searchByFilters: (filters: Partial<SearchFilters>) => Promise<void>;
}

// Initial state
const initialFilters: SearchFilters = {
  query: '',
  location: '',
  cuisine: [],
  priceRange: [1, 4],
  rating: 0,
  deliveryTime: 60,
  isOpen: false,
  hasDelivery: false,
  hasPromotion: false,
  sortBy: 'relevance'
};

const initialState: SearchState = {
  currentQuery: '',
  currentFilters: initialFilters,
  searchResults: {
    restaurants: [],
    menuItems: [],
    totalResults: 0,
    searchTime: 0
  },
  isSearching: false,
  showSuggestions: false,
  showFilters: false,
  suggestions: [],
  searchHistory: [],
  popularSearches: [],
  cuisineOptions: [],
  locationSuggestions: [],
  error: null
};

// Reducer
function searchReducer(state: SearchState, action: SearchAction): SearchState {
  switch (action.type) {
    case 'SET_QUERY':
      return {
        ...state,
        currentQuery: action.payload,
        currentFilters: {
          ...state.currentFilters,
          query: action.payload
        }
      };
      
    case 'SET_FILTERS':
      return {
        ...state,
        currentFilters: {
          ...state.currentFilters,
          ...action.payload
        }
      };
      
    case 'SET_SEARCH_RESULTS':
      return {
        ...state,
        searchResults: action.payload,
        isSearching: false
      };
      
    case 'SET_SEARCHING':
      return {
        ...state,
        isSearching: action.payload
      };
      
    case 'SET_SHOW_SUGGESTIONS':
      return {
        ...state,
        showSuggestions: action.payload
      };
      
    case 'SET_SHOW_FILTERS':
      return {
        ...state,
        showFilters: action.payload
      };
      
    case 'SET_SUGGESTIONS':
      return {
        ...state,
        suggestions: action.payload
      };
      
    case 'SET_SEARCH_HISTORY':
      return {
        ...state,
        searchHistory: action.payload
      };
      
    case 'SET_POPULAR_SEARCHES':
      return {
        ...state,
        popularSearches: action.payload
      };
      
    case 'SET_CUISINE_OPTIONS':
      return {
        ...state,
        cuisineOptions: action.payload
      };
      
    case 'SET_LOCATION_SUGGESTIONS':
      return {
        ...state,
        locationSuggestions: action.payload
      };
      
    case 'SET_ERROR':
      return {
        ...state,
        error: action.payload,
        isSearching: false
      };
      
    case 'CLEAR_SEARCH':
      return {
        ...state,
        currentQuery: '',
        currentFilters: { ...initialFilters },
        searchResults: {
          restaurants: [],
          menuItems: [],
          totalResults: 0,
          searchTime: 0
        },
        showSuggestions: false,
        error: null
      };
      
    case 'RESET_FILTERS':
      return {
        ...state,
        currentFilters: {
          ...initialFilters,
          query: state.currentQuery // Keep the current query
        }
      };
      
    default:
      return state;
  }
}

// Context
const SearchContext = createContext<SearchContextType | undefined>(undefined);

// Provider
export const SearchProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [state, dispatch] = useReducer(searchReducer, initialState);

  // Load initial data
  useEffect(() => {
    loadCuisineOptions();
    loadPopularSearches();
    loadSearchHistory();
  }, []);

  // Search actions
  const setQuery = useCallback((query: string) => {
    dispatch({ type: 'SET_QUERY', payload: query });
  }, []);

  const setFilters = useCallback((filters: Partial<SearchFilters>) => {
    dispatch({ type: 'SET_FILTERS', payload: filters });
  }, []);

  const performSearch = useCallback(async (saveToHistory = true) => {
    try {
      dispatch({ type: 'SET_SEARCHING', payload: true });
      dispatch({ type: 'SET_ERROR', payload: null });

      const searchRequest: AdvancedSearchRequest = {
        filters: state.currentFilters,
        pagination: { page: 1, limit: 20 }
      };

      const response: AdvancedSearchResponse = await apiService.performAdvancedSearch(searchRequest);

      dispatch({ 
        type: 'SET_SEARCH_RESULTS', 
        payload: {
          restaurants: response.restaurants,
          menuItems: response.menuItems,
          totalResults: response.totalResults,
          searchTime: response.searchTime
        }
      });

      // Save to history if requested
      if (saveToHistory && state.currentFilters.query) {
        await apiService.saveSearchHistory(
          state.currentFilters.query,
          state.currentFilters,
          response.totalResults
        );
        await loadSearchHistory();
      }

    } catch (error) {
      console.error('Search error:', error);
      dispatch({ 
        type: 'SET_ERROR', 
        payload: error instanceof Error ? error.message : 'خطا در جستجو' 
      });
    }
  }, [state.currentFilters]);

  const clearSearch = useCallback(() => {
    dispatch({ type: 'CLEAR_SEARCH' });
  }, []);

  const resetFilters = useCallback(() => {
    dispatch({ type: 'RESET_FILTERS' });
  }, []);

  // Suggestions
  const loadSuggestions = useCallback(async (query: string) => {
    if (!query.trim()) {
      dispatch({ type: 'SET_SUGGESTIONS', payload: [] });
      return;
    }

    try {
      const suggestions = await apiService.getSearchSuggestions(query, 10);
      dispatch({ type: 'SET_SUGGESTIONS', payload: suggestions });
    } catch (error) {
      console.error('Error loading suggestions:', error);
      dispatch({ type: 'SET_SUGGESTIONS', payload: [] });
    }
  }, []);

  const setSuggestions = useCallback((suggestions: SearchSuggestion[]) => {
    dispatch({ type: 'SET_SUGGESTIONS', payload: suggestions });
  }, []);

  const showSuggestions = useCallback(() => {
    dispatch({ type: 'SET_SHOW_SUGGESTIONS', payload: true });
  }, []);

  const hideSuggestions = useCallback(() => {
    dispatch({ type: 'SET_SHOW_SUGGESTIONS', payload: false });
  }, []);

  // History
  const loadSearchHistory = useCallback(async () => {
    try {
      const history = await apiService.getSearchHistory(10);
      dispatch({ type: 'SET_SEARCH_HISTORY', payload: history });
    } catch (error) {
      console.error('Error loading search history:', error);
    }
  }, []);

  const clearSearchHistory = useCallback(async () => {
    try {
      await apiService.clearSearchHistory();
      dispatch({ type: 'SET_SEARCH_HISTORY', payload: [] });
    } catch (error) {
      console.error('Error clearing search history:', error);
    }
  }, []);

  // Popular searches
  const loadPopularSearches = useCallback(async () => {
    try {
      const popular = await apiService.getPopularSearches(8);
      dispatch({ type: 'SET_POPULAR_SEARCHES', payload: popular });
    } catch (error) {
      console.error('Error loading popular searches:', error);
    }
  }, []);

  // Filter options
  const loadCuisineOptions = useCallback(async () => {
    try {
      const cuisines = await apiService.getCuisineOptions();
      dispatch({ type: 'SET_CUISINE_OPTIONS', payload: cuisines });
    } catch (error) {
      console.error('Error loading cuisine options:', error);
    }
  }, []);

  const loadLocationSuggestions = useCallback(async (query: string) => {
    if (!query.trim()) {
      dispatch({ type: 'SET_LOCATION_SUGGESTIONS', payload: [] });
      return;
    }

    try {
      const locations = await apiService.getLocationSuggestions(query, 10);
      dispatch({ type: 'SET_LOCATION_SUGGESTIONS', payload: locations });
    } catch (error) {
      console.error('Error loading location suggestions:', error);
      dispatch({ type: 'SET_LOCATION_SUGGESTIONS', payload: [] });
    }
  }, []);

  // UI state
  const setShowFilters = useCallback((show: boolean) => {
    dispatch({ type: 'SET_SHOW_FILTERS', payload: show });
  }, []);

  // Quick search actions
  const searchByQuery = useCallback(async (query: string) => {
    setQuery(query);
    dispatch({ type: 'SET_FILTERS', payload: { query } });
    // Perform search on next tick to ensure state is updated
    setTimeout(() => performSearch(), 0);
  }, [setQuery, performSearch]);

  const searchByCuisine = useCallback(async (cuisine: string) => {
    const filters = { cuisine: [cuisine] };
    setFilters(filters);
    // Perform search on next tick to ensure state is updated
    setTimeout(() => performSearch(), 0);
  }, [setFilters, performSearch]);

  const searchByLocation = useCallback(async (location: string) => {
    const filters = { location };
    setFilters(filters);
    // Perform search on next tick to ensure state is updated
    setTimeout(() => performSearch(), 0);
  }, [setFilters, performSearch]);

  const searchByFilters = useCallback(async (filters: Partial<SearchFilters>) => {
    setFilters(filters);
    // Perform search on next tick to ensure state is updated
    setTimeout(() => performSearch(), 0);
  }, [setFilters, performSearch]);

  const contextValue: SearchContextType = {
    state,
    setQuery,
    setFilters,
    performSearch,
    clearSearch,
    resetFilters,
    loadSuggestions,
    setSuggestions,
    showSuggestions,
    hideSuggestions,
    loadSearchHistory,
    clearSearchHistory,
    loadPopularSearches,
    loadCuisineOptions,
    loadLocationSuggestions,
    setShowFilters,
    searchByQuery,
    searchByCuisine,
    searchByLocation,
    searchByFilters
  };

  return (
    <SearchContext.Provider value={contextValue}>
      {children}
    </SearchContext.Provider>
  );
};

// Hook
export const useSearch = (): SearchContextType => {
  const context = useContext(SearchContext);
  if (!context) {
    throw new Error('useSearch must be used within a SearchProvider');
  }
  return context;
}; 