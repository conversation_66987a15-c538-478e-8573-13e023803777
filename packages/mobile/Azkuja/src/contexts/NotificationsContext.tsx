import React, { createContext, useContext, useReducer, useCallback, useEffect } from 'react';
import { Platform, Alert } from 'react-native';
import * as Notifications from 'expo-notifications';
import * as Device from 'expo-device';
import Constants from 'expo-constants';

import { 
  PushNotification, 
  NotificationSettings,
  OrderUpdate,
  ChatMessage
} from '../lib/api';
import apiService from '../lib/api';
import webSocketService from '../services/WebSocketService';
import { useAuth } from './AuthContext';

// Configure notification behavior
Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: true,
    shouldSetBadge: true,
  }),
});

// Notification Context Types
interface NotificationsState {
  // Notifications
  notifications: PushNotification[];
  unreadCount: number;
  isLoading: boolean;
  
  // Settings
  settings: NotificationSettings;
  pushToken: string | null;
  permissionStatus: 'granted' | 'denied' | 'undetermined';
  
  // Real-time updates
  orderUpdates: OrderUpdate[];
  chatMessages: ChatMessage[];
  
  // Error state
  error: string | null;
}

type NotificationsAction =
  | { type: 'SET_NOTIFICATIONS'; payload: PushNotification[] }
  | { type: 'ADD_NOTIFICATION'; payload: PushNotification }
  | { type: 'MARK_AS_READ'; payload: string }
  | { type: 'MARK_ALL_AS_READ' }
  | { type: 'DELETE_NOTIFICATION'; payload: string }
  | { type: 'SET_SETTINGS'; payload: NotificationSettings }
  | { type: 'SET_PUSH_TOKEN'; payload: string | null }
  | { type: 'SET_PERMISSION_STATUS'; payload: NotificationsState['permissionStatus'] }
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'ADD_ORDER_UPDATE'; payload: OrderUpdate }
  | { type: 'ADD_CHAT_MESSAGE'; payload: ChatMessage }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'UPDATE_UNREAD_COUNT'; payload: number };

interface NotificationsContextType {
  state: NotificationsState;
  
  // Notification actions
  loadNotifications: () => Promise<void>;
  markAsRead: (notificationId: string) => Promise<void>;
  markAllAsRead: () => Promise<void>;
  deleteNotification: (notificationId: string) => Promise<void>;
  
  // Settings actions
  loadSettings: () => Promise<void>;
  updateSettings: (settings: Partial<NotificationSettings>) => Promise<void>;
  
  // Permission actions
  requestPermissions: () => Promise<boolean>;
  checkPermissions: () => Promise<void>;
  
  // Push notification actions
  registerForPushNotifications: () => Promise<void>;
  unregisterFromPushNotifications: () => Promise<void>;
  
  // Real-time actions
  startRealTimeUpdates: () => void;
  stopRealTimeUpdates: () => void;
  
  // Utility actions
  showLocalNotification: (title: string, body: string, data?: any) => Promise<void>;
  scheduleNotification: (title: string, body: string, trigger: Date, data?: any) => Promise<string>;
  cancelScheduledNotification: (notificationId: string) => Promise<void>;
}

// Initial state
const initialState: NotificationsState = {
  notifications: [],
  unreadCount: 0,
  isLoading: false,
  settings: {
    orderUpdates: true,
    promotions: true,
    newRestaurants: false,
    reminders: true,
    marketing: false,
    pushEnabled: true,
    emailEnabled: true,
    smsEnabled: false
  },
  pushToken: null,
  permissionStatus: 'undetermined',
  orderUpdates: [],
  chatMessages: [],
  error: null
};

// Reducer
function notificationsReducer(state: NotificationsState, action: NotificationsAction): NotificationsState {
  switch (action.type) {
    case 'SET_NOTIFICATIONS':
      return {
        ...state,
        notifications: action.payload,
        unreadCount: action.payload.filter(n => !n.read).length,
        isLoading: false
      };
      
    case 'ADD_NOTIFICATION':
      const newNotifications = [action.payload, ...state.notifications];
      return {
        ...state,
        notifications: newNotifications,
        unreadCount: newNotifications.filter(n => !n.read).length
      };
      
    case 'MARK_AS_READ':
      const updatedNotifications = state.notifications.map(n =>
        n.id === action.payload ? { ...n, read: true } : n
      );
      return {
        ...state,
        notifications: updatedNotifications,
        unreadCount: updatedNotifications.filter(n => !n.read).length
      };
      
    case 'MARK_ALL_AS_READ':
      const allReadNotifications = state.notifications.map(n => ({ ...n, read: true }));
      return {
        ...state,
        notifications: allReadNotifications,
        unreadCount: 0
      };
      
    case 'DELETE_NOTIFICATION':
      const filteredNotifications = state.notifications.filter(n => n.id !== action.payload);
      return {
        ...state,
        notifications: filteredNotifications,
        unreadCount: filteredNotifications.filter(n => !n.read).length
      };
      
    case 'SET_SETTINGS':
      return {
        ...state,
        settings: action.payload
      };
      
    case 'SET_PUSH_TOKEN':
      return {
        ...state,
        pushToken: action.payload
      };
      
    case 'SET_PERMISSION_STATUS':
      return {
        ...state,
        permissionStatus: action.payload
      };
      
    case 'SET_LOADING':
      return {
        ...state,
        isLoading: action.payload
      };
      
    case 'ADD_ORDER_UPDATE':
      return {
        ...state,
        orderUpdates: [action.payload, ...state.orderUpdates.slice(0, 49)] // Keep last 50
      };
      
    case 'ADD_CHAT_MESSAGE':
      return {
        ...state,
        chatMessages: [action.payload, ...state.chatMessages.slice(0, 99)] // Keep last 100
      };
      
    case 'SET_ERROR':
      return {
        ...state,
        error: action.payload,
        isLoading: false
      };
      
    case 'UPDATE_UNREAD_COUNT':
      return {
        ...state,
        unreadCount: action.payload
      };
      
    default:
      return state;
  }
}

// Context
const NotificationsContext = createContext<NotificationsContextType | undefined>(undefined);

// Provider
export const NotificationsProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [state, dispatch] = useReducer(notificationsReducer, initialState);
  const { user, isAuthenticated } = useAuth();

  // Load initial data when user authenticates
  useEffect(() => {
    if (isAuthenticated && user) {
      loadNotifications();
      loadSettings();
      checkPermissions();
      registerForPushNotifications();
      startRealTimeUpdates();
    } else {
      stopRealTimeUpdates();
    }
  }, [isAuthenticated, user]);

  // Handle foreground notifications
  useEffect(() => {
    const foregroundSubscription = Notifications.addNotificationReceivedListener(notification => {
      console.log('🔔 Foreground notification received:', notification);
      
      // Convert Expo notification to our format
      const pushNotification: PushNotification = {
        id: notification.request.identifier,
        type: notification.request.content.data?.type || 'system',
        title: notification.request.content.title || '',
        body: notification.request.content.body || '',
        data: notification.request.content.data,
        read: false,
        created_at: new Date().toISOString(),
        priority: 'normal'
      };
      
      dispatch({ type: 'ADD_NOTIFICATION', payload: pushNotification });
    });

    const responseSubscription = Notifications.addNotificationResponseReceivedListener(response => {
      console.log('🔔 Notification response received:', response);
      
      // Handle notification tap
      const data = response.notification.request.content.data;
      if (data?.action_url) {
        // Navigate to specific screen based on action_url
        // This would be handled by navigation service
      }
    });

    return () => {
      foregroundSubscription.remove();
      responseSubscription.remove();
    };
  }, []);

  // Notification actions
  const loadNotifications = useCallback(async () => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      const notifications = await apiService.getNotifications(1, 50);
      dispatch({ type: 'SET_NOTIFICATIONS', payload: notifications });
    } catch (error) {
      console.error('Error loading notifications:', error);
      dispatch({ type: 'SET_ERROR', payload: 'خطا در بارگذاری اعلان‌ها' });
    }
  }, []);

  const markAsRead = useCallback(async (notificationId: string) => {
    try {
      await apiService.markNotificationAsRead(notificationId);
      dispatch({ type: 'MARK_AS_READ', payload: notificationId });
    } catch (error) {
      console.error('Error marking notification as read:', error);
    }
  }, []);

  const markAllAsRead = useCallback(async () => {
    try {
      await apiService.markAllNotificationsAsRead();
      dispatch({ type: 'MARK_ALL_AS_READ' });
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
    }
  }, []);

  const deleteNotification = useCallback(async (notificationId: string) => {
    try {
      await apiService.deleteNotification(notificationId);
      dispatch({ type: 'DELETE_NOTIFICATION', payload: notificationId });
    } catch (error) {
      console.error('Error deleting notification:', error);
    }
  }, []);

  // Settings actions
  const loadSettings = useCallback(async () => {
    try {
      const settings = await apiService.getNotificationSettings();
      dispatch({ type: 'SET_SETTINGS', payload: settings });
    } catch (error) {
      console.error('Error loading notification settings:', error);
    }
  }, []);

  const updateSettings = useCallback(async (newSettings: Partial<NotificationSettings>) => {
    try {
      const updatedSettings = await apiService.updateNotificationSettings(newSettings);
      dispatch({ type: 'SET_SETTINGS', payload: updatedSettings });
      
      // Update push notification registration if push settings changed
      if ('pushEnabled' in newSettings) {
        if (newSettings.pushEnabled) {
          await registerForPushNotifications();
        } else {
          await unregisterFromPushNotifications();
        }
      }
    } catch (error) {
      console.error('Error updating notification settings:', error);
      dispatch({ type: 'SET_ERROR', payload: 'خطا در به‌روزرسانی تنظیمات' });
    }
  }, []);

  // Permission actions
  const checkPermissions = useCallback(async () => {
    try {
      const { status } = await Notifications.getPermissionsAsync();
      dispatch({ type: 'SET_PERMISSION_STATUS', payload: status });
    } catch (error) {
      console.error('Error checking notification permissions:', error);
    }
  }, []);

  const requestPermissions = useCallback(async (): Promise<boolean> => {
    try {
      if (!Device.isDevice) {
        Alert.alert('خطا', 'اعلان‌ها فقط روی دستگاه‌های واقعی کار می‌کنند');
        return false;
      }

      const { status: existingStatus } = await Notifications.getPermissionsAsync();
      let finalStatus = existingStatus;

      if (existingStatus !== 'granted') {
        const { status } = await Notifications.requestPermissionsAsync();
        finalStatus = status;
      }

      dispatch({ type: 'SET_PERMISSION_STATUS', payload: finalStatus });

      if (finalStatus !== 'granted') {
        Alert.alert(
          'مجوز مورد نیاز',
          'برای دریافت اعلان‌ها، لطفاً مجوز اعلان‌ها را در تنظیمات فعال کنید.',
          [{ text: 'باشه' }]
        );
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error requesting notification permissions:', error);
      return false;
    }
  }, []);

  // Push notification actions
  const registerForPushNotifications = useCallback(async () => {
    try {
      if (!state.settings.pushEnabled) {
        return;
      }

      const hasPermission = await requestPermissions();
      if (!hasPermission) {
        return;
      }

      const token = await Notifications.getExpoPushTokenAsync({
        projectId: Constants.expoConfig?.extra?.eas?.projectId,
      });

      dispatch({ type: 'SET_PUSH_TOKEN', payload: token.data });
      
      await apiService.registerForPushNotifications(token.data, Platform.OS as 'ios' | 'android');
      
      console.log('✅ Registered for push notifications:', token.data);
    } catch (error) {
      console.error('Error registering for push notifications:', error);
      dispatch({ type: 'SET_ERROR', payload: 'خطا در ثبت اعلان‌ها' });
    }
  }, [state.settings.pushEnabled, requestPermissions]);

  const unregisterFromPushNotifications = useCallback(async () => {
    try {
      await apiService.unregisterFromPushNotifications();
      dispatch({ type: 'SET_PUSH_TOKEN', payload: null });
      console.log('✅ Unregistered from push notifications');
    } catch (error) {
      console.error('Error unregistering from push notifications:', error);
    }
  }, []);

  // Real-time actions
  const startRealTimeUpdates = useCallback(() => {
    if (!user) return;

    webSocketService.updateEventHandlers({
      onOrderUpdate: (update: OrderUpdate) => {
        console.log('📱 Real-time order update:', update);
        dispatch({ type: 'ADD_ORDER_UPDATE', payload: update });
        
        // Show local notification if app is in foreground
        if (state.settings.orderUpdates) {
          showLocalNotification(
            'به‌روزرسانی سفارش',
            update.message,
            { type: 'order_update', order_id: update.order_id }
          );
        }
      },
      
      onChatMessage: (message: ChatMessage) => {
        console.log('💬 Real-time chat message:', message);
        dispatch({ type: 'ADD_CHAT_MESSAGE', payload: message });
        
        // Show local notification for new messages
        showLocalNotification(
          `پیام جدید از ${message.sender_name}`,
          message.message,
          { type: 'chat', order_id: message.order_id }
        );
      },
      
      onNotification: (notification: PushNotification) => {
        console.log('🔔 Real-time notification:', notification);
        dispatch({ type: 'ADD_NOTIFICATION', payload: notification });
      },
      
      onConnect: () => {
        console.log('✅ Real-time connection established');
      },
      
      onDisconnect: (reason: string) => {
        console.log('❌ Real-time connection lost:', reason);
      },
      
      onError: (error: Error) => {
        console.error('❌ Real-time connection error:', error);
      }
    });
  }, [user, state.settings]);

  const stopRealTimeUpdates = useCallback(() => {
    webSocketService.updateEventHandlers({});
  }, []);

  // Utility actions
  const showLocalNotification = useCallback(async (title: string, body: string, data?: any) => {
    try {
      await Notifications.scheduleNotificationAsync({
        content: {
          title,
          body,
          data,
        },
        trigger: null, // Show immediately
      });
    } catch (error) {
      console.error('Error showing local notification:', error);
    }
  }, []);

  const scheduleNotification = useCallback(async (
    title: string, 
    body: string, 
    trigger: Date, 
    data?: any
  ): Promise<string> => {
    try {
      const notificationId = await Notifications.scheduleNotificationAsync({
        content: {
          title,
          body,
          data,
        },
        trigger: {
          date: trigger,
        },
      });
      
      return notificationId;
    } catch (error) {
      console.error('Error scheduling notification:', error);
      throw error;
    }
  }, []);

  const cancelScheduledNotification = useCallback(async (notificationId: string) => {
    try {
      await Notifications.cancelScheduledNotificationAsync(notificationId);
    } catch (error) {
      console.error('Error canceling scheduled notification:', error);
    }
  }, []);

  const contextValue: NotificationsContextType = {
    state,
    loadNotifications,
    markAsRead,
    markAllAsRead,
    deleteNotification,
    loadSettings,
    updateSettings,
    requestPermissions,
    checkPermissions,
    registerForPushNotifications,
    unregisterFromPushNotifications,
    startRealTimeUpdates,
    stopRealTimeUpdates,
    showLocalNotification,
    scheduleNotification,
    cancelScheduledNotification
  };

  return (
    <NotificationsContext.Provider value={contextValue}>
      {children}
    </NotificationsContext.Provider>
  );
};

// Hook
export const useNotifications = (): NotificationsContextType => {
  const context = useContext(NotificationsContext);
  if (!context) {
    throw new Error('useNotifications must be used within a NotificationsProvider');
  }
  return context;
}; 