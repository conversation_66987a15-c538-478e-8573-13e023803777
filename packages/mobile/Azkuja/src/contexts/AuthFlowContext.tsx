import React, { createContext, useContext, useState, ReactNode } from 'react';

type AuthFlowStep = 'phone' | 'otp' | 'register' | 'forgotPassword';

interface AuthFlowContextType {
  currentStep: AuthFlowStep;
  phoneNumber: string;
  message: string;
  setPhoneStep: () => void;
  setOtpStep: (phone: string, msg: string) => void;
  setRegisterStep: () => void;
  setForgotPasswordStep: () => void;
  resetFlow: () => void;
}

const AuthFlowContext = createContext<AuthFlowContextType | undefined>(undefined);

interface AuthFlowProviderProps {
  children: ReactNode;
}

export const AuthFlowProvider: React.FC<AuthFlowProviderProps> = ({ children }) => {
  const [currentStep, setCurrentStep] = useState<AuthFlowStep>('phone');
  const [phoneNumber, setPhoneNumber] = useState('');
  const [message, setMessage] = useState('');

  const setPhoneStep = () => {
    console.log('🔄 AuthFlowProvider: Setting step to phone');
    setCurrentStep('phone');
    setPhoneNumber('');
    setMessage('');
  };

  const setOtpStep = (phone: string, msg: string) => {
    console.log('🔄 AuthFlowProvider: Setting step to OTP with phone:', phone);
    setCurrentStep('otp');
    setPhoneNumber(phone);
    setMessage(msg);
  };

  const setRegisterStep = () => {
    console.log('🔄 AuthFlowProvider: Setting step to register');
    setCurrentStep('register');
  };

  const setForgotPasswordStep = () => {
    console.log('🔄 AuthFlowProvider: Setting step to forgot password');
    setCurrentStep('forgotPassword');
  };

  const resetFlow = () => {
    console.log('🔄 AuthFlowProvider: Resetting flow to phone');
    setCurrentStep('phone');
    setPhoneNumber('');
    setMessage('');
  };

  const value: AuthFlowContextType = {
    currentStep,
    phoneNumber,
    message,
    setPhoneStep,
    setOtpStep,
    setRegisterStep,
    setForgotPasswordStep,
    resetFlow,
  };

  return <AuthFlowContext.Provider value={value}>{children}</AuthFlowContext.Provider>;
};

export const useAuthFlow = (): AuthFlowContextType => {
  const context = useContext(AuthFlowContext);
  if (context === undefined) {
    throw new Error('useAuthFlow must be used within an AuthFlowProvider');
  }
  return context;
}; 