import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { Alert } from 'react-native';
import apiService, { PaymentMethod, Payment, PaymentStats, CreatePaymentRequest } from '../lib/api';
import { useAuth } from './AuthContext';

interface PaymentContextType {
  // Payment Methods
  paymentMethods: PaymentMethod[];
  defaultPaymentMethod: PaymentMethod | null;
  isLoadingMethods: boolean;
  
  // Payment History
  paymentHistory: Payment[];
  isLoadingHistory: boolean;
  
  // Payment Stats
  paymentStats: PaymentStats | null;
  
  // Payment Processing
  isProcessingPayment: boolean;
  
  // Actions
  loadPaymentMethods: () => Promise<void>;
  addPaymentMethod: (method: Omit<PaymentMethod, 'id' | 'created_at' | 'updated_at'>) => Promise<PaymentMethod>;
  updatePaymentMethod: (id: string, updates: Partial<PaymentMethod>) => Promise<PaymentMethod>;
  deletePaymentMethod: (id: string) => Promise<void>;
  setDefaultPaymentMethod: (id: string) => Promise<void>;
  
  loadPaymentHistory: (page?: number, limit?: number) => Promise<void>;
  loadPaymentStats: () => Promise<void>;
  
  processPayment: (paymentData: CreatePaymentRequest) => Promise<Payment>;
  processRefund: (paymentId: string) => Promise<Payment>;
  
  // Utility
  refreshAll: () => Promise<void>;
}

const PaymentContext = createContext<PaymentContextType | undefined>(undefined);

export const usePayment = () => {
  const context = useContext(PaymentContext);
  if (!context) {
    throw new Error('usePayment must be used within a PaymentProvider');
  }
  return context;
};

interface PaymentProviderProps {
  children: ReactNode;
}

export const PaymentProvider: React.FC<PaymentProviderProps> = ({ children }) => {
  const { user } = useAuth();
  
  // Payment Methods State
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>([]);
  const [defaultPaymentMethod, setDefaultPaymentMethodState] = useState<PaymentMethod | null>(null);
  const [isLoadingMethods, setIsLoadingMethods] = useState(false);
  
  // Payment History State
  const [paymentHistory, setPaymentHistory] = useState<Payment[]>([]);
  const [isLoadingHistory, setIsLoadingHistory] = useState(false);
  
  // Payment Stats State
  const [paymentStats, setPaymentStats] = useState<PaymentStats | null>(null);
  
  // Payment Processing State
  const [isProcessingPayment, setIsProcessingPayment] = useState(false);

  // Load payment methods
  const loadPaymentMethods = async () => {
    if (!user) return;
    
    setIsLoadingMethods(true);
    try {
      const methods = await apiService.getPaymentMethods();
      setPaymentMethods(methods);
      
      // Set default payment method
      const defaultMethod = methods.find(method => method.is_default);
      setDefaultPaymentMethodState(defaultMethod || null);
    } catch (error) {
      console.error('Error loading payment methods:', error);
      Alert.alert('خطا', 'خطا در بارگذاری روش‌های پرداخت');
    } finally {
      setIsLoadingMethods(false);
    }
  };

  // Add payment method
  const addPaymentMethod = async (method: Omit<PaymentMethod, 'id' | 'created_at' | 'updated_at'>): Promise<PaymentMethod> => {
    try {
      const newMethod = await apiService.addPaymentMethod(method);
      
      // Update local state with optimistic update
      setPaymentMethods(prev => [...prev, newMethod]);
      
      // If this is the first method, make it default
      if (paymentMethods.length === 0) {
        setDefaultPaymentMethodState(newMethod);
      }
      
      return newMethod;
    } catch (error) {
      console.error('Error adding payment method:', error);
      Alert.alert('خطا', 'خطا در افزودن روش پرداخت');
      throw error;
    }
  };

  // Update payment method
  const updatePaymentMethod = async (id: string, updates: Partial<PaymentMethod>): Promise<PaymentMethod> => {
    try {
      const updatedMethod = await apiService.updatePaymentMethod(id, updates);
      
      // Update local state
      setPaymentMethods(prev => 
        prev.map(method => 
          method.id === id ? updatedMethod : method
        )
      );
      
      // Update default method if needed
      if (updatedMethod.is_default) {
        setDefaultPaymentMethodState(updatedMethod);
      }
      
      return updatedMethod;
    } catch (error) {
      console.error('Error updating payment method:', error);
      Alert.alert('خطا', 'خطا در به‌روزرسانی روش پرداخت');
      throw error;
    }
  };

  // Delete payment method
  const deletePaymentMethod = async (id: string): Promise<void> => {
    try {
      await apiService.deletePaymentMethod(id);
      
      // Update local state
      setPaymentMethods(prev => prev.filter(method => method.id !== id));
      
      // Update default method if deleted method was default
      if (defaultPaymentMethod?.id === id) {
        const remainingMethods = paymentMethods.filter(method => method.id !== id);
        setDefaultPaymentMethodState(remainingMethods[0] || null);
      }
    } catch (error) {
      console.error('Error deleting payment method:', error);
      Alert.alert('خطا', 'خطا در حذف روش پرداخت');
      throw error;
    }
  };

  // Set default payment method
  const setDefaultPaymentMethod = async (id: string): Promise<void> => {
    try {
      await apiService.setDefaultPaymentMethod(id);
      
      // Update local state
      setPaymentMethods(prev => 
        prev.map(method => ({
          ...method,
          is_default: method.id === id
        }))
      );
      
      const newDefaultMethod = paymentMethods.find(method => method.id === id);
      setDefaultPaymentMethodState(newDefaultMethod || null);
    } catch (error) {
      console.error('Error setting default payment method:', error);
      Alert.alert('خطا', 'خطا در تنظیم روش پرداخت پیش‌فرض');
      throw error;
    }
  };

  // Load payment history
  const loadPaymentHistory = async (page: number = 1, limit: number = 10): Promise<void> => {
    if (!user) return;
    
    setIsLoadingHistory(true);
    try {
      const payments = await apiService.getPaymentHistory(page, limit);
      
      if (page === 1) {
        setPaymentHistory(payments);
      } else {
        setPaymentHistory(prev => [...prev, ...payments]);
      }
    } catch (error) {
      console.error('Error loading payment history:', error);
      Alert.alert('خطا', 'خطا در بارگذاری تاریخچه پرداخت');
    } finally {
      setIsLoadingHistory(false);
    }
  };

  // Load payment stats
  const loadPaymentStats = async (): Promise<void> => {
    if (!user) return;
    
    try {
      const stats = await apiService.getPaymentStats();
      setPaymentStats(stats);
    } catch (error) {
      console.error('Error loading payment stats:', error);
      // Don't show alert for stats as it's not critical
    }
  };

  // Process payment
  const processPayment = async (paymentData: CreatePaymentRequest): Promise<Payment> => {
    setIsProcessingPayment(true);
    try {
      const payment = await apiService.createPayment(paymentData);
      
      // Add to payment history
      setPaymentHistory(prev => [payment, ...prev]);
      
      // Refresh stats
      loadPaymentStats();
      
      return payment;
    } catch (error) {
      console.error('Error processing payment:', error);
      Alert.alert('خطا', 'خطا در پردازش پرداخت');
      throw error;
    } finally {
      setIsProcessingPayment(false);
    }
  };

  // Process refund
  const processRefund = async (paymentId: string): Promise<Payment> => {
    try {
      const refundedPayment = await apiService.processRefund(paymentId);
      
      // Update payment history
      setPaymentHistory(prev => 
        prev.map(payment => 
          payment.id === paymentId ? refundedPayment : payment
        )
      );
      
      // Refresh stats
      loadPaymentStats();
      
      return refundedPayment;
    } catch (error) {
      console.error('Error processing refund:', error);
      Alert.alert('خطا', 'خطا در پردازش بازپرداخت');
      throw error;
    }
  };

  // Refresh all payment data
  const refreshAll = async (): Promise<void> => {
    await Promise.all([
      loadPaymentMethods(),
      loadPaymentHistory(),
      loadPaymentStats()
    ]);
  };

  // Load initial data when user logs in
  useEffect(() => {
    if (user) {
      refreshAll();
    } else {
      // Clear data when user logs out
      setPaymentMethods([]);
      setDefaultPaymentMethodState(null);
      setPaymentHistory([]);
      setPaymentStats(null);
    }
  }, [user]);

  const value: PaymentContextType = {
    // Payment Methods
    paymentMethods,
    defaultPaymentMethod,
    isLoadingMethods,
    
    // Payment History
    paymentHistory,
    isLoadingHistory,
    
    // Payment Stats
    paymentStats,
    
    // Payment Processing
    isProcessingPayment,
    
    // Actions
    loadPaymentMethods,
    addPaymentMethod,
    updatePaymentMethod,
    deletePaymentMethod,
    setDefaultPaymentMethod,
    
    loadPaymentHistory,
    loadPaymentStats,
    
    processPayment,
    processRefund,
    
    // Utility
    refreshAll
  };

  return (
    <PaymentContext.Provider value={value}>
      {children}
    </PaymentContext.Provider>
  );
}; 