// Font families
export const fonts = {
  // YekanBakh for Dari/Pashto (RTL languages)
  yekanBakh: {
    light: 'YekanBakh-Light',
    regular: 'YekanBakh-Regular',
    medium: 'YekanBakh-SemiBold',
    semiBold: 'YekanBakh-SemiBold',
    bold: 'YekanBakh-Bold',
    extraBold: 'YekanBakh-ExtraBold',
  },
  
  // System fonts for English and fallback
  system: {
    light: 'System',
    regular: 'System',
    medium: 'System',
    semiBold: 'System',
    bold: 'System',
  },
};

// Font sizes
export const fontSizes = {
  xs: 10,
  sm: 12,
  base: 14,
  lg: 16,
  xl: 18,
  '2xl': 20,
  '3xl': 24,
  '4xl': 28,
  '5xl': 32,
  '6xl': 36,
  '7xl': 48,
} as const;

// Line heights
export const lineHeights = {
  none: 1,
  tight: 1.25,
  snug: 1.375,
  normal: 1.5,
  relaxed: 1.625,
  loose: 2,
} as const;

// Letter spacing
export const letterSpacing = {
  tighter: -0.5,
  tight: -0.25,
  normal: 0,
  wide: 0.25,
  wider: 0.5,
  widest: 1,
} as const;

// Font weights
export const fontWeights = {
  light: '300' as const,
  regular: '400' as const,
  medium: '500' as const,
  semiBold: '600' as const,
  bold: '700' as const,
  extraBold: '800' as const,
};

// Typography styles
export const typography = {
  // Heading styles
  heading: {
    h1: {
      fontSize: fontSizes['3xl'],
      fontWeight: '700' as const,
      lineHeight: 32,
    },
    h2: {
      fontSize: fontSizes['2xl'],
      fontWeight: '600' as const,
      lineHeight: 28,
    },
    h3: {
      fontSize: fontSizes.xl,
      fontWeight: '600' as const,
      lineHeight: 24,
    },
  },

  // Body text styles
  body: {
    large: {
      fontSize: fontSizes.lg,
      fontWeight: '400' as const,
      lineHeight: 24,
    },
    medium: {
      fontSize: fontSizes.base,
      fontWeight: '400' as const,
      lineHeight: 20,
    },
    small: {
      fontSize: fontSizes.sm,
      fontWeight: '400' as const,
      lineHeight: 18,
    },
  },

  // Button styles
  button: {
    large: {
      fontSize: fontSizes.lg,
      fontWeight: '600' as const,
      lineHeight: 20,
    },
    medium: {
      fontSize: fontSizes.base,
      fontWeight: '600' as const,
      lineHeight: 18,
    },
    small: {
      fontSize: fontSizes.sm,
      fontWeight: '600' as const,
      lineHeight: 16,
    },
  },

  // Caption
  caption: {
    fontSize: fontSizes.xs,
    fontWeight: '400' as const,
    lineHeight: 14,
  },
};

// Default typography (LTR)
export const typographyLTR = {
  // Display styles
  display: {
    large: {
      fontFamily: fonts.system.bold,
      fontSize: fontSizes['7xl'],
      lineHeight: fontSizes['7xl'] * lineHeights.tight,
      letterSpacing: letterSpacing.tight,
      fontWeight: fontWeights.bold,
    },
    medium: {
      fontFamily: fonts.system.bold,
      fontSize: fontSizes['5xl'],
      lineHeight: fontSizes['5xl'] * lineHeights.tight,
      letterSpacing: letterSpacing.tight,
      fontWeight: fontWeights.bold,
    },
    small: {
      fontFamily: fonts.system.bold,
      fontSize: fontSizes['4xl'],
      lineHeight: fontSizes['4xl'] * lineHeights.tight,
      letterSpacing: letterSpacing.tight,
      fontWeight: fontWeights.bold,
    },
  },

  // Heading styles
  heading: {
    h4: {
      fontFamily: fonts.system.medium,
      fontSize: fontSizes.lg,
      lineHeight: fontSizes.lg * lineHeights.normal,
      letterSpacing: letterSpacing.normal,
      fontWeight: fontWeights.medium,
    },
    h5: {
      fontFamily: fonts.system.medium,
      fontSize: fontSizes.base,
      lineHeight: fontSizes.base * lineHeights.normal,
      letterSpacing: letterSpacing.normal,
      fontWeight: fontWeights.medium,
    },
    h6: {
      fontFamily: fonts.system.medium,
      fontSize: fontSizes.sm,
      lineHeight: fontSizes.sm * lineHeights.normal,
      letterSpacing: letterSpacing.wide,
      fontWeight: fontWeights.medium,
    },
  },

  // Body text styles
  body: {
    large: {
      fontFamily: fonts.system.regular,
      fontSize: fontSizes.lg,
      lineHeight: fontSizes.lg * lineHeights.relaxed,
      letterSpacing: letterSpacing.normal,
      fontWeight: fontWeights.regular,
    },
    medium: {
      fontFamily: fonts.system.regular,
      fontSize: fontSizes.base,
      lineHeight: fontSizes.base * lineHeights.normal,
      letterSpacing: letterSpacing.normal,
      fontWeight: fontWeights.regular,
    },
    small: {
      fontFamily: fonts.system.regular,
      fontSize: fontSizes.sm,
      lineHeight: fontSizes.sm * lineHeights.normal,
      letterSpacing: letterSpacing.normal,
      fontWeight: fontWeights.regular,
    },
  },

  // Label styles
  label: {
    large: {
      fontFamily: fonts.system.medium,
      fontSize: fontSizes.base,
      lineHeight: fontSizes.base * lineHeights.normal,
      letterSpacing: letterSpacing.wide,
      fontWeight: fontWeights.medium,
    },
    medium: {
      fontFamily: fonts.system.medium,
      fontSize: fontSizes.sm,
      lineHeight: fontSizes.sm * lineHeights.normal,
      letterSpacing: letterSpacing.wide,
      fontWeight: fontWeights.medium,
    },
    small: {
      fontFamily: fonts.system.medium,
      fontSize: fontSizes.xs,
      lineHeight: fontSizes.xs * lineHeights.normal,
      letterSpacing: letterSpacing.wider,
      fontWeight: fontWeights.medium,
    },
  },

  // Caption and utility styles
  overline: {
    fontFamily: fonts.system.medium,
    fontSize: fontSizes.xs,
    lineHeight: fontSizes.xs * lineHeights.normal,
    letterSpacing: letterSpacing.widest,
    fontWeight: fontWeights.medium,
    textTransform: 'uppercase' as const,
  },
};

// RTL typography
export const typographyRTL = {
  // Display styles
  display: {
    large: {
      fontFamily: fonts.yekanBakh.bold,
      fontSize: fontSizes['7xl'],
      lineHeight: fontSizes['7xl'] * lineHeights.tight,
      letterSpacing: letterSpacing.tight,
      fontWeight: fontWeights.bold,
    },
    medium: {
      fontFamily: fonts.yekanBakh.bold,
      fontSize: fontSizes['5xl'],
      lineHeight: fontSizes['5xl'] * lineHeights.tight,
      letterSpacing: letterSpacing.tight,
      fontWeight: fontWeights.bold,
    },
    small: {
      fontFamily: fonts.yekanBakh.bold,
      fontSize: fontSizes['4xl'],
      lineHeight: fontSizes['4xl'] * lineHeights.tight,
      letterSpacing: letterSpacing.tight,
      fontWeight: fontWeights.bold,
    },
  },

  // Heading styles
  heading: {
    h1: {
      fontFamily: fonts.yekanBakh.bold,
      fontSize: fontSizes['3xl'],
      lineHeight: fontSizes['3xl'] * lineHeights.tight,
      letterSpacing: letterSpacing.tight,
      fontWeight: fontWeights.bold,
    },
    h2: {
      fontFamily: fonts.yekanBakh.semiBold,
      fontSize: fontSizes['2xl'],
      lineHeight: fontSizes['2xl'] * lineHeights.snug,
      letterSpacing: letterSpacing.normal,
      fontWeight: fontWeights.semiBold,
    },
    h3: {
      fontFamily: fonts.yekanBakh.semiBold,
      fontSize: fontSizes.xl,
      lineHeight: fontSizes.xl * lineHeights.snug,
      letterSpacing: letterSpacing.normal,
      fontWeight: fontWeights.semiBold,
    },
    h4: {
      fontFamily: fonts.yekanBakh.medium,
      fontSize: fontSizes.lg,
      lineHeight: fontSizes.lg * lineHeights.normal,
      letterSpacing: letterSpacing.normal,
      fontWeight: fontWeights.medium,
    },
    h5: {
      fontFamily: fonts.yekanBakh.medium,
      fontSize: fontSizes.base,
      lineHeight: fontSizes.base * lineHeights.normal,
      letterSpacing: letterSpacing.normal,
      fontWeight: fontWeights.medium,
    },
    h6: {
      fontFamily: fonts.yekanBakh.medium,
      fontSize: fontSizes.sm,
      lineHeight: fontSizes.sm * lineHeights.normal,
      letterSpacing: letterSpacing.wide,
      fontWeight: fontWeights.medium,
    },
  },

  // Body text styles
  body: {
    large: {
      fontFamily: fonts.yekanBakh.regular,
      fontSize: fontSizes.lg,
      lineHeight: fontSizes.lg * lineHeights.relaxed,
      letterSpacing: letterSpacing.normal,
      fontWeight: fontWeights.regular,
    },
    medium: {
      fontFamily: fonts.yekanBakh.regular,
      fontSize: fontSizes.base,
      lineHeight: fontSizes.base * lineHeights.normal,
      letterSpacing: letterSpacing.normal,
      fontWeight: fontWeights.regular,
    },
    small: {
      fontFamily: fonts.yekanBakh.regular,
      fontSize: fontSizes.sm,
      lineHeight: fontSizes.sm * lineHeights.normal,
      letterSpacing: letterSpacing.normal,
      fontWeight: fontWeights.regular,
    },
  },

  // Label styles
  label: {
    large: {
      fontFamily: fonts.yekanBakh.medium,
      fontSize: fontSizes.base,
      lineHeight: fontSizes.base * lineHeights.normal,
      letterSpacing: letterSpacing.wide,
      fontWeight: fontWeights.medium,
    },
    medium: {
      fontFamily: fonts.yekanBakh.medium,
      fontSize: fontSizes.sm,
      lineHeight: fontSizes.sm * lineHeights.normal,
      letterSpacing: letterSpacing.wide,
      fontWeight: fontWeights.medium,
    },
    small: {
      fontFamily: fonts.yekanBakh.medium,
      fontSize: fontSizes.xs,
      lineHeight: fontSizes.xs * lineHeights.normal,
      letterSpacing: letterSpacing.wider,
      fontWeight: fontWeights.medium,
    },
  },

  // Button styles
  button: {
    large: {
      fontFamily: fonts.yekanBakh.semiBold,
      fontSize: fontSizes.lg,
      lineHeight: fontSizes.lg * lineHeights.tight,
      letterSpacing: letterSpacing.wide,
      fontWeight: fontWeights.semiBold,
    },
    medium: {
      fontFamily: fonts.yekanBakh.semiBold,
      fontSize: fontSizes.base,
      lineHeight: fontSizes.base * lineHeights.tight,
      letterSpacing: letterSpacing.wide,
      fontWeight: fontWeights.semiBold,
    },
    small: {
      fontFamily: fonts.yekanBakh.semiBold,
      fontSize: fontSizes.sm,
      lineHeight: fontSizes.sm * lineHeights.tight,
      letterSpacing: letterSpacing.wide,
      fontWeight: fontWeights.semiBold,
    },
  },

  // Caption and utility styles
  caption: {
    fontFamily: fonts.yekanBakh.regular,
    fontSize: fontSizes.xs,
    lineHeight: fontSizes.xs * lineHeights.normal,
    letterSpacing: letterSpacing.wide,
    fontWeight: fontWeights.regular,
  },

  overline: {
    fontFamily: fonts.yekanBakh.medium,
    fontSize: fontSizes.xs,
    lineHeight: fontSizes.xs * lineHeights.normal,
    letterSpacing: letterSpacing.widest,
    fontWeight: fontWeights.medium,
    textTransform: 'uppercase' as const,
  },
};

// Type definitions
export type FontSize = keyof typeof fontSizes;
export type LineHeight = keyof typeof lineHeights;
export type LetterSpacing = keyof typeof letterSpacing;
export type FontWeight = keyof typeof fontWeights; 