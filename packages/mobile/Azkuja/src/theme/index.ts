import { colors } from './colors';
import { typography, fonts, fontSizes, typographyRTL, typographyLTR } from './typography';
import { spacing } from './spacing';

// Define theme components directly to avoid import issues
const borderRadius = {
  none: 0,
  sm: 2,
  base: 4,
  md: 6,
  lg: 8,
  xl: 12,
  '2xl': 16,
  '3xl': 24,
  full: 9999,
} as const;

const shadows = {
  none: {
    shadowColor: 'transparent',
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0,
    shadowRadius: 0,
    elevation: 0,
  },
  sm: {
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  base: {
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  md: {
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 6,
    elevation: 5,
  },
  lg: {
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.15,
    shadowRadius: 12,
    elevation: 8,
  },
  xl: {
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 12 },
    shadowOpacity: 0.2,
    shadowRadius: 16,
    elevation: 12,
  },
} as const;

const zIndex = {
  hide: -1,
  auto: 'auto',
  base: 0,
  docked: 10,
  dropdown: 1000,
  sticky: 1100,
  banner: 1200,
  overlay: 1300,
  modal: 1400,
  popover: 1500,
  skipLink: 1600,
  toast: 1700,
  tooltip: 1800,
} as const;

const layout = {
  // Container max widths
  container: {
    sm: 640,
    md: 768,
    lg: 1024,
    xl: 1280,
  },
  
  // Header/Footer heights
  header: {
    mobile: 56,
    desktop: 64,
  },
  
  // Tab bar height
  tabBar: {
    height: 60,
    safeHeight: 80, // with safe area
  },
  
  // Button heights
  button: {
    sm: 32,
    md: 40,
    lg: 48,
    xl: 56,
  },
  
  // Input heights
  input: {
    sm: 32,
    md: 40,
    lg: 48,
  },
  
  // Icon sizes
  icon: {
    xs: 12,
    sm: 16,
    md: 20,
    lg: 24,
    xl: 32,
    '2xl': 40,
    '3xl': 48,
  },
} as const;

// Main theme object - Default to RTL for Dari
export const theme = {
  colors,
  typography: typographyRTL, // Use RTL typography by default
  fonts: fonts.yekanBakh, // Use YekanBakh fonts by default
  fontSizes,
  spacing,
  borderRadius,
  shadows,
  zIndex,
  layout,
  
  // Afghan currency symbol
  currency: {
    symbol: '؋',
    name: 'Afghan Afghani',
    code: 'AFN',
  },
  
  // RTL support - Default to RTL for Dari
  rtl: {
    isRTL: true, // Default to RTL for Dari language
    direction: 'rtl' as 'ltr' | 'rtl',
  },
  
  // Animation timings
  animation: {
    duration: {
      fast: 200,
      normal: 300,
      slow: 500,
    },
    easing: {
      ease: 'ease',
      easeIn: 'ease-in',
      easeOut: 'ease-out',
      easeInOut: 'ease-in-out',
    },
  },
  
  // Breakpoints for responsive design
  breakpoints: {
    sm: 640,
    md: 768,
    lg: 1024,
    xl: 1280,
  },
} as const;

// Theme with RTL support
export const createTheme = (isRTL: boolean = false) => ({
  ...theme,
  rtl: {
    isRTL,
    direction: isRTL ? 'rtl' as const : 'ltr' as const,
  },
  // Use appropriate fonts based on RTL
  fonts: isRTL ? fonts.yekanBakh : fonts.system,
});

// Export individual theme parts
export { colors } from './colors';
export { typography, fonts, fontSizes, typographyRTL, typographyLTR } from './typography';
export { spacing } from './spacing';

// Export the theme components we defined here
export { borderRadius, shadows, zIndex, layout };

// Export types
export type Theme = typeof theme;
export type Colors = typeof colors;
export type Typography = typeof typography;

// Global text style configuration
export const createGlobalTextStyle = (isRTL: boolean) => ({
  fontFamily: isRTL ? fonts.yekanBakh.regular : fonts.system.regular,
  textAlign: isRTL ? 'right' : 'left',
  writingDirection: isRTL ? 'rtl' : 'ltr',
}); 