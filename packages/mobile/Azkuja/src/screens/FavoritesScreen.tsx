import React, { useCallback } from 'react';
import {
  View,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Image,
  RefreshControl,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useFocusEffect } from '@react-navigation/native';

import { GlobalText } from '../components/GlobalText';
import { useFavorites } from '../contexts/FavoritesContext';
import { useTranslation } from '../i18n';
import { Favorite } from '../lib/api';

interface FavoritesScreenProps {
  navigation?: any;
}

export const FavoritesScreen: React.FC<FavoritesScreenProps> = ({ navigation }) => {
  const { favorites, isLoading, removeFromFavorites, refreshFavorites } = useFavorites();
  const { t } = useTranslation();
  const insets = useSafeAreaInsets();

  // Refresh favorites when screen comes into focus
  useFocusEffect(
    useCallback(() => {
      refreshFavorites();
    }, [refreshFavorites])
  );

  const handleRemoveFavorite = (restaurantId: string, restaurantName: string) => {
    Alert.alert(
      'حذف از علاقه‌مندی‌ها',
      `آیا مطمئن هستید که می‌خواهید "${restaurantName}" را از علاقه‌مندی‌هایتان حذف کنید؟`,
      [
        { text: 'لغو', style: 'cancel' },
        {
          text: 'حذف',
          style: 'destructive',
          onPress: async () => {
            try {
              await removeFromFavorites(restaurantId);
              // Success feedback is handled by the context
            } catch (error) {
              Alert.alert('خطا', 'خطا در حذف رستوران از علاقه‌مندی‌ها');
            }
          },
        },
      ]
    );
  };

  const handleRestaurantPress = (restaurantId: string) => {
    if (navigation) {
      navigation.navigate('RestaurantDetail', { restaurantId });
    }
  };

  const renderFavoriteItem = ({ item }: { item: Favorite }) => {
    const restaurant = item.restaurant;
    
    if (!restaurant) {
      return null;
    }

    return (
      <TouchableOpacity
        style={styles.favoriteItem}
        onPress={() => handleRestaurantPress(restaurant.id)}
        activeOpacity={0.7}
      >
        <Image
          source={{
            uri: restaurant.photos?.[0]?.url || 'https://images.unsplash.com/photo-1555396273-367ea4eb4db5?w=400',
          }}
          style={styles.restaurantImage}
          resizeMode="cover"
        />
        
        <View style={styles.restaurantInfo}>
          <View style={styles.restaurantHeader}>
            <GlobalText variant="heading" style={styles.restaurantName}>
              {restaurant.name}
            </GlobalText>
            <TouchableOpacity
              style={styles.removeButton}
              onPress={() => handleRemoveFavorite(restaurant.id, restaurant.name)}
            >
              <Ionicons name="heart" size={24} color="#e6034b" />
            </TouchableOpacity>
          </View>
          
          <GlobalText variant="body" style={styles.restaurantDescription} numberOfLines={2}>
            {restaurant.description || 'رستوران خوشمزه'}
          </GlobalText>
          
          <View style={styles.restaurantMeta}>
            <View style={styles.ratingContainer}>
              <Ionicons name="star" size={16} color="#FFA500" />
              <GlobalText variant="caption" style={styles.rating}>
                {restaurant.avg_rating.toFixed(1)}
              </GlobalText>
            </View>
            
            <View style={styles.priceContainer}>
              <GlobalText variant="caption" style={styles.priceRange}>
                {'$'.repeat(restaurant.price_range)}
              </GlobalText>
            </View>
            
            <View style={styles.locationContainer}>
              <Ionicons name="location-outline" size={16} color="#757575" />
              <GlobalText variant="caption" style={styles.location}>
                {restaurant.city}
              </GlobalText>
            </View>
          </View>
          
          <View style={styles.addedDate}>
            <Ionicons name="time-outline" size={14} color="#999" />
            <GlobalText variant="caption" style={styles.addedDateText}>
              اضافه شده: {new Date(item.created_at).toLocaleDateString('fa-IR')}
            </GlobalText>
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <Ionicons name="heart-outline" size={64} color="#E0E0E0" />
      <GlobalText variant="heading" style={styles.emptyTitle}>
        هیچ علاقه‌مندی یافت نشد
      </GlobalText>
      <GlobalText variant="body" style={styles.emptyDescription}>
        رستوران‌های مورد علاقه‌تان را با ضربه روی آیکون قلب اضافه کنید
      </GlobalText>
      <TouchableOpacity
        style={styles.exploreButton}
        onPress={() => navigation?.navigate('Explore')}
      >
        <GlobalText variant="button" style={styles.exploreButtonText}>
          کاوش رستوران‌ها
        </GlobalText>
      </TouchableOpacity>
    </View>
  );

  return (
    <View style={[styles.container, { paddingTop: insets.top }]}>
      {/* Header */}
      <View style={styles.header}>
        <GlobalText variant="heading" style={styles.headerTitle}>
          علاقه‌مندی‌ها
        </GlobalText>
        {favorites.length > 0 && (
          <GlobalText variant="caption" style={styles.favoritesCount}>
            {favorites.length} رستوران
          </GlobalText>
        )}
      </View>

      {/* Favorites List */}
      <FlatList
        data={favorites}
        renderItem={renderFavoriteItem}
        keyExtractor={(item) => item.id}
        contentContainerStyle={[
          styles.listContainer,
          favorites.length === 0 && styles.emptyListContainer,
        ]}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={isLoading}
            onRefresh={refreshFavorites}
            colors={['#e6034b']}
            tintColor="#e6034b"
          />
        }
        ListEmptyComponent={!isLoading ? renderEmptyState : null}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FAFAFA',
  },
  header: {
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 24,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#212121',
  },
  favoritesCount: {
    fontSize: 14,
    color: '#757575',
  },
  listContainer: {
    padding: 16,
  },
  emptyListContainer: {
    flex: 1,
    justifyContent: 'center',
  },
  favoriteItem: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    overflow: 'hidden',
  },
  restaurantImage: {
    width: '100%',
    height: 160,
  },
  restaurantInfo: {
    padding: 16,
  },
  restaurantHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  restaurantName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#212121',
    flex: 1,
    marginRight: 12,
  },
  removeButton: {
    padding: 4,
  },
  restaurantDescription: {
    fontSize: 14,
    color: '#757575',
    lineHeight: 20,
    marginBottom: 12,
  },
  restaurantMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 16,
  },
  rating: {
    fontSize: 12,
    color: '#757575',
    marginLeft: 4,
  },
  priceContainer: {
    marginRight: 16,
  },
  priceRange: {
    fontSize: 12,
    color: '#4CAF50',
    fontWeight: 'bold',
  },
  locationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  location: {
    fontSize: 12,
    color: '#757575',
    marginLeft: 4,
  },
  addedDate: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  addedDateText: {
    fontSize: 11,
    color: '#999',
    marginLeft: 4,
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 32,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#212121',
    marginTop: 16,
    marginBottom: 8,
    textAlign: 'center',
  },
  emptyDescription: {
    fontSize: 14,
    color: '#757575',
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: 24,
  },
  exploreButton: {
    backgroundColor: '#e6034b',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  exploreButtonText: {
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
}); 