import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  StyleSheet,
  Alert,
  RefreshControl,
  Image,
  Platform,
  Modal,
  Switch,
  Animated,
  Dimensions
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';

import { GlobalText } from '../components/GlobalText';
import { useNotifications } from '../contexts/NotificationsContext';
import { PushNotification, NotificationSettings } from '../lib/api';

type RootStackParamList = {
  Notifications: undefined;
  LiveOrderTracking: { orderId: string };
  OrderHistory: undefined;
  Restaurants: undefined;
};

type NotificationsScreenNavigationProp = StackNavigationProp<RootStackParamList, 'Notifications'>;

const { width } = Dimensions.get('window');

export const NotificationsScreen: React.FC = () => {
  const navigation = useNavigation<NotificationsScreenNavigationProp>();
  const { 
    state, 
    loadNotifications, 
    markAsRead, 
    markAllAsRead, 
    deleteNotification,
    loadSettings,
    updateSettings
  } = useNotifications();

  const [activeFilter, setActiveFilter] = useState<'all' | PushNotification['type']>('all');
  const [showSettings, setShowSettings] = useState(false);
  const [settingsLoading, setSettingsLoading] = useState(false);

  useEffect(() => {
    loadNotifications();
    loadSettings();
  }, []);

  const filters = [
    { key: 'all', label: 'همه', icon: 'notifications-outline' },
    { key: 'order_update', label: 'سفارشات', icon: 'bag-outline' },
    { key: 'promotion', label: 'تخفیف‌ها', icon: 'pricetag-outline' },
    { key: 'reminder', label: 'یادآوری', icon: 'alarm-outline' },
    { key: 'system', label: 'سیستم', icon: 'settings-outline' },
  ] as const;

  const filteredNotifications = activeFilter === 'all' 
    ? state.notifications 
    : state.notifications.filter(n => n.type === activeFilter);

  const onRefresh = useCallback(async () => {
    await loadNotifications();
  }, [loadNotifications]);

  const handleNotificationPress = useCallback(async (notification: PushNotification) => {
    // Mark as read if unread
    if (!notification.read) {
      await markAsRead(notification.id);
    }

    // Handle navigation based on notification type and data
    if (notification.data) {
      switch (notification.type) {
        case 'order_update':
          if (notification.data.order_id) {
            navigation.navigate('LiveOrderTracking', { 
              orderId: notification.data.order_id 
            });
          }
          break;
        case 'promotion':
          // Navigate to restaurants or specific promotion
          navigation.navigate('Restaurants');
          break;
        case 'reminder':
          // Navigate to explore or order history
          navigation.navigate('OrderHistory');
          break;
        default:
          // Handle other types or show details
          break;
      }
    }
  }, [markAsRead, navigation]);

  const handleDeleteNotification = useCallback((notificationId: string) => {
    Alert.alert(
      'حذف اعلان',
      'آیا مطمئن هستید که می‌خواهید این اعلان را حذف کنید؟',
      [
        { text: 'انصراف', style: 'cancel' },
        { 
          text: 'حذف', 
          style: 'destructive',
          onPress: () => deleteNotification(notificationId)
        }
      ]
    );
  }, [deleteNotification]);

  const handleMarkAllAsRead = useCallback(() => {
    if (state.unreadCount === 0) return;
    
    Alert.alert(
      'علامت‌گذاری همه به عنوان خوانده شده',
      'آیا می‌خواهید همه اعلان‌ها را به عنوان خوانده شده علامت‌گذاری کنید؟',
      [
        { text: 'انصراف', style: 'cancel' },
        { text: 'تأیید', onPress: markAllAsRead }
      ]
    );
  }, [state.unreadCount, markAllAsRead]);

  const getNotificationIcon = (type: PushNotification['type']) => {
    switch (type) {
      case 'order_update': return 'bag-outline';
      case 'promotion': return 'pricetag-outline';
      case 'reminder': return 'alarm-outline';
      case 'marketing': return 'megaphone-outline';
      case 'system': return 'settings-outline';
      default: return 'notifications-outline';
    }
  };

  const getNotificationColor = (type: PushNotification['type'], priority: PushNotification['priority']) => {
    if (priority === 'high') return '#EF4444';
    
    switch (type) {
      case 'order_update': return '#e6034b';
      case 'promotion': return '#F59E0B';
      case 'reminder': return '#3B82F6';
      case 'marketing': return '#8B5CF6';
      case 'system': return '#6B7280';
      default: return '#9CA3AF';
    }
  };

  const formatNotificationTime = (timestamp: string) => {
    const now = new Date();
    const notificationTime = new Date(timestamp);
    const diffMinutes = Math.floor((now.getTime() - notificationTime.getTime()) / (1000 * 60));

    if (diffMinutes < 1) return 'همین الان';
    if (diffMinutes < 60) return `${diffMinutes} دقیقه پیش`;
    
    const diffHours = Math.floor(diffMinutes / 60);
    if (diffHours < 24) return `${diffHours} ساعت پیش`;
    
    const diffDays = Math.floor(diffHours / 24);
    if (diffDays < 7) return `${diffDays} روز پیش`;
    
    return notificationTime.toLocaleDateString('fa-IR');
  };

  const handleSettingChange = useCallback(async (setting: keyof NotificationSettings, value: boolean) => {
    setSettingsLoading(true);
    try {
      await updateSettings({ [setting]: value });
    } catch (error) {
      Alert.alert('خطا', 'خطا در به‌روزرسانی تنظیمات');
    } finally {
      setSettingsLoading(false);
    }
  }, [updateSettings]);

  const renderFilterButton = ({ item }: { item: typeof filters[0] }) => {
    const isActive = activeFilter === item.key;
    const notificationCount = item.key === 'all' 
      ? state.notifications.length 
      : state.notifications.filter(n => n.type === item.key).length;

    return (
      <TouchableOpacity
        style={[
          styles.filterButton,
          { backgroundColor: isActive ? '#e6034b' : '#FFFFFF' }
        ]}
        onPress={() => setActiveFilter(item.key)}
      >
        <Ionicons 
          name={item.icon as any} 
          size={20} 
          color={isActive ? '#FFFFFF' : '#6B7280'} 
        />
        <GlobalText 
          variant="body" 
          style={[
            styles.filterText,
            { color: isActive ? '#FFFFFF' : '#6B7280' }
          ]}
        >
          {item.label}
        </GlobalText>
        {notificationCount > 0 && (
          <View 
            style={[
              styles.filterBadge,
              { backgroundColor: isActive ? '#FFFFFF' : '#e6034b' }
            ]}
          >
            <GlobalText 
              variant="caption" 
              style={[
                styles.filterBadgeText,
                { color: isActive ? '#e6034b' : '#FFFFFF' }
              ]}
            >
              {notificationCount}
            </GlobalText>
          </View>
        )}
      </TouchableOpacity>
    );
  };

  const renderNotificationItem = ({ item }: { item: PushNotification }) => {
    const iconColor = getNotificationColor(item.type, item.priority);
    
    return (
      <Animated.View style={styles.notificationItem}>
        <TouchableOpacity
          style={[
            styles.notificationContent,
            { backgroundColor: item.read ? '#FFFFFF' : '#F3F4F6' }
          ]}
          onPress={() => handleNotificationPress(item)}
          activeOpacity={0.7}
        >
          {/* Icon */}
          <View style={[styles.notificationIcon, { backgroundColor: iconColor }]}>
            <Ionicons 
              name={getNotificationIcon(item.type) as any} 
              size={24} 
              color="#FFFFFF" 
            />
          </View>

          {/* Content */}
          <View style={styles.notificationBody}>
            <View style={styles.notificationHeader}>
              <GlobalText 
                variant="body" 
                style={[
                  styles.notificationTitle,
                  { fontWeight: item.read ? 'normal' : 'bold' }
                ]}
                numberOfLines={1}
              >
                {item.title}
              </GlobalText>
              <GlobalText variant="caption" style={styles.notificationTime}>
                {formatNotificationTime(item.created_at)}
              </GlobalText>
            </View>
            
            <GlobalText 
              variant="body" 
              style={styles.notificationMessage}
              numberOfLines={2}
            >
              {item.body}
            </GlobalText>

            {/* Image */}
            {item.image_url && (
              <Image 
                source={{ uri: item.image_url }} 
                style={styles.notificationImage}
                resizeMode="cover"
              />
            )}

            {/* Priority indicator */}
            {item.priority === 'high' && (
              <View style={styles.priorityIndicator}>
                <Ionicons name="flag" size={12} color="#EF4444" />
                <GlobalText variant="caption" style={styles.priorityText}>
                  فوری
                </GlobalText>
              </View>
            )}
          </View>

          {/* Actions */}
          <View style={styles.notificationActions}>
            {!item.read && <View style={styles.unreadDot} />}
            <TouchableOpacity
              style={styles.deleteButton}
              onPress={() => handleDeleteNotification(item.id)}
            >
              <Ionicons name="trash-outline" size={20} color="#EF4444" />
            </TouchableOpacity>
          </View>
        </TouchableOpacity>
      </Animated.View>
    );
  };

  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <Ionicons name="notifications-off-outline" size={64} color="#9CA3AF" />
      <GlobalText variant="heading" style={styles.emptyTitle}>
        اعلانی موجود نیست
      </GlobalText>
      <GlobalText variant="body" style={styles.emptyDescription}>
        {activeFilter === 'all' 
          ? 'هنوز اعلانی دریافت نکرده‌اید'
          : `اعلانی از نوع ${filters.find(f => f.key === activeFilter)?.label} وجود ندارد`
        }
      </GlobalText>
    </View>
  );

  const renderSettingsModal = () => (
    <Modal
      visible={showSettings}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={() => setShowSettings(false)}
    >
      <SafeAreaView style={styles.settingsContainer} edges={['top', 'left', 'right']}>
        {/* Settings Header */}
        <View style={styles.settingsHeader}>
          <TouchableOpacity
            style={styles.settingsCloseButton}
            onPress={() => setShowSettings(false)}
          >
            <Ionicons name="close" size={24} color="#374151" />
          </TouchableOpacity>
          <GlobalText variant="heading" style={styles.settingsTitle}>
            تنظیمات اعلان‌ها
          </GlobalText>
          <View style={styles.settingsCloseButton} />
        </View>

        {/* Settings List */}
        <View style={styles.settingsList}>
          <View style={styles.settingsSection}>
            <GlobalText variant="heading" style={styles.sectionTitle}>
              انواع اعلان‌ها
            </GlobalText>
            
            <View style={styles.settingItem}>
              <View style={styles.settingInfo}>
                <GlobalText variant="body" style={styles.settingLabel}>
                  به‌روزرسانی سفارشات
                </GlobalText>
                <GlobalText variant="caption" style={styles.settingDescription}>
                  اعلان‌های مربوط به وضعیت سفارش
                </GlobalText>
              </View>
              <Switch
                value={state.settings.orderUpdates}
                onValueChange={(value) => handleSettingChange('orderUpdates', value)}
                disabled={settingsLoading}
                trackColor={{ false: '#E5E7EB', true: '#e6034b' }}
                thumbColor="#FFFFFF"
              />
            </View>

            <View style={styles.settingItem}>
              <View style={styles.settingInfo}>
                <GlobalText variant="body" style={styles.settingLabel}>
                  تخفیف‌ها و پیشنهادات
                </GlobalText>
                <GlobalText variant="caption" style={styles.settingDescription}>
                  اعلان‌های مربوط به تخفیف‌ها و پیشنهادات ویژه
                </GlobalText>
              </View>
              <Switch
                value={state.settings.promotions}
                onValueChange={(value) => handleSettingChange('promotions', value)}
                disabled={settingsLoading}
                trackColor={{ false: '#E5E7EB', true: '#e6034b' }}
                thumbColor="#FFFFFF"
              />
            </View>

            <View style={styles.settingItem}>
              <View style={styles.settingInfo}>
                <GlobalText variant="body" style={styles.settingLabel}>
                  رستوران‌های جدید
                </GlobalText>
                <GlobalText variant="caption" style={styles.settingDescription}>
                  اعلان‌های مربوط به رستوران‌های جدید
                </GlobalText>
              </View>
              <Switch
                value={state.settings.newRestaurants}
                onValueChange={(value) => handleSettingChange('newRestaurants', value)}
                disabled={settingsLoading}
                trackColor={{ false: '#E5E7EB', true: '#e6034b' }}
                thumbColor="#FFFFFF"
              />
            </View>

            <View style={styles.settingItem}>
              <View style={styles.settingInfo}>
                <GlobalText variant="body" style={styles.settingLabel}>
                  یادآوری‌ها
                </GlobalText>
                <GlobalText variant="caption" style={styles.settingDescription}>
                  یادآوری‌های مربوط به سفارش
                </GlobalText>
              </View>
              <Switch
                value={state.settings.reminders}
                onValueChange={(value) => handleSettingChange('reminders', value)}
                disabled={settingsLoading}
                trackColor={{ false: '#E5E7EB', true: '#e6034b' }}
                thumbColor="#FFFFFF"
              />
            </View>

            <View style={styles.settingItem}>
              <View style={styles.settingInfo}>
                <GlobalText variant="body" style={styles.settingLabel}>
                  بازاریابی
                </GlobalText>
                <GlobalText variant="caption" style={styles.settingDescription}>
                  اعلان‌های بازاریابی و تبلیغاتی
                </GlobalText>
              </View>
              <Switch
                value={state.settings.marketing}
                onValueChange={(value) => handleSettingChange('marketing', value)}
                disabled={settingsLoading}
                trackColor={{ false: '#E5E7EB', true: '#e6034b' }}
                thumbColor="#FFFFFF"
              />
            </View>
          </View>

          <View style={styles.settingsSection}>
            <GlobalText variant="heading" style={styles.sectionTitle}>
              روش‌های ارسال
            </GlobalText>
            
            <View style={styles.settingItem}>
              <View style={styles.settingInfo}>
                <GlobalText variant="body" style={styles.settingLabel}>
                  اعلان‌های Push
                </GlobalText>
                <GlobalText variant="caption" style={styles.settingDescription}>
                  دریافت اعلان‌ها در برنامه
                </GlobalText>
              </View>
              <Switch
                value={state.settings.pushEnabled}
                onValueChange={(value) => handleSettingChange('pushEnabled', value)}
                disabled={settingsLoading}
                trackColor={{ false: '#E5E7EB', true: '#e6034b' }}
                thumbColor="#FFFFFF"
              />
            </View>

            <View style={styles.settingItem}>
              <View style={styles.settingInfo}>
                <GlobalText variant="body" style={styles.settingLabel}>
                  ایمیل
                </GlobalText>
                <GlobalText variant="caption" style={styles.settingDescription}>
                  دریافت اعلان‌ها از طریق ایمیل
                </GlobalText>
              </View>
              <Switch
                value={state.settings.emailEnabled}
                onValueChange={(value) => handleSettingChange('emailEnabled', value)}
                disabled={settingsLoading}
                trackColor={{ false: '#E5E7EB', true: '#e6034b' }}
                thumbColor="#FFFFFF"
              />
            </View>

            <View style={styles.settingItem}>
              <View style={styles.settingInfo}>
                <GlobalText variant="body" style={styles.settingLabel}>
                  پیامک
                </GlobalText>
                <GlobalText variant="caption" style={styles.settingDescription}>
                  دریافت اعلان‌ها از طریق پیامک
                </GlobalText>
              </View>
              <Switch
                value={state.settings.smsEnabled}
                onValueChange={(value) => handleSettingChange('smsEnabled', value)}
                disabled={settingsLoading}
                trackColor={{ false: '#E5E7EB', true: '#e6034b' }}
                thumbColor="#FFFFFF"
              />
            </View>
          </View>
        </View>
      </SafeAreaView>
    </Modal>
  );

  return (
    <SafeAreaView style={styles.container} edges={['top', 'left', 'right']}>
      {/* Header */}
      <View style={styles.header}>
        <GlobalText variant="heading" style={styles.headerTitle}>
          اعلان‌ها
        </GlobalText>
        <View style={styles.headerActions}>
          {state.unreadCount > 0 && (
            <TouchableOpacity
              style={styles.markAllButton}
              onPress={handleMarkAllAsRead}
            >
              <Ionicons name="checkmark-done" size={20} color="#e6034b" />
            </TouchableOpacity>
          )}
          <TouchableOpacity
            style={styles.settingsButton}
            onPress={() => setShowSettings(true)}
          >
            <Ionicons name="settings-outline" size={20} color="#e6034b" />
          </TouchableOpacity>
        </View>
      </View>

      {/* Unread count indicator */}
      {state.unreadCount > 0 && (
        <View style={styles.unreadIndicator}>
          <Ionicons name="notifications" size={16} color="#e6034b" />
          <GlobalText variant="caption" style={styles.unreadText}>
            {state.unreadCount} اعلان خوانده نشده
          </GlobalText>
        </View>
      )}

      {/* Filters */}
      <FlatList
        data={filters}
        renderItem={renderFilterButton}
        keyExtractor={(item) => item.key}
        horizontal
        showsHorizontalScrollIndicator={false}
        style={styles.filtersContainer}
        contentContainerStyle={styles.filtersContent}
      />

      {/* Notifications List */}
      <FlatList
        data={filteredNotifications}
        renderItem={renderNotificationItem}
        keyExtractor={(item) => item.id}
        style={styles.notificationsList}
        contentContainerStyle={styles.notificationsContent}
        refreshControl={
          <RefreshControl
            refreshing={state.isLoading}
            onRefresh={onRefresh}
            colors={['#e6034b']}
          />
        }
        showsVerticalScrollIndicator={false}
        ListEmptyComponent={renderEmptyState}
      />

      {/* Settings Modal */}
      {renderSettingsModal()}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#374151',
  },
  headerActions: {
    flexDirection: 'row',
    gap: 12,
  },
  markAllButton: {
    padding: 8,
  },
  settingsButton: {
    padding: 8,
  },
  unreadIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: '#FEF3F2',
    gap: 8,
  },
  unreadText: {
    color: '#e6034b',
    fontWeight: '600',
  },
  filtersContainer: {
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  filtersContent: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    gap: 8,
  },
  filterButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: '#E5E7EB',
    gap: 6,
  },
  filterText: {
    fontSize: 14,
    fontWeight: '500',
  },
  filterBadge: {
    minWidth: 20,
    height: 20,
    borderRadius: 10,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 6,
  },
  filterBadgeText: {
    fontSize: 12,
    fontWeight: 'bold',
  },
  notificationsList: {
    flex: 1,
  },
  notificationsContent: {
    padding: 16,
    gap: 12,
  },
  notificationItem: {
    borderRadius: 12,
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      },
      android: {
        elevation: 2,
      },
    }),
  },
  notificationContent: {
    flexDirection: 'row',
    padding: 16,
    borderRadius: 12,
  },
  notificationIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  notificationBody: {
    flex: 1,
  },
  notificationHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    justifyContent: 'space-between',
    marginBottom: 4,
  },
  notificationTitle: {
    flex: 1,
    fontSize: 16,
    color: '#374151',
    marginRight: 8,
  },
  notificationTime: {
    color: '#9CA3AF',
    fontSize: 12,
  },
  notificationMessage: {
    color: '#6B7280',
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 8,
  },
  notificationImage: {
    width: '100%',
    height: 120,
    borderRadius: 8,
    marginBottom: 8,
  },
  priorityIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  priorityText: {
    color: '#EF4444',
    fontSize: 12,
    fontWeight: '600',
  },
  notificationActions: {
    alignItems: 'center',
    gap: 8,
  },
  unreadDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#e6034b',
  },
  deleteButton: {
    padding: 4,
  },
  emptyContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 32,
    paddingVertical: 64,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#374151',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyDescription: {
    color: '#9CA3AF',
    textAlign: 'center',
  },
  settingsContainer: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  settingsHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  settingsCloseButton: {
    padding: 8,
    width: 40,
  },
  settingsTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#374151',
  },
  settingsList: {
    flex: 1,
    padding: 16,
  },
  settingsSection: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      },
      android: {
        elevation: 2,
      },
    }),
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#374151',
    marginBottom: 16,
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
  },
  settingInfo: {
    flex: 1,
    marginRight: 16,
  },
  settingLabel: {
    fontSize: 16,
    color: '#374151',
    marginBottom: 2,
  },
  settingDescription: {
    color: '#9CA3AF',
    fontSize: 14,
  },
}); 