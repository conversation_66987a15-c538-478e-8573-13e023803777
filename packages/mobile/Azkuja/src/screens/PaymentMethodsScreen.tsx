import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Alert,
  Modal,
  TextInput,
  ScrollView,
  ActivityIndicator,
  RefreshControl,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { SafeAreaView } from 'react-native-safe-area-context';
import { usePayment } from '../contexts/PaymentContext';
import { PaymentMethod } from '../lib/api';

interface PaymentMethodsScreenProps {
  navigation: any;
}

const PaymentMethodsScreen: React.FC<PaymentMethodsScreenProps> = ({ navigation }) => {
  const {
    paymentMethods,
    defaultPaymentMethod,
    isLoadingMethods,
    addPaymentMethod,
    updatePaymentMethod,
    deletePaymentMethod,
    setDefaultPaymentMethod,
    loadPaymentMethods,
  } = usePayment();

  const [isAddModalVisible, setIsAddModalVisible] = useState(false);
  const [isEditModalVisible, setIsEditModalVisible] = useState(false);
  const [selectedMethod, setSelectedMethod] = useState<PaymentMethod | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Form state
  const [formData, setFormData] = useState({
    type: 'credit_card' as PaymentMethod['type'],
    display_name: '',
    description: '',
    card_number: '',
    card_holder_name: '',
    expiry_date: '',
    card_type: 'visa' as PaymentMethod['card_type'],
    wallet_provider: '',
    bank_name: '',
    account_number: '',
  });

  const resetForm = () => {
    setFormData({
      type: 'credit_card',
      display_name: '',
      description: '',
      card_number: '',
      card_holder_name: '',
      expiry_date: '',
      card_type: 'visa',
      wallet_provider: '',
      bank_name: '',
      account_number: '',
    });
  };

  const handleAddMethod = () => {
    resetForm();
    setIsAddModalVisible(true);
  };

  const handleEditMethod = (method: PaymentMethod) => {
    setSelectedMethod(method);
    setFormData({
      type: method.type,
      display_name: method.display_name,
      description: method.description || '',
      card_number: method.card_number || '',
      card_holder_name: method.card_holder_name || '',
      expiry_date: method.expiry_date || '',
      card_type: method.card_type || 'visa',
      wallet_provider: method.wallet_provider || '',
      bank_name: method.bank_name || '',
      account_number: method.account_number || '',
    });
    setIsEditModalVisible(true);
  };

  const handleDeleteMethod = (method: PaymentMethod) => {
    Alert.alert(
      'حذف روش پرداخت',
      `آیا از حذف ${method.display_name} اطمینان دارید؟`,
      [
        { text: 'لغو', style: 'cancel' },
        {
          text: 'حذف',
          style: 'destructive',
          onPress: async () => {
            try {
              await deletePaymentMethod(method.id);
              Alert.alert('موفق', 'روش پرداخت حذف شد');
            } catch (error) {
              // Error already handled in context
            }
          },
        },
      ]
    );
  };

  const handleSetDefault = async (method: PaymentMethod) => {
    try {
      await setDefaultPaymentMethod(method.id);
      Alert.alert('موفق', 'روش پرداخت پیش‌فرض تغییر کرد');
    } catch (error) {
      // Error already handled in context
    }
  };

  const handleSubmit = async () => {
    if (!formData.display_name.trim()) {
      Alert.alert('خطا', 'لطفاً نام روش پرداخت را وارد کنید');
      return;
    }

    setIsSubmitting(true);
    try {
      const methodData = {
        type: formData.type,
        name: formData.type,
        display_name: formData.display_name,
        is_default: false,
        is_active: true,
        icon: getPaymentMethodIcon(formData.type),
        description: formData.description,
        ...(formData.type === 'credit_card' && {
          card_number: formData.card_number,
          card_holder_name: formData.card_holder_name,
          expiry_date: formData.expiry_date,
          card_type: formData.card_type,
        }),
        ...(formData.type === 'mobile_wallet' && {
          wallet_provider: formData.wallet_provider,
        }),
        ...(formData.type === 'bank_transfer' && {
          bank_name: formData.bank_name,
          account_number: formData.account_number,
        }),
      };

      if (selectedMethod) {
        await updatePaymentMethod(selectedMethod.id, methodData);
        Alert.alert('موفق', 'روش پرداخت به‌روزرسانی شد');
        setIsEditModalVisible(false);
      } else {
        await addPaymentMethod(methodData);
        Alert.alert('موفق', 'روش پرداخت اضافه شد');
        setIsAddModalVisible(false);
      }

      resetForm();
      setSelectedMethod(null);
    } catch (error) {
      // Error already handled in context
    } finally {
      setIsSubmitting(false);
    }
  };

  const getPaymentMethodIcon = (type: PaymentMethod['type']) => {
    switch (type) {
      case 'cash':
        return '💵';
      case 'credit_card':
      case 'debit_card':
        return '💳';
      case 'mobile_wallet':
        return '📱';
      case 'bank_transfer':
        return '🏦';
      default:
        return '💳';
    }
  };

  const getPaymentMethodColor = (type: PaymentMethod['type']) => {
    switch (type) {
      case 'cash':
        return '#10B981';
      case 'credit_card':
      case 'debit_card':
        return '#3B82F6';
      case 'mobile_wallet':
        return '#8B5CF6';
      case 'bank_transfer':
        return '#F59E0B';
      default:
        return '#6B7280';
    }
  };

  const renderPaymentMethod = ({ item }: { item: PaymentMethod }) => (
    <View style={styles.methodCard}>
      <View style={styles.methodHeader}>
        <View style={styles.methodIcon}>
          <Text style={styles.methodIconText}>{item.icon}</Text>
        </View>
        <View style={styles.methodInfo}>
          <Text style={styles.methodName}>{item.display_name}</Text>
          {item.description && (
            <Text style={styles.methodDescription}>{item.description}</Text>
          )}
          {item.card_number && (
            <Text style={styles.methodDetails}>کارت: {item.card_number}</Text>
          )}
          {item.wallet_provider && (
            <Text style={styles.methodDetails}>ارائه‌دهنده: {item.wallet_provider}</Text>
          )}
          {item.bank_name && (
            <Text style={styles.methodDetails}>بانک: {item.bank_name}</Text>
          )}
        </View>
        <View style={styles.methodActions}>
          {item.is_default && (
            <View style={styles.defaultBadge}>
              <Text style={styles.defaultBadgeText}>پیش‌فرض</Text>
            </View>
          )}
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => handleEditMethod(item)}
          >
            <Ionicons name="pencil" size={16} color="#6B7280" />
          </TouchableOpacity>
        </View>
      </View>

      <View style={styles.methodFooter}>
        <TouchableOpacity
          style={[styles.footerButton, styles.deleteButton]}
          onPress={() => handleDeleteMethod(item)}
        >
          <Ionicons name="trash-outline" size={16} color="#EF4444" />
          <Text style={styles.deleteButtonText}>حذف</Text>
        </TouchableOpacity>
        
        {!item.is_default && (
          <TouchableOpacity
            style={[styles.footerButton, styles.defaultButton]}
            onPress={() => handleSetDefault(item)}
          >
            <Ionicons name="checkmark-circle-outline" size={16} color="#10B981" />
            <Text style={styles.defaultButtonText}>پیش‌فرض</Text>
          </TouchableOpacity>
        )}
      </View>
    </View>
  );

  const renderForm = () => (
    <ScrollView style={styles.form} showsVerticalScrollIndicator={false}>
      <View style={styles.formGroup}>
        <Text style={styles.formLabel}>نوع روش پرداخت</Text>
        <View style={styles.typeSelector}>
          {[
            { value: 'credit_card', label: 'کارت اعتباری', icon: '💳' },
            { value: 'debit_card', label: 'کارت نقدی', icon: '💳' },
            { value: 'mobile_wallet', label: 'کیف پول موبایل', icon: '📱' },
            { value: 'bank_transfer', label: 'انتقال بانکی', icon: '🏦' },
          ].map((type) => (
            <TouchableOpacity
              key={type.value}
              style={[
                styles.typeOption,
                formData.type === type.value && styles.typeOptionSelected,
              ]}
              onPress={() => setFormData({ ...formData, type: type.value as PaymentMethod['type'] })}
            >
              <Text style={styles.typeOptionIcon}>{type.icon}</Text>
              <Text style={[
                styles.typeOptionText,
                formData.type === type.value && styles.typeOptionTextSelected,
              ]}>
                {type.label}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>

      <View style={styles.formGroup}>
        <Text style={styles.formLabel}>نام روش پرداخت</Text>
        <TextInput
          style={styles.formInput}
          value={formData.display_name}
          onChangeText={(text) => setFormData({ ...formData, display_name: text })}
          placeholder="مثال: کارت اصلی من"
          placeholderTextColor="#9CA3AF"
        />
      </View>

      <View style={styles.formGroup}>
        <Text style={styles.formLabel}>توضیحات (اختیاری)</Text>
        <TextInput
          style={styles.formInput}
          value={formData.description}
          onChangeText={(text) => setFormData({ ...formData, description: text })}
          placeholder="توضیحات اضافی"
          placeholderTextColor="#9CA3AF"
          multiline
          numberOfLines={2}
        />
      </View>

      {(formData.type === 'credit_card' || formData.type === 'debit_card') && (
        <>
          <View style={styles.formGroup}>
            <Text style={styles.formLabel}>شماره کارت</Text>
            <TextInput
              style={styles.formInput}
              value={formData.card_number}
              onChangeText={(text) => setFormData({ ...formData, card_number: text })}
              placeholder="**** **** **** ****"
              placeholderTextColor="#9CA3AF"
              keyboardType="numeric"
            />
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.formLabel}>نام دارنده کارت</Text>
            <TextInput
              style={styles.formInput}
              value={formData.card_holder_name}
              onChangeText={(text) => setFormData({ ...formData, card_holder_name: text })}
              placeholder="نام کامل"
              placeholderTextColor="#9CA3AF"
            />
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.formLabel}>تاریخ انقضا</Text>
            <TextInput
              style={styles.formInput}
              value={formData.expiry_date}
              onChangeText={(text) => setFormData({ ...formData, expiry_date: text })}
              placeholder="MM/YY"
              placeholderTextColor="#9CA3AF"
              keyboardType="numeric"
            />
          </View>
        </>
      )}

      {formData.type === 'mobile_wallet' && (
        <View style={styles.formGroup}>
          <Text style={styles.formLabel}>ارائه‌دهنده کیف پول</Text>
          <TextInput
            style={styles.formInput}
            value={formData.wallet_provider}
            onChangeText={(text) => setFormData({ ...formData, wallet_provider: text })}
            placeholder="مثال: Apple Pay، Google Pay"
            placeholderTextColor="#9CA3AF"
          />
        </View>
      )}

      {formData.type === 'bank_transfer' && (
        <>
          <View style={styles.formGroup}>
            <Text style={styles.formLabel}>نام بانک</Text>
            <TextInput
              style={styles.formInput}
              value={formData.bank_name}
              onChangeText={(text) => setFormData({ ...formData, bank_name: text })}
              placeholder="نام بانک"
              placeholderTextColor="#9CA3AF"
            />
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.formLabel}>شماره حساب</Text>
            <TextInput
              style={styles.formInput}
              value={formData.account_number}
              onChangeText={(text) => setFormData({ ...formData, account_number: text })}
              placeholder="شماره حساب"
              placeholderTextColor="#9CA3AF"
              keyboardType="numeric"
            />
          </View>
        </>
      )}
    </ScrollView>
  );

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color="#1F2937" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>روش‌های پرداخت</Text>
        <TouchableOpacity
          style={styles.addButton}
          onPress={handleAddMethod}
        >
          <Ionicons name="add" size={24} color="#3B82F6" />
        </TouchableOpacity>
      </View>

      <FlatList
        data={paymentMethods}
        renderItem={renderPaymentMethod}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.listContainer}
        refreshControl={
          <RefreshControl
            refreshing={isLoadingMethods}
            onRefresh={loadPaymentMethods}
            colors={['#3B82F6']}
          />
        }
        ListEmptyComponent={
          <View style={styles.emptyState}>
            <Text style={styles.emptyStateIcon}>💳</Text>
            <Text style={styles.emptyStateTitle}>هیچ روش پرداختی وجود ندارد</Text>
            <Text style={styles.emptyStateDescription}>
              برای شروع، یک روش پرداخت اضافه کنید
            </Text>
            <TouchableOpacity
              style={styles.emptyStateButton}
              onPress={handleAddMethod}
            >
              <Text style={styles.emptyStateButtonText}>افزودن روش پرداخت</Text>
            </TouchableOpacity>
          </View>
        }
      />

      {/* Add Method Modal */}
      <Modal
        visible={isAddModalVisible}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <SafeAreaView style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <TouchableOpacity
              style={styles.modalCloseButton}
              onPress={() => setIsAddModalVisible(false)}
            >
              <Text style={styles.modalCloseButtonText}>لغو</Text>
            </TouchableOpacity>
            <Text style={styles.modalTitle}>افزودن روش پرداخت</Text>
            <TouchableOpacity
              style={styles.modalSaveButton}
              onPress={handleSubmit}
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <ActivityIndicator size="small" color="#3B82F6" />
              ) : (
                <Text style={styles.modalSaveButtonText}>ذخیره</Text>
              )}
            </TouchableOpacity>
          </View>
          {renderForm()}
        </SafeAreaView>
      </Modal>

      {/* Edit Method Modal */}
      <Modal
        visible={isEditModalVisible}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <SafeAreaView style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <TouchableOpacity
              style={styles.modalCloseButton}
              onPress={() => setIsEditModalVisible(false)}
            >
              <Text style={styles.modalCloseButtonText}>لغو</Text>
            </TouchableOpacity>
            <Text style={styles.modalTitle}>ویرایش روش پرداخت</Text>
            <TouchableOpacity
              style={styles.modalSaveButton}
              onPress={handleSubmit}
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <ActivityIndicator size="small" color="#3B82F6" />
              ) : (
                <Text style={styles.modalSaveButtonText}>ذخیره</Text>
              )}
            </TouchableOpacity>
          </View>
          {renderForm()}
        </SafeAreaView>
      </Modal>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#F3F4F6',
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937',
  },
  addButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#EBF4FF',
    alignItems: 'center',
    justifyContent: 'center',
  },
  listContainer: {
    padding: 20,
  },
  methodCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  methodHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  methodIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#F3F4F6',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  methodIconText: {
    fontSize: 20,
  },
  methodInfo: {
    flex: 1,
  },
  methodName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 4,
  },
  methodDescription: {
    fontSize: 14,
    color: '#6B7280',
    marginBottom: 4,
  },
  methodDetails: {
    fontSize: 12,
    color: '#9CA3AF',
  },
  methodActions: {
    alignItems: 'flex-end',
  },
  defaultBadge: {
    backgroundColor: '#10B981',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginBottom: 8,
  },
  defaultBadgeText: {
    fontSize: 12,
    color: '#FFFFFF',
    fontWeight: '500',
  },
  actionButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#F3F4F6',
    alignItems: 'center',
    justifyContent: 'center',
  },
  methodFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
  },
  footerButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
  },
  deleteButton: {
    backgroundColor: '#FEF2F2',
  },
  deleteButtonText: {
    fontSize: 14,
    color: '#EF4444',
    marginLeft: 4,
    fontWeight: '500',
  },
  defaultButton: {
    backgroundColor: '#F0FDF4',
  },
  defaultButtonText: {
    fontSize: 14,
    color: '#10B981',
    marginLeft: 4,
    fontWeight: '500',
  },
  emptyState: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 60,
  },
  emptyStateIcon: {
    fontSize: 48,
    marginBottom: 16,
  },
  emptyStateTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 8,
  },
  emptyStateDescription: {
    fontSize: 14,
    color: '#6B7280',
    textAlign: 'center',
    marginBottom: 24,
  },
  emptyStateButton: {
    backgroundColor: '#3B82F6',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  emptyStateButtonText: {
    fontSize: 16,
    color: '#FFFFFF',
    fontWeight: '500',
  },
  modalContainer: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  modalCloseButton: {
    paddingVertical: 8,
  },
  modalCloseButtonText: {
    fontSize: 16,
    color: '#6B7280',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937',
  },
  modalSaveButton: {
    paddingVertical: 8,
  },
  modalSaveButtonText: {
    fontSize: 16,
    color: '#3B82F6',
    fontWeight: '500',
  },
  form: {
    flex: 1,
    padding: 20,
  },
  formGroup: {
    marginBottom: 20,
  },
  formLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: '#1F2937',
    marginBottom: 8,
  },
  formInput: {
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
    fontSize: 16,
    color: '#1F2937',
    backgroundColor: '#FFFFFF',
  },
  typeSelector: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 8,
  },
  typeOption: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#D1D5DB',
    marginRight: 8,
    marginBottom: 8,
    backgroundColor: '#FFFFFF',
  },
  typeOptionSelected: {
    borderColor: '#3B82F6',
    backgroundColor: '#EBF4FF',
  },
  typeOptionIcon: {
    fontSize: 16,
    marginRight: 6,
  },
  typeOptionText: {
    fontSize: 14,
    color: '#6B7280',
  },
  typeOptionTextSelected: {
    color: '#3B82F6',
    fontWeight: '500',
  },
});

export default PaymentMethodsScreen; 