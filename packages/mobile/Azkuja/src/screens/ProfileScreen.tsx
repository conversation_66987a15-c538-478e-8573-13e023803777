import React, { useState } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  Modal,
  TextInput,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

import { GlobalText } from '../components/GlobalText';
import { useAuth } from '../contexts/AuthContext';
import { useTranslation } from '../i18n';
import apiService from '../lib/api';

interface ProfileScreenProps {
  navigation?: any;
}

export const ProfileScreen: React.FC<ProfileScreenProps> = ({ navigation }) => {
  const { user, logout, refreshUser } = useAuth();
  const { t } = useTranslation();
  const insets = useSafeAreaInsets();
  
  const [isEditModalVisible, setIsEditModalVisible] = useState(false);
  const [editForm, setEditForm] = useState({
    name: user?.name || '',
    email: user?.email || '',
  });

  const handleLogout = () => {
    Alert.alert(
      'تأیید خروج',
      'آیا مطمئن هستید که می‌خواهید خارج شوید؟',
      [
        { text: 'لغو', style: 'cancel' },
        { 
          text: 'خروج', 
          style: 'destructive',
          onPress: logout 
        },
      ]
    );
  };

  const handleEditProfile = () => {
    setEditForm({
      name: user?.name || '',
      email: user?.email || '',
    });
    setIsEditModalVisible(true);
  };

  const handleSaveProfile = async () => {
    try {
      console.log('Saving profile:', editForm);
      
      // Validate form data
      if (!editForm.name.trim()) {
        Alert.alert('خطا', 'نام الزامی است');
        return;
      }

      // Call API to update profile
      const updatedUser = await apiService.updateProfile({
        name: editForm.name.trim(),
        email: editForm.email.trim() || undefined,
      });

      console.log('Profile updated successfully:', updatedUser);
      
      // Close the modal
      setIsEditModalVisible(false);
      
      // Refresh user data
      await refreshUser();
      
      Alert.alert('موفقیت', 'اطلاعات پروفایل با موفقیت به‌روزرسانی شد');
    } catch (error) {
      console.error('Error updating profile:', error);
      Alert.alert('خطا', 'خطا در به‌روزرسانی اطلاعات. لطفاً دوباره تلاش کنید.');
    }
  };

  const ProfileItem = ({ 
    icon, 
    title, 
    value, 
    onPress, 
    showArrow = true 
  }: {
    icon: string;
    title: string;
    value?: string;
    onPress?: () => void;
    showArrow?: boolean;
  }) => (
    <TouchableOpacity 
      style={styles.profileItem}
      onPress={onPress}
      disabled={!onPress}
    >
      <View style={styles.profileItemLeft}>
        <Ionicons name={icon as any} size={24} color="#e6034b" />
        <View style={styles.profileItemText}>
          <GlobalText variant="body" style={styles.profileItemTitle}>
            {title}
          </GlobalText>
          {value && (
            <GlobalText variant="caption" style={styles.profileItemValue}>
              {value}
            </GlobalText>
          )}
        </View>
      </View>
      {showArrow && onPress && (
        <Ionicons name="chevron-forward" size={20} color="#757575" />
      )}
    </TouchableOpacity>
  );

  return (
    <View style={[styles.container, { paddingTop: insets.top }]}>
      {/* Header */}
      <View style={styles.header}>
        <GlobalText variant="heading" style={styles.headerTitle}>
          پروفایل
        </GlobalText>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* User Info Card */}
        <View style={styles.userCard}>
          <View style={styles.userAvatar}>
            <Ionicons name="person" size={48} color="#FFFFFF" />
          </View>
          <View style={styles.userInfo}>
            <GlobalText variant="heading" style={styles.userName}>
              {user?.name || 'کاربر'}
            </GlobalText>
            <GlobalText variant="body" style={styles.userPhone}>
              {user?.phone_number}
            </GlobalText>
            {user?.email && (
              <GlobalText variant="caption" style={styles.userEmail}>
                {user.email}
              </GlobalText>
            )}
          </View>
          <TouchableOpacity 
            style={styles.editButton}
            onPress={handleEditProfile}
          >
            <Ionicons name="create-outline" size={20} color="#e6034b" />
          </TouchableOpacity>
        </View>

        {/* Profile Sections */}
        <View style={styles.section}>
          <GlobalText variant="heading" style={styles.sectionTitle}>
            اطلاعات حساب
          </GlobalText>
          
          <ProfileItem
            icon="person-outline"
            title="ویرایش پروفایل"
            value="نام، ایمیل و سایر اطلاعات"
            onPress={handleEditProfile}
          />
          
          <ProfileItem
            icon="location-outline"
            title="آدرس‌های من"
            value="مدیریت آدرس‌های تحویل"
            onPress={() => {
              // TODO: Navigate to addresses screen
              Alert.alert('در حال توسعه', 'این قسمت به زودی اضافه خواهد شد');
            }}
          />
          
          <ProfileItem
            icon="card-outline"
            title="روش‌های پرداخت"
            value="کارت‌ها و کیف پول"
            onPress={() => {
              navigation?.navigate('PaymentMethods');
            }}
          />
          
          <ProfileItem
            icon="receipt-outline"
            title="تاریخچه پرداخت"
            value="مشاهده پرداخت‌های قبلی"
            onPress={() => {
              navigation?.navigate('PaymentHistory');
            }}
          />
        </View>

        <View style={styles.section}>
          <GlobalText variant="heading" style={styles.sectionTitle}>
            تنظیمات
          </GlobalText>
          
          <ProfileItem
            icon="notifications-outline"
            title="اعلان‌ها"
            value="مدیریت اعلان‌های دریافتی"
            onPress={() => {
              // TODO: Navigate to notifications settings
              Alert.alert('در حال توسعه', 'این قسمت به زودی اضافه خواهد شد');
            }}
          />
          
          <ProfileItem
            icon="language-outline"
            title="زبان"
            value="فارسی / پشتو / انگلیسی"
            onPress={() => {
              // TODO: Navigate to language settings
              Alert.alert('در حال توسعه', 'این قسمت به زودی اضافه خواهد شد');
            }}
          />
          
          <ProfileItem
            icon="help-circle-outline"
            title="راهنما و پشتیبانی"
            value="سوالات متداول، تماس با ما"
            onPress={() => {
              // TODO: Navigate to help screen
              Alert.alert('در حال توسعه', 'این قسمت به زودی اضافه خواهد شد');
            }}
          />
        </View>

        <View style={styles.section}>
          <GlobalText variant="heading" style={styles.sectionTitle}>
            حساب
          </GlobalText>
          
          <ProfileItem
            icon="information-circle-outline"
            title="درباره اسکوجا"
            value="نسخه 1.0.0"
            onPress={() => {
              // TODO: Navigate to about screen
              Alert.alert('درباره اسکوجا', 'نسخه 1.0.0\n\nپلتفرم سفارش غذا در افغانستان');
            }}
          />
          
          <TouchableOpacity 
            style={[styles.profileItem, styles.logoutItem]}
            onPress={handleLogout}
          >
            <View style={styles.profileItemLeft}>
              <Ionicons name="log-out-outline" size={24} color="#e53e3e" />
              <GlobalText variant="body" style={styles.logoutText}>
                خروج از حساب
              </GlobalText>
            </View>
            <Ionicons name="chevron-forward" size={20} color="#e53e3e" />
          </TouchableOpacity>
        </View>

        {/* Bottom spacing */}
        <View style={{ height: 32 }} />
      </ScrollView>

      {/* Edit Profile Modal */}
      <Modal
        visible={isEditModalVisible}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setIsEditModalVisible(false)}
      >
        <View style={[styles.modalContainer, { paddingTop: insets.top }]}>
          <View style={styles.modalHeader}>
            <TouchableOpacity
              onPress={() => setIsEditModalVisible(false)}
              style={styles.modalCloseButton}
            >
              <Ionicons name="close" size={24} color="#757575" />
            </TouchableOpacity>
            <GlobalText variant="heading" style={styles.modalTitle}>
              ویرایش پروفایل
            </GlobalText>
            <TouchableOpacity
              onPress={handleSaveProfile}
              style={styles.modalSaveButton}
            >
              <GlobalText variant="button" style={styles.modalSaveText}>
                ذخیره
              </GlobalText>
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.modalContent}>
            <View style={styles.formGroup}>
              <GlobalText variant="body" style={styles.formLabel}>
                نام و نام خانوادگی
              </GlobalText>
              <TextInput
                style={styles.formInput}
                value={editForm.name}
                onChangeText={(text) => setEditForm({ ...editForm, name: text })}
                placeholder="نام خود را وارد کنید"
                placeholderTextColor="#999"
              />
            </View>

            <View style={styles.formGroup}>
              <GlobalText variant="body" style={styles.formLabel}>
                ایمیل (اختیاری)
              </GlobalText>
              <TextInput
                style={styles.formInput}
                value={editForm.email}
                onChangeText={(text) => setEditForm({ ...editForm, email: text })}
                placeholder="ایمیل خود را وارد کنید"
                placeholderTextColor="#999"
                keyboardType="email-address"
                autoCapitalize="none"
              />
            </View>

            <View style={styles.formGroup}>
              <GlobalText variant="body" style={styles.formLabel}>
                شماره تلفن
              </GlobalText>
              <View style={[styles.formInput, styles.disabledInput]}>
                <GlobalText variant="body" style={styles.disabledText}>
                  {user?.phone_number}
                </GlobalText>
              </View>
              <GlobalText variant="caption" style={styles.formHint}>
                برای تغییر شماره تلفن با پشتیبانی تماس بگیرید
              </GlobalText>
            </View>
          </ScrollView>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FAFAFA',
  },
  header: {
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 24,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#212121',
    textAlign: 'center',
  },
  content: {
    flex: 1,
  },
  userCard: {
    backgroundColor: '#FFFFFF',
    marginHorizontal: 16,
    marginTop: 16,
    padding: 20,
    borderRadius: 12,
    flexDirection: 'row',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  userAvatar: {
    width: 64,
    height: 64,
    borderRadius: 32,
    backgroundColor: '#e6034b',
    justifyContent: 'center',
    alignItems: 'center',
  },
  userInfo: {
    flex: 1,
    marginLeft: 16,
  },
  userName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#212121',
    marginBottom: 4,
  },
  userPhone: {
    fontSize: 14,
    color: '#757575',
    marginBottom: 2,
  },
  userEmail: {
    fontSize: 12,
    color: '#999',
  },
  editButton: {
    padding: 8,
  },
  section: {
    marginTop: 24,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#212121',
    marginHorizontal: 16,
    marginBottom: 12,
  },
  profileItem: {
    backgroundColor: '#FFFFFF',
    marginHorizontal: 16,
    marginBottom: 1,
    paddingHorizontal: 20,
    paddingVertical: 16,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  profileItemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  profileItemText: {
    marginLeft: 16,
    flex: 1,
  },
  profileItemTitle: {
    fontSize: 16,
    color: '#212121',
    marginBottom: 2,
  },
  profileItemValue: {
    fontSize: 12,
    color: '#757575',
  },
  logoutItem: {
    marginTop: 8,
  },
  logoutText: {
    fontSize: 16,
    color: '#e53e3e',
    marginLeft: 16,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: '#FAFAFA',
  },
  modalHeader: {
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 16,
    paddingVertical: 16,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
  },
  modalCloseButton: {
    padding: 4,
    width: 60,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#212121',
    flex: 1,
    textAlign: 'center',
  },
  modalSaveButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    width: 60,
    alignItems: 'flex-end',
  },
  modalSaveText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#e6034b',
  },
  modalContent: {
    flex: 1,
    padding: 16,
  },
  formGroup: {
    marginBottom: 24,
  },
  formLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: '#212121',
    marginBottom: 8,
  },
  formInput: {
    backgroundColor: '#FFFFFF',
    borderWidth: 1,
    borderColor: '#E0E0E0',
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    color: '#212121',
    textAlign: 'right',
  },
  disabledInput: {
    backgroundColor: '#F5F5F5',
    borderColor: '#E0E0E0',
  },
  disabledText: {
    color: '#757575',
  },
  formHint: {
    fontSize: 12,
    color: '#757575',
    marginTop: 4,
    textAlign: 'right',
  },
}); 