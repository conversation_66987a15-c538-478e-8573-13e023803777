import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  StatusBar,
  KeyboardAvoidingView,
  Platform,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation, useRoute } from '@react-navigation/native';

import { GlobalText } from '../../components/GlobalText';
import { Button, Input } from '../../components/ui';
import { useTranslation } from '../../i18n';
import { useAuth } from '../../contexts/AuthContext';

interface OtpVerificationRouteParams {
  phoneNumber: string;
  message: string;
}

export const OtpVerificationScreen: React.FC = () => {
  console.log('🔐 OtpVerificationScreen: Component is being rendered');
  
  const { t, isRTL } = useTranslation();
  const navigation = useNavigation();
  const route = useRoute();
  
  console.log('🔐 OtpVerificationScreen: Route params:', route.params);
  
  const params = route.params as OtpVerificationRouteParams;
  const { phoneNumber, message } = params || {};

  const { verifyOtp, isLoading } = useAuth();
  const [otpCode, setOtpCode] = useState('');
  const [error, setError] = useState('');

  console.log('🔐 OtpVerificationScreen: Initialized with phone:', phoneNumber);
  console.log('🔐 OtpVerificationScreen: Message:', message);

  const handleOtpSubmit = async () => {
    console.log('🔐 OtpVerificationScreen: handleOtpSubmit called with OTP:', otpCode);
    setError('');

    if (!otpCode) {
      console.log('❌ OtpVerificationScreen: OTP is empty');
      setError('کد تأیید الزامی است');
      return;
    }

    if (otpCode.length !== 6) {
      console.log('❌ OtpVerificationScreen: OTP length is invalid');
      setError('کد تأیید باید ۶ رقم باشد');
      return;
    }

    console.log('✅ OtpVerificationScreen: OTP validation passed, calling verifyOtp...');

    try {
      await verifyOtp({ phone_number: phoneNumber, otp_code: otpCode });
      console.log('✅ OtpVerificationScreen: OTP verification successful');
      // Navigation will be handled automatically by AuthContext
    } catch (error) {
      console.error('💥 OtpVerificationScreen: OTP verification error:', error);
      const errorMessage = error instanceof Error ? error.message : 'کد تأیید نامعتبر است';
      setError(errorMessage);
      
      // Also show alert for critical errors
      Alert.alert('خطا', errorMessage);
    }
  };

  const handleBackToPhone = () => {
    console.log('🔄 OtpVerificationScreen: Going back to phone screen');
    navigation.goBack();
  };

  return (
    <SafeAreaView style={styles.container} edges={['top', 'left', 'right']}>
      <StatusBar barStyle="light-content" backgroundColor="#e6034b" />
      
      <View style={styles.header}>
        <View style={styles.headerContent}>
          <View style={styles.logoContainer}>
            <View style={styles.logoCircle}>
              <GlobalText style={styles.logoText}>🔐</GlobalText>
            </View>
            <GlobalText style={styles.appName}>Azkuja</GlobalText>
            <GlobalText style={styles.appTagline}>
              کد تأیید را وارد کنید
            </GlobalText>
          </View>
        </View>
      </View>

      <KeyboardAvoidingView 
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardView}
      >
        <ScrollView 
          style={styles.content}
          contentContainerStyle={styles.contentContainer}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
        >
          <View style={styles.welcomeCard}>
            <GlobalText style={styles.welcomeTitle}>
              کد تأیید
            </GlobalText>
            <GlobalText style={styles.welcomeSubtitle}>
              کد تأیید به {phoneNumber} ارسال شد
            </GlobalText>
          </View>

          {message && (
            <View style={styles.successContainer}>
              <GlobalText style={styles.successText}>{message}</GlobalText>
            </View>
          )}

          {error && (
            <View style={styles.errorContainer}>
              <GlobalText style={styles.errorText}>{error}</GlobalText>
            </View>
          )}

          <View style={styles.formContainer}>
            <Input
              label="کد تأیید"
              placeholder="کد ۶ رقمی"
              value={otpCode}
              onChangeText={(value) => {
                setOtpCode(value);
                setError('');
              }}
              keyboardType="numeric"
              maxLength={6}
            />

            <View style={styles.testNoticeContainer}>
              <GlobalText style={styles.testNoticeText}>
                کد تستی: 123456
              </GlobalText>
            </View>

            <Button
              title="تأیید و ورود"
              onPress={handleOtpSubmit}
              loading={isLoading}
              fullWidth
              size="large"
              style={styles.submitButton}
            />

            <TouchableOpacity 
              style={styles.changePhoneButton}
              onPress={handleBackToPhone}
            >
              <GlobalText style={styles.changePhoneText}>تغییر شماره تلفن</GlobalText>
            </TouchableOpacity>
          </View>

          <View style={styles.footer}>
            <GlobalText style={styles.footerText}>
              احراز هویت امن افغانی
            </GlobalText>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FAFAFA',
  },
  header: {
    minHeight: 180,
    backgroundColor: '#e6034b',
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: 16,
    paddingBottom: 24,
    elevation: 8,
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.15,
    shadowRadius: 12,
  },
  headerContent: {
    alignItems: 'center',
    paddingHorizontal: 24,
    width: '100%',
  },
  logoContainer: {
    alignItems: 'center',
    marginTop: 16,
  },
  logoCircle: {
    width: 70,
    height: 70,
    borderRadius: 35,
    backgroundColor: '#FFFFFF',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
    elevation: 4,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 6,
  },
  logoText: {
    fontSize: 28,
  },
  appName: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 4,
  },
  appTagline: {
    fontSize: 16,
    color: '#FFE0E6',
    textAlign: 'center',
  },
  keyboardView: {
    flex: 1,
    backgroundColor: '#FAFAFA',
  },
  content: {
    flex: 1,
  },
  contentContainer: {
    flexGrow: 1,
    paddingHorizontal: 24,
  },
  welcomeCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 24,
    padding: 24,
    marginTop: -32,
    marginBottom: 24,
    elevation: 8,
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.15,
    shadowRadius: 12,
  },
  welcomeTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#212121',
    marginBottom: 8,
  },
  welcomeSubtitle: {
    fontSize: 16,
    color: '#757575',
    lineHeight: 22,
  },
  successContainer: {
    backgroundColor: '#E8F5E8',
    borderColor: '#81C784',
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
  },
  successText: {
    fontSize: 14,
    color: '#2E7D32',
    textAlign: 'center',
  },
  errorContainer: {
    backgroundColor: '#FFEBEE',
    borderColor: '#EF9A9A',
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
  },
  errorText: {
    fontSize: 14,
    color: '#C62828',
    textAlign: 'center',
  },
  formContainer: {
    marginBottom: 24,
  },
  testNoticeContainer: {
    backgroundColor: '#FFF8E1',
    borderColor: '#FFD54F',
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    marginTop: 8,
    marginBottom: 16,
  },
  testNoticeText: {
    fontSize: 14,
    color: '#F57F17',
    textAlign: 'center',
    fontWeight: '500',
  },
  submitButton: {
    marginTop: 8,
    elevation: 4,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 6,
  },
  changePhoneButton: {
    alignItems: 'center',
    paddingVertical: 16,
    marginTop: 8,
  },
  changePhoneText: {
    fontSize: 16,
    color: '#e6034b',
    fontWeight: '500',
  },
  footer: {
    alignItems: 'center',
    paddingVertical: 16,
  },
  footerText: {
    fontSize: 12,
    color: '#BDBDBD',
  },
}); 