import React, { useState } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  StatusBar,
  KeyboardAvoidingView,
  Platform,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation } from '@react-navigation/native';

import { GlobalText } from '../../components/GlobalText';
import { Button, Input } from '../../components/ui';
import { useTranslation } from '../../i18n';
import { useAuth } from '../../contexts/AuthContext';

type RegisterStep = 'form' | 'otp';

export const RegisterScreen: React.FC = () => {
  const { t, isRTL } = useTranslation();
  const navigation = useNavigation();

  const { register, verifyOtp, isLoading } = useAuth();
  const [step, setStep] = useState<RegisterStep>('form');
  const [formData, setFormData] = useState({
    name: '',
    phone_number: '',
    email: '',
  });
  const [otpCode, setOtpCode] = useState('');
  const [error, setError] = useState('');
  const [message, setMessage] = useState('');

  const validatePhoneNumber = (phone: string): boolean => {
    const phoneRegex = /^07\d{8}$/;
    return phoneRegex.test(phone);
  };

  const handleFormSubmit = async () => {
    console.log('📝 RegisterScreen: handleFormSubmit called with:', formData);
    
    // Clear previous errors and messages
    setError('');
    setMessage('');

    // Validation
    if (!formData.name.trim()) {
      console.log('❌ RegisterScreen: Name is empty');
      setError('نام و نام خانوادگی الزامی است');
      return;
    }

    if (!formData.phone_number) {
      console.log('❌ RegisterScreen: Phone number is empty');
      setError('شماره تلفن الزامی است');
      return;
    }

    if (!validatePhoneNumber(formData.phone_number)) {
      console.log('❌ RegisterScreen: Phone number format is invalid');
      setError('شماره تلفن باید ۱۰ رقم باشد و با ۰۷ شروع شود');
      return;
    }

    // Email validation (optional)
    if (formData.email && !/\S+@\S+\.\S+/.test(formData.email)) {
      console.log('❌ RegisterScreen: Email format is invalid');
      setError('فرمت ایمیل صحیح نیست');
      return;
    }

    console.log('✅ RegisterScreen: Form validation passed, calling register...');

    try {
      const response = await register(formData);
      console.log('✅ RegisterScreen: Registration successful, response:', response);
      
      setMessage(response.message);
      setStep('otp');
      
      console.log('🔄 RegisterScreen: Step changed to OTP');
    } catch (error) {
      console.error('💥 RegisterScreen: Registration error:', error);
      const errorMessage = error instanceof Error ? error.message : 'خطا در ثبت نام';
      setError(errorMessage);
      
      // Also show alert for critical errors
      Alert.alert('خطا', errorMessage);
    }
  };

  const handleOtpSubmit = async () => {
    console.log('🔐 RegisterScreen: handleOtpSubmit called with OTP:', otpCode);
    setError('');

    if (!otpCode) {
      console.log('❌ RegisterScreen: OTP is empty');
      setError('کد تأیید الزامی است');
      return;
    }

    if (otpCode.length !== 6) {
      console.log('❌ RegisterScreen: OTP length is invalid');
      setError('کد تأیید باید ۶ رقم باشد');
      return;
    }

    console.log('✅ RegisterScreen: OTP validation passed, calling verifyOtp...');

    try {
      await verifyOtp({ phone_number: formData.phone_number, otp_code: otpCode });
      console.log('✅ RegisterScreen: OTP verification successful');
      // Navigation will be handled automatically by AuthContext
    } catch (error) {
      console.error('💥 RegisterScreen: OTP verification error:', error);
      const errorMessage = error instanceof Error ? error.message : 'کد تأیید نامعتبر است';
      setError(errorMessage);
      
      // Also show alert for critical errors
      Alert.alert('خطا', errorMessage);
    }
  };

  const handleBackToForm = () => {
    console.log('🔄 RegisterScreen: Going back to form step');
    setStep('form');
    setOtpCode('');
    setError('');
    setMessage('');
  };

  const handleNavigateToLogin = () => {
    console.log('🔄 RegisterScreen: Navigating to Login');
    try {
      navigation.navigate('Login' as never);
    } catch (error) {
      console.error('💥 RegisterScreen: Navigation to Login failed:', error);
    }
  };

  const handleInputChange = (field: keyof typeof formData) => (value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    setError('');
  };

  console.log('🎨 RegisterScreen: Rendering with step:', step, 'loading:', isLoading);

  return (
    <SafeAreaView style={styles.container} edges={['top', 'left', 'right']}>
      <StatusBar barStyle="light-content" backgroundColor="#e6034b" />
      
      <View style={styles.header}>
        <View style={styles.headerContent}>
          <View style={styles.logoContainer}>
            <View style={styles.logoCircle}>
              <GlobalText style={styles.logoText}>👥</GlobalText>
            </View>
            <GlobalText style={styles.appName}>Azkuja</GlobalText>
            <GlobalText style={styles.appTagline}>
              {step === 'form' ? 'حساب جدید ایجاد کنید' : 'کد تأیید را وارد کنید'}
            </GlobalText>
          </View>
        </View>
      </View>

      <KeyboardAvoidingView 
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardView}
      >
        <ScrollView 
          style={styles.content}
          contentContainerStyle={styles.contentContainer}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
        >
          <View style={styles.welcomeCard}>
            <GlobalText style={styles.welcomeTitle}>
              {step === 'form' ? 'ثبت نام' : 'کد تأیید'}
            </GlobalText>
            <GlobalText style={styles.welcomeSubtitle}>
              {step === 'form' 
                ? 'اطلاعات خود را وارد کنید'
                : `کد تأیید به ${formData.phone_number} ارسال شد`
              }
            </GlobalText>
          </View>

          {message && (
            <View style={styles.successContainer}>
              <GlobalText style={styles.successText}>{message}</GlobalText>
            </View>
          )}

          {error && (
            <View style={styles.errorContainer}>
              <GlobalText style={styles.errorText}>{error}</GlobalText>
            </View>
          )}

          {step === 'form' && (
            <View style={styles.formContainer}>
              <Input
                label="نام و نام خانوادگی"
                placeholder="احمد احمدی"
                value={formData.name}
                onChangeText={handleInputChange('name')}
              />

              <Input
                label="شماره تلفن"
                placeholder="07XXXXXXXX"
                value={formData.phone_number}
                onChangeText={handleInputChange('phone_number')}
                type="phone"
              />

              <Input
                label="ایمیل (اختیاری)"
                placeholder="<EMAIL>"
                value={formData.email}
                onChangeText={handleInputChange('email')}
                type="email"
              />
              
              <View style={styles.helpContainer}>
                <GlobalText style={styles.helpText}>
                  شماره تلفن باید ۱۰ رقم باشد و با ۰۷ شروع شود
                </GlobalText>
              </View>

              <Button
                title="ثبت نام و ارسال کد"
                onPress={handleFormSubmit}
                loading={isLoading}
                fullWidth
                size="large"
                style={styles.submitButton}
              />
            </View>
          )}

          {step === 'otp' && (
            <View style={styles.formContainer}>
              <Input
                label="کد تأیید"
                placeholder="کد ۶ رقمی"
                value={otpCode}
                onChangeText={(value) => {
                  setOtpCode(value);
                  setError('');
                }}
              />

              <View style={styles.testNoticeContainer}>
                <GlobalText style={styles.testNoticeText}>
                  کد تستی: 123456
                </GlobalText>
              </View>

              <Button
                title="تأیید و ورود"
                onPress={handleOtpSubmit}
                loading={isLoading}
                fullWidth
                size="large"
                style={styles.submitButton}
              />

              <TouchableOpacity 
                style={styles.changeFormButton}
                onPress={handleBackToForm}
              >
                <GlobalText style={styles.changeFormText}>ویرایش اطلاعات</GlobalText>
              </TouchableOpacity>
            </View>
          )}

          {step === 'form' && (
            <View style={styles.loginContainer}>
              <GlobalText style={styles.loginText}>حساب کاربری دارید؟ </GlobalText>
              <TouchableOpacity onPress={handleNavigateToLogin}>
                <GlobalText style={styles.loginLink}>ورود</GlobalText>
              </TouchableOpacity>
            </View>
          )}

          <View style={styles.footer}>
            <GlobalText style={styles.footerText}>
              به خانواده آذوقه بپیوندید
            </GlobalText>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FAFAFA',
  },
  header: {
    minHeight: 180,
    backgroundColor: '#e6034b',
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: 16,
    paddingBottom: 24,
    elevation: 8,
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.15,
    shadowRadius: 12,
  },
  headerContent: {
    alignItems: 'center',
    paddingHorizontal: 24,
    width: '100%',
  },
  logoContainer: {
    alignItems: 'center',
    marginTop: 16,
  },
  logoCircle: {
    width: 70,
    height: 70,
    borderRadius: 35,
    backgroundColor: '#FFFFFF',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
    elevation: 4,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 6,
  },
  logoText: {
    fontSize: 28,
  },
  appName: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 4,
  },
  appTagline: {
    fontSize: 16,
    color: '#FFE0E6',
    textAlign: 'center',
  },
  keyboardView: {
    flex: 1,
    backgroundColor: '#FAFAFA',
  },
  content: {
    flex: 1,
  },
  contentContainer: {
    flexGrow: 1,
    paddingHorizontal: 24,
  },
  welcomeCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 24,
    padding: 24,
    marginTop: -32,
    marginBottom: 24,
    elevation: 8,
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.15,
    shadowRadius: 12,
  },
  welcomeTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#212121',
    marginBottom: 8,
  },
  welcomeSubtitle: {
    fontSize: 16,
    color: '#757575',
    lineHeight: 22,
  },
  successContainer: {
    backgroundColor: '#E8F5E8',
    borderColor: '#81C784',
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
  },
  successText: {
    fontSize: 14,
    color: '#2E7D32',
    textAlign: 'center',
  },
  errorContainer: {
    backgroundColor: '#FFEBEE',
    borderColor: '#EF9A9A',
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
  },
  errorText: {
    fontSize: 14,
    color: '#C62828',
    textAlign: 'center',
  },
  formContainer: {
    marginBottom: 24,
  },
  helpContainer: {
    backgroundColor: '#F5F5F5',
    borderRadius: 8,
    padding: 12,
    marginTop: 8,
    marginBottom: 16,
  },
  helpText: {
    fontSize: 14,
    color: '#757575',
    textAlign: 'center',
  },
  testNoticeContainer: {
    backgroundColor: '#FFF8E1',
    borderColor: '#FFD54F',
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    marginTop: 8,
    marginBottom: 16,
  },
  testNoticeText: {
    fontSize: 14,
    color: '#F57F17',
    textAlign: 'center',
    fontWeight: '500',
  },
  submitButton: {
    marginTop: 8,
    elevation: 4,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 6,
  },
  changeFormButton: {
    alignItems: 'center',
    paddingVertical: 16,
    marginTop: 8,
  },
  changeFormText: {
    fontSize: 16,
    color: '#e6034b',
    fontWeight: '500',
  },
  loginContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 'auto',
    paddingVertical: 24,
  },
  loginText: {
    fontSize: 16,
    color: '#757575',
  },
  loginLink: {
    fontSize: 16,
    color: '#e6034b',
    fontWeight: '600',
    marginLeft: 4,
  },
  footer: {
    alignItems: 'center',
    paddingVertical: 16,
  },
  footerText: {
    fontSize: 12,
    color: '#BDBDBD',
  },
}); 