import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  StatusBar,
  KeyboardAvoidingView,
  Platform,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation } from '@react-navigation/native';

import { GlobalText } from '../../components/GlobalText';
import { Button, Input } from '../../components/ui';
import { useTranslation } from '../../i18n';
import { useAuth, UserNotFoundError } from '../../contexts/AuthContext';

type LoginStep = 'phone' | 'otp';

export const PhoneLoginScreen: React.FC = () => {
  const { t, isRTL } = useTranslation();
  const navigation = useNavigation();

  const { login, verifyOtp, isLoading } = useAuth();
  const [step, setStep] = useState<LoginStep>('phone');
  const [phoneNumber, setPhoneNumber] = useState('');
  const [otpCode, setOtpCode] = useState('');
  const [error, setError] = useState('');
  const [message, setMessage] = useState('');

  // Track step changes
  useEffect(() => {
    console.log('🔄 PhoneLoginScreen: Step changed to:', step);
  }, [step]);

  // Track loading changes
  useEffect(() => {
    console.log('⏳ PhoneLoginScreen: Loading changed to:', isLoading);
  }, [isLoading]);

  // Prevent step from being reset when component re-renders
  useEffect(() => {
    if (step === 'otp' && !isLoading) {
      console.log('🔒 PhoneLoginScreen: Protecting OTP step from reset');
    }
  }, [step, isLoading]);

  const validatePhoneNumber = (phone: string): boolean => {
    const phoneRegex = /^07\d{8}$/;
    return phoneRegex.test(phone);
  };

  const handlePhoneSubmit = async () => {
    console.log('📞 PhoneLoginScreen: handlePhoneSubmit called with phone:', phoneNumber);
    
    // Clear previous errors and messages
    setError('');
    setMessage('');

    // Validation
    if (!phoneNumber) {
      console.log('❌ PhoneLoginScreen: Phone number is empty');
      setError('شماره تلفن الزامی است');
      return;
    }

    if (!validatePhoneNumber(phoneNumber)) {
      console.log('❌ PhoneLoginScreen: Phone number format is invalid');
      setError('شماره تلفن باید ۱۰ رقم باشد و با ۰۷ شروع شود');
      return;
    }

    console.log('✅ PhoneLoginScreen: Phone validation passed, calling login...');

    try {
      const response = await login({ phone_number: phoneNumber });
      console.log('✅ PhoneLoginScreen: Login successful, response:', response);
      
      setMessage(response.message);
      console.log('🔄 PhoneLoginScreen: Setting step to OTP');
      
      // Use local state - the simplest approach
      setStep('otp');
      console.log('✅ PhoneLoginScreen: Step set to OTP');
    } catch (error) {
      console.error('💥 PhoneLoginScreen: Login error:', error);
      
      // Handle user not found error - redirect to register
      if (error instanceof UserNotFoundError) {
        console.log('👤 PhoneLoginScreen: User not found - showing register redirect dialog');
        Alert.alert(
          'حساب کاربری یافت نشد',
          'این شماره تلفن ثبت نام نشده است. آیا می‌خواهید حساب جدید ایجاد کنید؟',
          [
            {
              text: 'انصراف',
              style: 'cancel',
            },
            {
              text: 'ثبت نام',
              onPress: () => {
                console.log('🔄 PhoneLoginScreen: User chose to register');
                try {
                  navigation.navigate('Register' as never);
                } catch (navError) {
                  console.error('💥 PhoneLoginScreen: Navigation to Register failed:', navError);
                }
              },
            },
          ],
          { cancelable: true }
        );
        return;
      }
      
      // Handle other errors
      const errorMessage = error instanceof Error ? error.message : 'خطا در ارسال کد';
      setError(errorMessage);
      
      // Also show alert for critical errors
      Alert.alert('خطا', errorMessage);
    }
  };

  const handleOtpSubmit = async () => {
    console.log('🔐 PhoneLoginScreen: handleOtpSubmit called with OTP:', otpCode);
    setError('');

    if (!otpCode) {
      console.log('❌ PhoneLoginScreen: OTP is empty');
      setError('کد تأیید الزامی است');
      return;
    }

    if (otpCode.length !== 6) {
      console.log('❌ PhoneLoginScreen: OTP length is invalid');
      setError('کد تأیید باید ۶ رقم باشد');
      return;
    }

    console.log('✅ PhoneLoginScreen: OTP validation passed, calling verifyOtp...');

    try {
      await verifyOtp({ phone_number: phoneNumber, otp_code: otpCode });
      console.log('✅ PhoneLoginScreen: OTP verification successful');
      // Navigation will be handled automatically by AuthContext
    } catch (error) {
      console.error('💥 PhoneLoginScreen: OTP verification error:', error);
      const errorMessage = error instanceof Error ? error.message : 'کد تأیید نامعتبر است';
      setError(errorMessage);
      
      // Also show alert for critical errors
      Alert.alert('خطا', errorMessage);
    }
  };

  const handleBackToPhone = () => {
    console.log('🔄 PhoneLoginScreen: Going back to phone step');
    setStep('phone');
    setOtpCode('');
    setError('');
    setMessage('');
  };

  const handleNavigateToRegister = () => {
    console.log('🔄 PhoneLoginScreen: Navigating to Register');
    try {
      navigation.navigate('Register' as never);
    } catch (error) {
      console.error('💥 PhoneLoginScreen: Navigation to Register failed:', error);
    }
  };

  console.log('🎨 PhoneLoginScreen: Rendering with step:', step, 'loading:', isLoading);
  console.log('🎨 PhoneLoginScreen: Current phone number:', phoneNumber);
  console.log('🎨 PhoneLoginScreen: Current OTP code:', otpCode);
  console.log('🎨 PhoneLoginScreen: Current message:', message);

  return (
    <SafeAreaView style={styles.container} edges={['top', 'left', 'right']}>
      <StatusBar barStyle="light-content" backgroundColor="#e6034b" />
      
      <View style={styles.header}>
        <View style={styles.headerContent}>
          <View style={styles.logoContainer}>
            <View style={styles.logoCircle}>
              <GlobalText style={styles.logoText}>📱</GlobalText>
            </View>
            <GlobalText style={styles.appName}>Azkuja</GlobalText>
            <GlobalText style={styles.appTagline}>
              {step === 'phone' ? 'خوش آمدید' : 'کد تأیید را وارد کنید'}
            </GlobalText>
          </View>
        </View>
      </View>

      <KeyboardAvoidingView 
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardView}
      >
        <ScrollView 
          style={styles.content}
          contentContainerStyle={styles.contentContainer}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
        >
          <View style={styles.welcomeCard}>
            <GlobalText style={styles.welcomeTitle}>
              {step === 'phone' ? 'ورود' : 'کد تأیید'}
            </GlobalText>
            <GlobalText style={styles.welcomeSubtitle}>
              {step === 'phone' 
                ? 'شماره تلفن خود را وارد کنید'
                : `کد تأیید به ${phoneNumber} ارسال شد`
              }
            </GlobalText>
          </View>

          {message && (
            <View style={styles.successContainer}>
              <GlobalText style={styles.successText}>{message}</GlobalText>
            </View>
          )}

          {error && (
            <View style={styles.errorContainer}>
              <GlobalText style={styles.errorText}>{error}</GlobalText>
            </View>
          )}

          {step === 'phone' && (
            <View style={styles.formContainer}>
              <Input
                label="شماره تلفن"
                placeholder="07XXXXXXXX"
                value={phoneNumber}
                onChangeText={(value) => {
                  setPhoneNumber(value);
                  setError('');
                }}
                type="phone"
              />
              
              <View style={styles.helpContainer}>
                <GlobalText style={styles.helpText}>
                  شماره تلفن باید ۱۰ رقم باشد و با ۰۷ شروع شود
                </GlobalText>
              </View>

              <Button
                title="ارسال کد"
                onPress={handlePhoneSubmit}
                loading={isLoading}
                fullWidth
                size="large"
                style={styles.submitButton}
              />
            </View>
          )}

          {step === 'otp' && (
            <View style={styles.formContainer}>
              <Input
                label="کد تأیید"
                placeholder="کد ۶ رقمی"
                value={otpCode}
                onChangeText={(value) => {
                  setOtpCode(value);
                  setError('');
                }}
              />

              <View style={styles.testNoticeContainer}>
                <GlobalText style={styles.testNoticeText}>
                  کد تستی: 123456
                </GlobalText>
              </View>

              <Button
                title="تأیید و ورود"
                onPress={handleOtpSubmit}
                loading={isLoading}
                fullWidth
                size="large"
                style={styles.submitButton}
              />

              <TouchableOpacity 
                style={styles.changePhoneButton}
                onPress={handleBackToPhone}
              >
                <GlobalText style={styles.changePhoneText}>تغییر شماره تلفن</GlobalText>
              </TouchableOpacity>
            </View>
          )}

          {step === 'phone' && (
            <View style={styles.registerContainer}>
              <GlobalText style={styles.registerText}>حساب کاربری ندارید؟ </GlobalText>
              <TouchableOpacity onPress={handleNavigateToRegister}>
                <GlobalText style={styles.registerLink}>ثبت نام</GlobalText>
              </TouchableOpacity>
            </View>
          )}

          <View style={styles.footer}>
            <GlobalText style={styles.footerText}>
              احراز هویت امن افغانی
            </GlobalText>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FAFAFA',
  },
  header: {
    minHeight: 180,
    backgroundColor: '#e6034b',
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: 16,
    paddingBottom: 24,
    elevation: 8,
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.15,
    shadowRadius: 12,
  },
  headerContent: {
    alignItems: 'center',
    paddingHorizontal: 24,
    width: '100%',
  },
  logoContainer: {
    alignItems: 'center',
    marginTop: 16,
  },
  logoCircle: {
    width: 70,
    height: 70,
    borderRadius: 35,
    backgroundColor: '#FFFFFF',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
    elevation: 4,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 6,
  },
  logoText: {
    fontSize: 28,
  },
  appName: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 4,
  },
  appTagline: {
    fontSize: 16,
    color: '#FFE0E6',
    textAlign: 'center',
  },
  keyboardView: {
    flex: 1,
    backgroundColor: '#FAFAFA',
  },
  content: {
    flex: 1,
  },
  contentContainer: {
    flexGrow: 1,
    paddingHorizontal: 24,
  },
  welcomeCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 24,
    padding: 24,
    marginTop: -32,
    marginBottom: 24,
    elevation: 8,
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.15,
    shadowRadius: 12,
  },
  welcomeTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#212121',
    marginBottom: 8,
  },
  welcomeSubtitle: {
    fontSize: 16,
    color: '#757575',
    lineHeight: 22,
  },
  successContainer: {
    backgroundColor: '#E8F5E8',
    borderColor: '#81C784',
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
  },
  successText: {
    fontSize: 14,
    color: '#2E7D32',
    textAlign: 'center',
  },
  errorContainer: {
    backgroundColor: '#FFEBEE',
    borderColor: '#EF9A9A',
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
  },
  errorText: {
    fontSize: 14,
    color: '#C62828',
    textAlign: 'center',
  },
  formContainer: {
    marginBottom: 24,
  },
  helpContainer: {
    backgroundColor: '#F5F5F5',
    borderRadius: 8,
    padding: 12,
    marginTop: 8,
    marginBottom: 16,
  },
  helpText: {
    fontSize: 14,
    color: '#757575',
    textAlign: 'center',
  },
  testNoticeContainer: {
    backgroundColor: '#FFF8E1',
    borderColor: '#FFD54F',
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    marginTop: 8,
    marginBottom: 16,
  },
  testNoticeText: {
    fontSize: 14,
    color: '#F57F17',
    textAlign: 'center',
    fontWeight: '500',
  },
  submitButton: {
    marginTop: 8,
    elevation: 4,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 6,
  },
  changePhoneButton: {
    alignItems: 'center',
    paddingVertical: 16,
    marginTop: 8,
  },
  changePhoneText: {
    fontSize: 16,
    color: '#e6034b',
    fontWeight: '500',
  },
  registerContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 'auto',
    paddingVertical: 24,
  },
  registerText: {
    fontSize: 16,
    color: '#757575',
  },
  registerLink: {
    fontSize: 16,
    color: '#e6034b',
    fontWeight: '600',
    marginLeft: 4,
  },
  footer: {
    alignItems: 'center',
    paddingVertical: 16,
  },
  footerText: {
    fontSize: 12,
    color: '#BDBDBD',
  },
});
 