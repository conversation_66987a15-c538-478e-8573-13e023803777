import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  StatusBar,
  KeyboardAvoidingView,
  Platform,
  TouchableOpacity,
  Image,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

// Our custom imports
import { colors, typography, spacing, borderRadius, shadows } from '../../theme';
import { Button, Input } from '../../components/ui';
import { useTranslation } from '../../i18n';

interface LoginScreenProps {
  onLoginSuccess?: () => void;
  onNavigateToRegister?: () => void;
  onForgotPassword?: () => void;
}

export const LoginScreen: React.FC<LoginScreenProps> = ({
  onLoginSuccess,
  onNavigateToRegister,
  onForgotPassword,
}) => {
  // Safe hook usage with fallbacks
  let t, isRTL;
  try {
    const translationHook = useTranslation();
    t = translationHook.t;
    isRTL = translationHook.isRTL;
  } catch (error) {
    console.warn('Translation hook failed in LoginScreen, using fallbacks:', error);
    t = (key: string) => {
      // Simple fallback translations
      const fallbacks: Record<string, string> = {
        'auth.login': 'Login',
        'auth.email': 'Email',
        'auth.password': 'Password',
        'auth.forgotPassword': 'Forgot Password?',
        'auth.register': 'Register',
        'auth.emailRequired': 'Email is required',
        'auth.passwordRequired': 'Password is required',
        'auth.passwordTooShort': 'Password too short',
        'auth.invalidCredentials': 'Invalid email format',
        'auth.loginError': 'Login failed',
        'auth.phoneNumber': 'Phone Number',
      };
      return fallbacks[key] || key;
    };
    isRTL = false;
  }
  const [formData, setFormData] = useState({
    email: '',
    password: '',
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isLoading, setIsLoading] = useState(false);

  // Validation function
  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.email) {
      newErrors.email = t('auth.emailRequired');
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = t('auth.invalidCredentials');
    }

    if (!formData.password) {
      newErrors.password = t('auth.passwordRequired');
    } else if (formData.password.length < 6) {
      newErrors.password = t('auth.passwordTooShort');
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleLogin = async () => {
    if (!validateForm()) return;

    setIsLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // TODO: Replace with actual authentication API call
      console.log('Login attempt:', formData);
      
      onLoginSuccess?.();
    } catch (error) {
      setErrors({ general: t('auth.loginError') });
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (field: keyof typeof formData) => (value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor={colors.primary[600]} />
      
      {/* Header Background */}
      <View style={styles.header}>
        <View style={styles.headerContent}>
          {/* App Logo/Icon */}
          <View style={styles.logoContainer}>
            <View style={styles.logoCircle}>
              <Text style={styles.logoText}>🍽️</Text>
            </View>
            <Text style={styles.appName}>Azkuja</Text>
            <Text style={styles.appTagline}>
              {isRTL ? 'غذای خوشمزه، تحویل سریع' : 'Delicious Food, Fast Delivery'}
            </Text>
          </View>
        </View>
      </View>

      <KeyboardAvoidingView 
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardView}
      >
        <ScrollView 
          style={styles.content}
          contentContainerStyle={styles.contentContainer}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
        >
          {/* Welcome Card */}
          <View style={styles.welcomeCard}>
            <Text style={[styles.welcomeTitle, { textAlign: isRTL ? 'right' : 'left' }]}>
              {t('auth.login')}
            </Text>
            <Text style={[styles.welcomeSubtitle, { textAlign: isRTL ? 'right' : 'left' }]}>
              {isRTL 
                ? 'به اپلیکیشن آذوقه خوش آمدید' 
                : 'Welcome back to Azkuja'
              }
            </Text>
          </View>

          {/* Error Message */}
          {errors.general && (
            <View style={styles.errorContainer}>
              <Text style={styles.errorText}>{errors.general}</Text>
            </View>
          )}

          {/* Login Form */}
          <View style={styles.formContainer}>
            <Input
              label={t('auth.email')}
              placeholder="<EMAIL>"
              value={formData.email}
              onChangeText={handleInputChange('email')}
              type="email"
              error={errors.email}
              testID="email-input"
            />

            <Input
              label={t('auth.password')}
              placeholder={t('auth.password')}
              value={formData.password}
              onChangeText={handleInputChange('password')}
              type="password"
              error={errors.password}
              testID="password-input"
            />

            {/* Forgot Password */}
            <TouchableOpacity 
              style={[styles.forgotPassword, { alignSelf: isRTL ? 'flex-start' : 'flex-end' }]}
              onPress={onForgotPassword}
            >
              <Text style={styles.forgotPasswordText}>
                {t('auth.forgotPassword')}
              </Text>
            </TouchableOpacity>

            {/* Login Button */}
            <Button
              title={t('auth.login')}
              onPress={handleLogin}
              loading={isLoading}
              fullWidth
              size="large"
              style={styles.loginButton}
              testID="login-button"
            />

            {/* Divider */}
            <View style={styles.divider}>
              <View style={styles.dividerLine} />
              <Text style={styles.dividerText}>
                {isRTL ? 'یا' : 'or'}
              </Text>
              <View style={styles.dividerLine} />
            </View>

            {/* Social Login Buttons */}
            <View style={styles.socialContainer}>
              <TouchableOpacity style={styles.socialButton}>
                <Text style={styles.socialButtonText}>📱 {t('auth.phoneNumber')}</Text>
              </TouchableOpacity>
            </View>

            {/* Register Link */}
            <View style={styles.registerContainer}>
              <Text style={styles.registerText}>
                {isRTL ? 'حساب کاربری ندارید؟' : "Don't have an account? "}
              </Text>
              <TouchableOpacity onPress={onNavigateToRegister}>
                <Text style={styles.registerLink}>
                  {t('auth.register')}
                </Text>
              </TouchableOpacity>
            </View>
          </View>

          {/* Bottom Decoration */}
          <View style={styles.bottomDecoration}>
            <Text style={styles.decorationText}>
              {isRTL ? '🇦🇫 ساخته شده برای افغانستان' : '🇦🇫 Made for Afghanistan'}
            </Text>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background.default,
  },
  header: {
    height: 280,
    backgroundColor: colors.primary[500],
    justifyContent: 'center',
    alignItems: 'center',
    ...shadows.lg,
  },
  headerContent: {
    alignItems: 'center',
    paddingHorizontal: spacing[6],
  },
  logoContainer: {
    alignItems: 'center',
    marginTop: spacing[4],
  },
  logoCircle: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: colors.white,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: spacing[4],
    ...shadows.md,
  },
  logoText: {
    fontSize: 32,
  },
  appName: {
    fontSize: typography.heading.h1.fontSize,
    fontWeight: typography.heading.h1.fontWeight,
    color: colors.white,
    marginBottom: spacing[1],
  },
  appTagline: {
    fontSize: typography.body.medium.fontSize,
    color: colors.primary[100],
    textAlign: 'center',
  },
  keyboardView: {
    flex: 1,
  },
  content: {
    flex: 1,
  },
  contentContainer: {
    flexGrow: 1,
    paddingHorizontal: spacing[6],
  },
  welcomeCard: {
    backgroundColor: colors.white,
    borderRadius: borderRadius.xl,
    padding: spacing[6],
    marginTop: -spacing[12], // Overlap with header
    marginBottom: spacing[6],
    ...shadows.lg,
  },
  welcomeTitle: {
    fontSize: typography.heading.h2.fontSize,
    fontWeight: typography.heading.h2.fontWeight,
    color: colors.text.primary,
    marginBottom: spacing[2],
  },
  welcomeSubtitle: {
    fontSize: typography.body.medium.fontSize,
    color: colors.text.secondary,
  },
  errorContainer: {
    backgroundColor: colors.error[50],
    borderColor: colors.error[200],
    borderWidth: 1,
    borderRadius: borderRadius.md,
    padding: spacing[3],
    marginBottom: spacing[4],
  },
  errorText: {
    fontSize: typography.body.small.fontSize,
    color: colors.error[600],
    textAlign: 'center',
  },
  formContainer: {
    flex: 1,
  },
  forgotPassword: {
    marginBottom: spacing[6],
    marginTop: -spacing[2],
  },
  forgotPasswordText: {
    fontSize: typography.body.small.fontSize,
    color: colors.primary[500],
    fontWeight: '500',
  },
  loginButton: {
    marginBottom: spacing[6],
    ...shadows.md,
  },
  divider: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: spacing[6],
  },
  dividerLine: {
    flex: 1,
    height: 1,
    backgroundColor: colors.border.light,
  },
  dividerText: {
    fontSize: typography.body.small.fontSize,
    color: colors.text.hint,
    marginHorizontal: spacing[4],
  },
  socialContainer: {
    marginBottom: spacing[8],
  },
  socialButton: {
    backgroundColor: colors.background.surface,
    borderColor: colors.border.light,
    borderWidth: 1,
    borderRadius: borderRadius.md,
    padding: spacing[4],
    alignItems: 'center',
    marginBottom: spacing[3],
    ...shadows.sm,
  },
  socialButtonText: {
    fontSize: typography.body.medium.fontSize,
    color: colors.text.primary,
    fontWeight: '500',
  },
  registerContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 'auto',
    paddingVertical: spacing[6],
  },
  registerText: {
    fontSize: typography.body.medium.fontSize,
    color: colors.text.secondary,
  },
  registerLink: {
    fontSize: typography.body.medium.fontSize,
    color: colors.primary[500],
    fontWeight: '600',
    marginLeft: spacing[1],
  },
  bottomDecoration: {
    alignItems: 'center',
    paddingVertical: spacing[4],
  },
  decorationText: {
    fontSize: typography.body.small.fontSize,
    color: colors.text.hint,
  },
}); 