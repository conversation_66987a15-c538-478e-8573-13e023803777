import React, { useState } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  StatusBar,
  KeyboardAvoidingView,
  Platform,
  TouchableOpacity,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

// Our custom imports
import { colors, typography, spacing, borderRadius, shadows } from '../../theme';
import { Button, Input } from '../../components/ui';
import { Text } from '../../components/GlobalText';
import { useTranslation } from '../../i18n';

interface ForgotPasswordScreenProps {
  onEmailSent?: () => void;
  onNavigateToLogin?: () => void;
}

export const ForgotPasswordScreen: React.FC<ForgotPasswordScreenProps> = ({
  onEmailSent,
  onNavigateToLogin,
}) => {
  const { t, isRTL } = useTranslation();

  const [email, setEmail] = useState('');
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [emailSent, setEmailSent] = useState(false);

  const handleSendResetEmail = async () => {
    if (!email) {
      setError('Email is required');
      return;
    }

    setIsLoading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 2000));
      console.log('Password reset email sent to:', email);
      setEmailSent(true);
      onEmailSent?.();
    } catch (error) {
      setError('Failed to send reset email. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  if (emailSent) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle="light-content" backgroundColor={colors.primary[600]} />
        
        {/* Success Header */}
        <View style={styles.header}>
          <View style={styles.headerContent}>
            <View style={styles.logoContainer}>
              <View style={styles.logoCircle}>
                <Text style={styles.logoText}>✉️</Text>
              </View>
              <Text style={styles.appName} variant="heading">Email Sent!</Text>
              <Text style={styles.appTagline} variant="body">
                {isRTL ? 'ایمیل خود را بررسی کنید' : 'Check your email'}
              </Text>
            </View>
          </View>
        </View>

        <View style={styles.content}>
          <View style={styles.successCard}>
            <Text style={styles.successTitle} variant="heading">
              Reset Email Sent
            </Text>
            <Text style={styles.successMessage} variant="body">
              {isRTL 
                ? `لینک بازنشانی رمز عبور به ${email} ارسال شد. ایمیل خود را بررسی کنید.`
                : `We've sent a password reset link to ${email}. Please check your email and follow the instructions.`
              }
            </Text>
            
            <Button
              title={t('auth.login')}
              onPress={() => onNavigateToLogin?.()}
              variant="primary"
              fullWidth
              size="large"
              style={styles.backButton}
            />
          </View>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor={colors.primary[600]} />
      
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.headerContent}>
          <View style={styles.logoContainer}>
            <View style={styles.logoCircle}>
              <Text style={styles.logoText}>🔑</Text>
            </View>
            <Text style={styles.appName} variant="heading">Reset Password</Text>
            <Text style={styles.appTagline} variant="body">
              {isRTL ? 'رمز عبور خود را فراموش کرده‌اید؟' : "Don't worry, we'll help you"}
            </Text>
          </View>
        </View>
      </View>

      <KeyboardAvoidingView 
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardView}
      >
        <ScrollView 
          style={styles.content}
          contentContainerStyle={styles.contentContainer}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
        >
          {/* Instructions Card */}
          <View style={styles.welcomeCard}>
            <Text style={styles.welcomeTitle} variant="heading">
              {t('auth.forgotPassword')}
            </Text>
            <Text style={styles.welcomeSubtitle} variant="body">
              {isRTL 
                ? 'ایمیل خود را وارد کنید تا لینک بازنشانی رمز عبور برای شما ارسال شود'
                : 'Enter your email address and we\'ll send you a link to reset your password'
              }
            </Text>
          </View>

          {/* Error Message */}
          {error && (
            <View style={styles.errorContainer}>
              <Text style={styles.errorText} variant="caption">{error}</Text>
            </View>
          )}

          {/* Reset Form */}
          <View style={styles.formContainer}>
            <Input
              label={t('auth.email')}
              placeholder="<EMAIL>"
              value={email}
              onChangeText={(value) => {
                setEmail(value);
                setError('');
              }}
              type="email"
              error={error}
              testID="email-input"
            />

            {/* Send Reset Email Button */}
            <Button
              title="Send Reset Link"
              onPress={handleSendResetEmail}
              loading={isLoading}
              fullWidth
              size="large"
              style={styles.sendButton}
              testID="send-button"
            />

            {/* Back to Login */}
            <TouchableOpacity 
              style={styles.backToLogin}
              onPress={() => onNavigateToLogin?.()}
            >
              <Text style={styles.backToLoginText} variant="button">
                ← {t('common.back')} to {t('auth.login')}
              </Text>
            </TouchableOpacity>
          </View>

          {/* Help Section */}
          <View style={styles.helpSection}>
            <Text style={styles.helpTitle} variant="heading">Need Help?</Text>
            <Text style={styles.helpText} variant="body">
              {isRTL 
                ? 'اگر ایمیل دریافت نکردید، پوشه اسپم خود را بررسی کنید یا با پشتیبانی تماس بگیرید.'
                : "If you don't receive an email, check your spam folder or contact our support team."
              }
            </Text>
          </View>

          {/* Bottom Decoration */}
          <View style={styles.bottomDecoration}>
            <Text style={styles.decorationText} variant="caption">
              {isRTL ? '🔒 امنیت حساب شما مهم است' : '🔒 Your account security matters'}
            </Text>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background.default,
  },
  header: {
    height: 240,
    backgroundColor: colors.primary[500],
    justifyContent: 'center',
    alignItems: 'center',
    ...shadows.lg,
  },
  headerContent: {
    alignItems: 'center',
    paddingHorizontal: spacing[6],
  },
  logoContainer: {
    alignItems: 'center',
    marginTop: spacing[4],
  },
  logoCircle: {
    width: 70,
    height: 70,
    borderRadius: 35,
    backgroundColor: colors.white,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: spacing[3],
    ...shadows.md,
  },
  logoText: {
    fontSize: 28,
  },
  appName: {
    fontSize: typography.heading.h2.fontSize,
    fontWeight: typography.heading.h2.fontWeight,
    color: colors.white,
    marginBottom: spacing[1],
  },
  appTagline: {
    fontSize: typography.body.medium.fontSize,
    color: colors.primary[100],
    textAlign: 'center',
  },
  keyboardView: {
    flex: 1,
  },
  content: {
    flex: 1,
  },
  contentContainer: {
    flexGrow: 1,
    paddingHorizontal: spacing[6],
  },
  welcomeCard: {
    backgroundColor: colors.white,
    borderRadius: borderRadius.xl,
    padding: spacing[6],
    marginTop: -spacing[8],
    marginBottom: spacing[6],
    ...shadows.lg,
  },
  welcomeTitle: {
    fontSize: typography.heading.h2.fontSize,
    fontWeight: typography.heading.h2.fontWeight,
    color: colors.text.primary,
    marginBottom: spacing[3],
  },
  welcomeSubtitle: {
    fontSize: typography.body.medium.fontSize,
    color: colors.text.secondary,
    lineHeight: 22,
  },
  errorContainer: {
    backgroundColor: colors.error[50],
    borderColor: colors.error[200],
    borderWidth: 1,
    borderRadius: borderRadius.md,
    padding: spacing[3],
    marginBottom: spacing[4],
  },
  errorText: {
    fontSize: typography.body.small.fontSize,
    color: colors.error[600],
    textAlign: 'center',
  },
  formContainer: {
    marginBottom: spacing[8],
  },
  sendButton: {
    marginTop: spacing[6],
    marginBottom: spacing[4],
    ...shadows.md,
  },
  backToLogin: {
    alignItems: 'center',
    paddingVertical: spacing[3],
  },
  backToLoginText: {
    fontSize: typography.body.medium.fontSize,
    color: colors.primary[500],
    fontWeight: '500',
  },
  helpSection: {
    backgroundColor: colors.background.surface,
    borderRadius: borderRadius.lg,
    padding: spacing[6],
    marginBottom: spacing[6],
  },
  helpTitle: {
    fontSize: typography.heading.h3.fontSize,
    fontWeight: typography.heading.h3.fontWeight,
    color: colors.text.primary,
    marginBottom: spacing[3],
  },
  helpText: {
    fontSize: typography.body.medium.fontSize,
    color: colors.text.secondary,
    lineHeight: 22,
  },
  successCard: {
    backgroundColor: colors.white,
    borderRadius: borderRadius.xl,
    padding: spacing[8],
    marginTop: -spacing[8],
    marginHorizontal: spacing[6],
    ...shadows.lg,
  },
  successTitle: {
    fontSize: typography.heading.h2.fontSize,
    fontWeight: typography.heading.h2.fontWeight,
    color: colors.success[600],
    marginBottom: spacing[4],
  },
  successMessage: {
    fontSize: typography.body.medium.fontSize,
    color: colors.text.secondary,
    lineHeight: 24,
    marginBottom: spacing[8],
  },
  backButton: {
    ...shadows.md,
  },
  bottomDecoration: {
    alignItems: 'center',
    paddingVertical: spacing[4],
  },
  decorationText: {
    fontSize: typography.body.small.fontSize,
    color: colors.text.hint,
  },
}); 