import React from 'react';
import { View } from 'react-native';

import { useAuthFlow } from '../../contexts/AuthFlowContext';
import { PhoneLoginComponent } from './components/PhoneLoginComponent';
import { OtpVerificationComponent } from './components/OtpVerificationComponent';
import { RegisterComponent } from './components/RegisterComponent';

export const AuthScreen: React.FC = () => {
  const { currentStep } = useAuthFlow();

  console.log('🔐 AuthScreen: Rendering with step:', currentStep);

  const renderCurrentStep = () => {
    switch (currentStep) {
      case 'phone':
        return <PhoneLoginComponent />;
      case 'otp':
        return <OtpVerificationComponent />;
      case 'register':
        return <RegisterComponent />;
      case 'forgotPassword':
        return <PhoneLoginComponent />; // For now, use phone login
      default:
        return <PhoneLoginComponent />;
    }
  };

  return (
    <View style={{ flex: 1 }}>
      {renderCurrentStep()}
    </View>
  );
}; 