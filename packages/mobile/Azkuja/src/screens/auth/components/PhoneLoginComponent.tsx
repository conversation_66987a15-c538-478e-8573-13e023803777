import React, { useState } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  StatusBar,
  KeyboardAvoidingView,
  Platform,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

import { GlobalText } from '../../../components/GlobalText';
import { Button, Input } from '../../../components/ui';
import { useTranslation } from '../../../i18n';
import { useAuth, UserNotFoundError } from '../../../contexts/AuthContext';
import { useAuthFlow } from '../../../contexts/AuthFlowContext';

export const PhoneLoginComponent: React.FC = () => {
  const { t, isRTL } = useTranslation();
  const { login, isLoading } = useAuth();
  const { setOtpStep, setRegisterStep } = useAuthFlow();
  
  const [phoneNumber, setPhoneNumber] = useState('');
  const [error, setError] = useState('');

  const validatePhoneNumber = (phone: string): boolean => {
    const phoneRegex = /^07\d{8}$/;
    return phoneRegex.test(phone);
  };

  const handlePhoneSubmit = async () => {
    console.log('📞 PhoneLoginComponent: handlePhoneSubmit called with phone:', phoneNumber);
    
    // Clear previous errors
    setError('');

    // Validation
    if (!phoneNumber) {
      console.log('❌ PhoneLoginComponent: Phone number is empty');
      setError('شماره تلفن الزامی است');
      return;
    }

    if (!validatePhoneNumber(phoneNumber)) {
      console.log('❌ PhoneLoginComponent: Phone number format is invalid');
      setError('شماره تلفن باید ۱۰ رقم باشد و با ۰۷ شروع شود');
      return;
    }

    console.log('✅ PhoneLoginComponent: Phone validation passed, calling login...');

    try {
      const response = await login({ phone_number: phoneNumber });
      console.log('✅ PhoneLoginComponent: Login successful, response:', response);
      
      // Move to OTP step using context
      setOtpStep(phoneNumber, response.message);
      console.log('🔄 PhoneLoginComponent: Moved to OTP step');
      
    } catch (error) {
      console.error('💥 PhoneLoginComponent: Login error:', error);
      
      // Handle user not found error - redirect to register
      if (error instanceof UserNotFoundError) {
        console.log('👤 PhoneLoginComponent: User not found - showing register redirect dialog');
        Alert.alert(
          'حساب کاربری یافت نشد',
          'این شماره تلفن ثبت نام نشده است. آیا می‌خواهید حساب جدید ایجاد کنید؟',
          [
            {
              text: 'انصراف',
              style: 'cancel',
            },
            {
              text: 'ثبت نام',
              onPress: () => {
                console.log('🔄 PhoneLoginComponent: User chose to register');
                setRegisterStep();
              },
            },
          ],
          { cancelable: true }
        );
        return;
      }
      
      // Handle other errors
      const errorMessage = error instanceof Error ? error.message : 'خطا در ارسال کد';
      setError(errorMessage);
      
      // Also show alert for critical errors
      Alert.alert('خطا', errorMessage);
    }
  };

  const handleNavigateToRegister = () => {
    console.log('🔄 PhoneLoginComponent: Navigating to Register');
    setRegisterStep();
  };

  console.log('🎨 PhoneLoginComponent: Rendering with loading:', isLoading);

  return (
    <SafeAreaView style={styles.container} edges={['top', 'left', 'right']}>
      <StatusBar barStyle="light-content" backgroundColor="#e6034b" />
      
      <View style={styles.header}>
        <View style={styles.headerContent}>
          <View style={styles.logoContainer}>
            <View style={styles.logoCircle}>
              <GlobalText style={styles.logoText}>📱</GlobalText>
            </View>
            <GlobalText style={styles.appName}>Azkuja</GlobalText>
            <GlobalText style={styles.appTagline}>
              خوش آمدید
            </GlobalText>
          </View>
        </View>
      </View>

      <KeyboardAvoidingView 
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardView}
      >
        <ScrollView 
          style={styles.content}
          contentContainerStyle={styles.contentContainer}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
        >
          <View style={styles.welcomeCard}>
            <GlobalText style={styles.welcomeTitle}>
              ورود
            </GlobalText>
            <GlobalText style={styles.welcomeSubtitle}>
              شماره تلفن خود را وارد کنید
            </GlobalText>
          </View>

          {error && (
            <View style={styles.errorContainer}>
              <GlobalText style={styles.errorText}>{error}</GlobalText>
            </View>
          )}

          <View style={styles.formContainer}>
            <Input
              label="شماره تلفن"
              placeholder="07XXXXXXXX"
              value={phoneNumber}
              onChangeText={(value) => {
                setPhoneNumber(value);
                setError('');
              }}
              type="phone"
            />
            
            <View style={styles.helpContainer}>
              <GlobalText style={styles.helpText}>
                شماره تلفن باید ۱۰ رقم باشد و با ۰۷ شروع شود
              </GlobalText>
            </View>

            <Button
              title="ارسال کد"
              onPress={handlePhoneSubmit}
              loading={isLoading}
              fullWidth
              size="large"
              style={styles.submitButton}
            />
          </View>

          <View style={styles.registerContainer}>
            <GlobalText style={styles.registerText}>حساب کاربری ندارید؟ </GlobalText>
            <TouchableOpacity onPress={handleNavigateToRegister}>
              <GlobalText style={styles.registerLink}>ثبت نام</GlobalText>
            </TouchableOpacity>
          </View>

          <View style={styles.footer}>
            <GlobalText style={styles.footerText}>
              احراز هویت امن افغانی
            </GlobalText>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FAFAFA',
  },
  header: {
    minHeight: 180,
    backgroundColor: '#e6034b',
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: 16,
    paddingBottom: 24,
    elevation: 8,
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.15,
    shadowRadius: 12,
  },
  headerContent: {
    alignItems: 'center',
    paddingHorizontal: 24,
    width: '100%',
  },
  logoContainer: {
    alignItems: 'center',
    marginTop: 16,
  },
  logoCircle: {
    width: 70,
    height: 70,
    borderRadius: 35,
    backgroundColor: '#FFFFFF',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
    elevation: 4,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 6,
  },
  logoText: {
    fontSize: 28,
  },
  appName: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 4,
  },
  appTagline: {
    fontSize: 16,
    color: '#FFE0E6',
    textAlign: 'center',
  },
  keyboardView: {
    flex: 1,
    backgroundColor: '#FAFAFA',
  },
  content: {
    flex: 1,
  },
  contentContainer: {
    flexGrow: 1,
    paddingHorizontal: 24,
  },
  welcomeCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 24,
    padding: 24,
    marginTop: -32,
    marginBottom: 24,
    elevation: 8,
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.15,
    shadowRadius: 12,
  },
  welcomeTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#212121',
    marginBottom: 8,
  },
  welcomeSubtitle: {
    fontSize: 16,
    color: '#757575',
    lineHeight: 22,
  },
  errorContainer: {
    backgroundColor: '#FFEBEE',
    borderColor: '#EF9A9A',
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
  },
  errorText: {
    fontSize: 14,
    color: '#C62828',
    textAlign: 'center',
  },
  formContainer: {
    marginBottom: 24,
  },
  helpContainer: {
    backgroundColor: '#F5F5F5',
    borderRadius: 8,
    padding: 12,
    marginTop: 8,
    marginBottom: 16,
  },
  helpText: {
    fontSize: 14,
    color: '#757575',
    textAlign: 'center',
  },
  submitButton: {
    marginTop: 8,
    elevation: 4,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 6,
  },
  registerContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 'auto',
    paddingVertical: 24,
  },
  registerText: {
    fontSize: 16,
    color: '#757575',
  },
  registerLink: {
    fontSize: 16,
    color: '#e6034b',
    fontWeight: '600',
    marginLeft: 4,
  },
  footer: {
    alignItems: 'center',
    paddingVertical: 16,
  },
  footerText: {
    fontSize: 12,
    color: '#BDBDBD',
  },
}); 