import React, { useState, useEffect } from 'react';
import {
  View,
  ScrollView,
  StyleSheet,
  Alert,
  RefreshControl,
  TouchableOpacity,
  Image,
  Dimensions,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';

import { GlobalText } from '../components/GlobalText';
import { Input } from '../components/ui/Input';
import { Button } from '../components/ui/Button';
import { FavoriteButton } from '../components/FavoriteButton';
import { SearchBar } from '../components/SearchBar';
import { useTranslation } from '../i18n';
import { useSearch } from '../contexts/SearchContext';
import apiService, { Restaurant, Category, RestaurantFilters } from '../lib/api';

type RootStackParamList = {
  RestaurantDetail: { restaurant: Restaurant };
  SearchFilters: { 
    onApplyFilters: (filters: RestaurantFilters) => void;
    currentFilters: RestaurantFilters;
  };
};

type NavigationProp = StackNavigationProp<RootStackParamList>;

const { width } = Dimensions.get('window');
const CARD_WIDTH = width - 32;

export const ExploreScreen: React.FC = () => {
  const { t } = useTranslation();
  const navigation = useNavigation<NavigationProp>();

  const [restaurants, setRestaurants] = useState<Restaurant[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [filters, setFilters] = useState<RestaurantFilters>({});

  useEffect(() => {
    loadData();
  }, []);

  useEffect(() => {
    if (searchQuery || selectedCategory || Object.keys(filters).length > 0) {
      searchRestaurants();
    } else {
      loadRestaurants();
    }
  }, [searchQuery, selectedCategory, filters]);

  const loadData = async () => {
    try {
      setLoading(true);
      const [restaurantsData, categoriesData] = await Promise.all([
        apiService.getRestaurants({ limit: 20, sortBy: 'avg_rating', sortOrder: 'DESC' }),
        apiService.getCategories()
      ]);
      
      setRestaurants(restaurantsData);
      setCategories(categoriesData);
    } catch (error) {
      console.error('Error loading data:', error);
      Alert.alert(
        'خطا',
        'خطا در بارگذاری اطلاعات'
      );
    } finally {
      setLoading(false);
    }
  };

  const loadRestaurants = async () => {
    try {
      const data = await apiService.getRestaurants({ 
        limit: 20, 
        sortBy: 'avg_rating', 
        sortOrder: 'DESC' 
      });
      setRestaurants(data);
    } catch (error) {
      console.error('Error loading restaurants:', error);
    }
  };

  const searchRestaurants = async () => {
    try {
      const searchFilters: RestaurantFilters = {
        ...filters,
        search: searchQuery || undefined,
        category_id: selectedCategory || undefined,
        limit: 20,
        sortBy: 'avg_rating',
        sortOrder: 'DESC'
      };

      const data = await apiService.getRestaurants(searchFilters);
      setRestaurants(data);
    } catch (error) {
      console.error('Error searching restaurants:', error);
      Alert.alert(
        'خطا',
        'خطا در جستجو'
      );
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadData();
    setRefreshing(false);
  };

  const handleRestaurantPress = (restaurant: Restaurant) => {
    navigation.navigate('RestaurantDetail', { restaurant });
  };

  const handleFiltersPress = () => {
    navigation.navigate('SearchFilters', {
      currentFilters: filters,
      onApplyFilters: setFilters
    });
  };

  const clearFilters = () => {
    setSearchQuery('');
    setSelectedCategory(null);
    setFilters({});
  };

  const getPriceRangeText = (priceRange: number) => {
    return '؋'.repeat(priceRange);
  };

  const renderStars = (rating: number) => {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;

    for (let i = 0; i < fullStars; i++) {
      stars.push(
        <Ionicons key={i} name="star" size={12} color="#FFB300" />
      );
    }

    if (hasHalfStar) {
      stars.push(
        <Ionicons key="half" name="star-half" size={12} color="#FFB300" />
      );
    }

    const emptyStars = 5 - Math.ceil(rating);
    for (let i = 0; i < emptyStars; i++) {
      stars.push(
        <Ionicons key={`empty-${i}`} name="star-outline" size={12} color="#E0E0E0" />
      );
    }

    return stars;
  };

  const renderCategoryChip = (category: Category, isSelected: boolean) => (
    <TouchableOpacity
      key={category.id}
      style={[
        styles.categoryChip,
        { 
          backgroundColor: isSelected ? '#e6034b' : '#FFFFFF',
          borderColor: isSelected ? '#e6034b' : '#E0E0E0'
        }
      ]}
      onPress={() => setSelectedCategory(isSelected ? null : category.id)}
    >
      <GlobalText 
        variant="body" 
        style={{ 
          color: isSelected ? '#FFFFFF' : '#212121',
          fontSize: 12
        }}
      >
        {category.name}
      </GlobalText>
    </TouchableOpacity>
  );

  const renderRestaurantCard = (restaurant: Restaurant) => (
    <TouchableOpacity
      key={restaurant.id}
      style={styles.restaurantCard}
      onPress={() => handleRestaurantPress(restaurant)}
    >
      <Image
        source={{ 
          uri: restaurant.photos?.[0]?.url || 'https://images.unsplash.com/photo-1555396273-367ea4eb4db5?w=400'
        }}
        style={styles.restaurantImage}
        resizeMode="cover"
      />
      
      {restaurant.is_featured && (
        <View style={styles.featuredBadge}>
          <GlobalText variant="body" style={styles.featuredText}>
            ویژه
          </GlobalText>
        </View>
      )}

      <View style={styles.favoriteButtonContainer}>
        <FavoriteButton
          restaurantId={restaurant.id}
          restaurantName={restaurant.name}
          size={20}
          showFeedback={false}
        />
      </View>

      <View style={styles.restaurantInfo}>
        <View style={styles.restaurantHeader}>
          <GlobalText variant="heading" style={styles.restaurantName}>
            {restaurant.name}
          </GlobalText>
          <View style={styles.ratingContainer}>
            {renderStars(restaurant.avg_rating)}
            <GlobalText variant="body" style={styles.ratingText}>
              {restaurant.avg_rating.toFixed(1)}
            </GlobalText>
          </View>
        </View>

        <GlobalText 
          variant="body" 
          style={styles.restaurantDescription}
          numberOfLines={2}
        >
          {restaurant.description}
        </GlobalText>

        <View style={styles.restaurantMeta}>
          <View style={styles.metaItem}>
            <Ionicons name="location-outline" size={14} color="#e6034b" />
            <GlobalText variant="body" style={styles.metaText}>
              {restaurant.city}
            </GlobalText>
          </View>
          
          <View style={styles.metaItem}>
            <Ionicons name="card-outline" size={14} color="#e6034b" />
            <GlobalText variant="body" style={styles.metaText}>
              {getPriceRangeText(restaurant.price_range)}
            </GlobalText>
          </View>

          <View style={styles.metaItem}>
            <Ionicons name="call-outline" size={14} color="#e6034b" />
            <GlobalText variant="body" style={styles.metaText}>
              {restaurant.phone}
            </GlobalText>
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );

  const hasActiveFilters = searchQuery || selectedCategory || Object.keys(filters).length > 0;

  return (
    <SafeAreaView style={styles.container} edges={['top', 'left', 'right']}>
      {/* Header */}
      <View style={styles.header}>
        <GlobalText variant="heading" style={styles.headerTitle}>
          کاوش رستوران‌ها
        </GlobalText>
        <TouchableOpacity onPress={handleFiltersPress}>
          <Ionicons name="options-outline" size={24} color="#e6034b" />
        </TouchableOpacity>
      </View>

      {/* Search Bar */}
      <SearchBar
        placeholder="جستجوی رستوران، غذا..."
        onSearchPress={() => {
          // Handle search results using existing search functionality
          searchRestaurants();
        }}
        style={styles.searchContainer}
      />

      {/* Categories */}
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        style={styles.categoriesContainer}
        contentContainerStyle={styles.categoriesContent}
      >
        {categories.map(category => 
          renderCategoryChip(category, selectedCategory === category.id)
        )}
      </ScrollView>

      {/* Clear Filters */}
      {hasActiveFilters && (
        <View style={styles.clearFiltersContainer}>
          <Button
            title="پاک کردن فیلترها"
            onPress={clearFilters}
            variant="outline"
            size="small"
          />
        </View>
      )}

      {/* Restaurants List */}
      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={['#e6034b']}
          />
        }
      >
        {loading ? (
          <View style={styles.loadingContainer}>
            <GlobalText variant="body" style={styles.loadingText}>
              در حال بارگذاری...
            </GlobalText>
          </View>
        ) : restaurants.length === 0 ? (
          <View style={styles.emptyContainer}>
            <Ionicons name="restaurant-outline" size={64} color="#E0E0E0" />
            <GlobalText variant="heading" style={styles.emptyTitle}>
              رستورانی یافت نشد
            </GlobalText>
            <GlobalText variant="body" style={styles.emptySubtitle}>
              با تغییر فیلترها یا جستجوی جدید دوباره تلاش کنید
            </GlobalText>
          </View>
        ) : (
          <View style={styles.restaurantsContainer}>
            {restaurants.map(renderRestaurantCard)}
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FAFAFA',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#FFFFFF',
    elevation: 2,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  headerTitle: {
    color: '#212121',
    fontSize: 24,
  },
  searchContainer: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#FFFFFF',
  },
  categoriesContainer: {
    marginBottom: 8,
    backgroundColor: '#FFFFFF',
  },
  categoriesContent: {
    paddingHorizontal: 16,
    paddingBottom: 8,
  },
  categoryChip: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    borderWidth: 1,
    marginRight: 8,
  },
  clearFiltersContainer: {
    paddingHorizontal: 16,
    paddingBottom: 8,
    backgroundColor: '#FFFFFF',
  },
  scrollView: {
    flex: 1,
  },
  loadingContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 40,
  },
  loadingText: {
    color: '#757575',
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 40,
    paddingHorizontal: 16,
  },
  emptyTitle: {
    color: '#757575',
    marginTop: 16,
  },
  emptySubtitle: {
    color: '#757575',
    textAlign: 'center',
    marginTop: 8,
  },
  restaurantsContainer: {
    padding: 16,
  },
  restaurantCard: {
    borderRadius: 12,
    marginBottom: 16,
    backgroundColor: '#FFFFFF',
    elevation: 2,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    overflow: 'hidden',
  },
  restaurantImage: {
    width: '100%',
    height: 200,
  },
  featuredBadge: {
    position: 'absolute',
    top: 8,
    right: 8,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
    backgroundColor: '#FF9800',
  },
  featuredText: {
    color: '#FFFFFF',
    fontSize: 10,
  },
  favoriteButtonContainer: {
    position: 'absolute',
    top: 8,
    left: 8,
  },
  restaurantInfo: {
    padding: 16,
  },
  restaurantHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  restaurantName: {
    color: '#212121',
    fontSize: 16,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  ratingText: {
    color: '#212121',
    fontSize: 12,
    marginLeft: 4,
  },
  restaurantDescription: {
    color: '#757575',
    fontSize: 12,
    marginTop: 4,
  },
  restaurantMeta: {
    flexDirection: 'row',
    marginTop: 8,
    flexWrap: 'wrap',
  },
  metaItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 12,
    marginBottom: 4,
  },
  metaText: {
    color: '#757575',
    fontSize: 11,
    marginLeft: 2,
  },
}); 