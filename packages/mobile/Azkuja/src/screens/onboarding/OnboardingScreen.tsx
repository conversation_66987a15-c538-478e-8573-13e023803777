import React, { useState, useRef } from 'react';
import {
  View,
  StyleSheet,
  Dimensions,
  ScrollView,
  StatusBar,
  TouchableOpacity,
  FlatList,
  ViewToken,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation } from '@react-navigation/native';
import type { NavigationProp } from '@react-navigation/native';

import { colors, typography, spacing, borderRadius, shadows, fonts } from '../../theme';
import { Button } from '../../components/ui';
import { Text } from '../../components/GlobalText';
import { useTranslation } from '../../i18n';
import { useAuth } from '../../contexts/AuthContext';

interface OnboardingSlide {
  id: number;
  icon: string;
  title: string;
  titleDari: string;
  titlePashto: string;
  description: string;
  descriptionDari: string;
  descriptionPashto: string;
  backgroundColor: string;
}

interface OnboardingScreenProps {
  onComplete?: () => void;
  onSkip?: () => void;
}

// Navigation type for the auth stack
type AuthStackParamList = {
  Onboarding: undefined;
  Login: undefined;
  Register: undefined;
  ForgotPassword: undefined;
};

const { width: screenWidth } = Dimensions.get('window');

export const OnboardingScreen: React.FC<OnboardingScreenProps> = ({
  onComplete,
  onSkip,
}) => {
  const { t, isRTL, language } = useTranslation();
  const navigation = useNavigation<NavigationProp<AuthStackParamList>>();
  const { completeOnboarding } = useAuth();

  const [currentSlide, setCurrentSlide] = useState(0);
  const flatListRef = useRef<FlatList<OnboardingSlide>>(null);

  const slides: OnboardingSlide[] = [
    {
      id: 1,
      icon: '🍽️',
      title: 'Discover Great Food',
      titleDari: 'غذاهای عالی کشف کنید',
      titlePashto: 'د ښه خواړو کشف وکړئ',
      description: 'Find the best restaurants and dishes in your area with our curated selection of local favorites.',
      descriptionDari: 'بهترین رستوران‌ها و غذاهای منطقه خود را با مجموعه منتخب ما از مورد علاقه‌های محلی پیدا کنید.',
      descriptionPashto: 'زموږ د محلي غوره توبونو ټاکل شوي ټولګه سره ستاسو په سیمه کې غوره رستورانتونه او غذاګانې ومومئ.',
      backgroundColor: colors.primary[500],
    },
    {
      id: 2,
      icon: '🚚',
      title: 'Fast Delivery',
      titleDari: 'تحویل سریع',
      titlePashto: 'ګړندۍ تحویلي',
      description: 'Get your favorite meals delivered quickly and safely to your doorstep by our trusted delivery partners.',
      descriptionDari: 'غذاهای مورد علاقه خود را به سرعت و با امنیت توسط شرکای تحویل مورد اعتماد ما در خانه دریافت کنید.',
      descriptionPashto: 'ستاسو د مینې ډکې خواړه زموږ د اعتماد وړ تحویلي شریکانو لخوا ستاسو کور ته ګړندي او خوندي تحویل کړئ.',
      backgroundColor: colors.success[500],
    },
    {
      id: 3,
      icon: '💳',
      title: 'Easy Payment',
      titleDari: 'پرداخت آسان',
      titlePashto: 'اسانه تادیه',
      description: 'Pay securely with multiple payment options including cash, card, and digital wallets.',
      descriptionDari: 'با گزینه‌های مختلف پرداخت شامل نقد، کارت و کیف پول دیجیتال به صورت امن پرداخت کنید.',
      descriptionPashto: 'د څو ورکړې لارو سره په خوندي ډول ورکړه وکړئ پشمول د نغدو پیسو، کارت، او دیجیټل پاکټونو.',
      backgroundColor: colors.warning[500],
    },
    {
      id: 4,
      icon: '🏪',
      title: 'Table Reservations',
      titleDari: 'رزرو میز',
      titlePashto: 'د میز ریزرویشن',
      description: 'Reserve tables at your favorite restaurants and enjoy dining out with friends and family.',
      descriptionDari: 'در رستوران‌های مورد علاقه خود میز رزرو کنید و از غذا خوردن با دوستان و خانواده لذت ببرید.',
      descriptionPashto: 'ستاسو د غوره رستورانتونو کې میزونه ریزرو کړئ او د ملګرو او کورنۍ سره د خوړو څخه خوند واخلئ.',
      backgroundColor: colors.secondary[500],
    },
  ];

  const getSlideContent = (slide: OnboardingSlide) => {
    let title = slide.title;
    let description = slide.description;

    if (language === 'fa-AF') {
      title = slide.titleDari;
      description = slide.descriptionDari;
    } else if (language === 'ps-AF') {
      title = slide.titlePashto;
      description = slide.descriptionPashto;
    }

    return { title, description };
  };

  const nextSlide = async () => {
    console.log('🎯 OnboardingScreen: Next slide pressed, current slide:', currentSlide, 'total slides:', slides.length);
    
    try {
      if (currentSlide < slides.length - 1) {
        const nextIndex = currentSlide + 1;
        setCurrentSlide(nextIndex);
        flatListRef.current?.scrollToIndex({ index: nextIndex, animated: true });
        console.log('📱 OnboardingScreen: Moved to slide', nextIndex);
      } else {
        // Navigate to login screen or call onComplete
        console.log('🚀 OnboardingScreen: Get Started pressed - marking onboarding complete and navigating to Login');
        
        // Mark onboarding as completed
        await completeOnboarding();
        
        if (onComplete) {
          console.log('📞 OnboardingScreen: Calling onComplete callback');
          onComplete();
        } else {
          console.log('🧭 OnboardingScreen: Using navigation.navigate to Login');
          navigation.navigate('Login');
        }
      }
    } catch (error) {
      console.error('💥 OnboardingScreen: Error in nextSlide:', error);
      Alert.alert('خطا', 'مشکلی پیش آمده است. لطفاً دوباره تلاش کنید.');
    }
  };

  const handleSkip = async () => {
    console.log('⏭️ OnboardingScreen: Skip pressed - marking onboarding complete and navigating to Login');
    try {
      // Mark onboarding as completed
      await completeOnboarding();
      
      if (onSkip) {
        console.log('📞 OnboardingScreen: Calling onSkip callback');
        onSkip();
      } else {
        console.log('🧭 OnboardingScreen: Using navigation.navigate to Login');
        navigation.navigate('Login');
      }
    } catch (error) {
      console.error('💥 OnboardingScreen: Error in handleSkip:', error);
      Alert.alert('خطا', 'مشکلی پیش آمده است. لطفاً دوباره تلاش کنید.');
    }
  };

  const previousSlide = () => {
    try {
      if (currentSlide > 0) {
        const prevIndex = currentSlide - 1;
        setCurrentSlide(prevIndex);
        flatListRef.current?.scrollToIndex({ index: prevIndex, animated: true });
        console.log('📱 OnboardingScreen: Moved to slide', prevIndex);
      }
    } catch (error) {
      console.error('💥 OnboardingScreen: Error in previousSlide:', error);
    }
  };

  const onViewableItemsChanged = ({ viewableItems }: { viewableItems: ViewToken[] }) => {
    if (viewableItems.length > 0) {
      const index = viewableItems[0].index;
      if (index !== null) {
        setCurrentSlide(index);
      }
    }
  };

  const renderSlide = ({ item }: { item: OnboardingSlide }) => {
    const { title, description } = getSlideContent(item);

    return (
      <View style={[styles.slide, { backgroundColor: item.backgroundColor }]}>
        <View style={styles.slideContent}>
          {/* Icon */}
          <View style={styles.iconContainer}>
            <Text style={styles.slideIcon}>{item.icon}</Text>
          </View>

          {/* Content */}
          <View style={styles.textContainer}>
            <Text style={styles.slideTitle} variant="heading">
              {title}
            </Text>
            <Text style={styles.slideDescription} variant="body">
              {description}
            </Text>
          </View>
        </View>
      </View>
    );
  };

  const renderPagination = () => (
    <View style={styles.pagination}>
      {slides.map((_, index) => (
        <View
          key={index}
          style={[
            styles.paginationDot,
            currentSlide === index && styles.paginationDotActive,
          ]}
        />
      ))}
    </View>
  );

  console.log('🎨 OnboardingScreen: Rendering with current slide:', currentSlide);

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar 
        barStyle="light-content" 
        backgroundColor={slides[currentSlide]?.backgroundColor || colors.primary[500]} 
      />
      
      {/* Skip Button */}
      <TouchableOpacity 
        style={styles.skipButton}
        onPress={handleSkip}
      >
        <Text style={styles.skipText} variant="caption">
          {isRTL ? 'رد کردن' : 'Skip'}
        </Text>
      </TouchableOpacity>

      {/* Slides */}
      <FlatList
        ref={flatListRef}
        data={slides}
        renderItem={renderSlide}
        keyExtractor={(item) => item.id.toString()}
        horizontal
        pagingEnabled
        showsHorizontalScrollIndicator={false}
        onViewableItemsChanged={onViewableItemsChanged}
        viewabilityConfig={{ itemVisiblePercentThreshold: 50 }}
        getItemLayout={(_, index) => ({
          length: screenWidth,
          offset: screenWidth * index,
          index,
        })}
      />

      {/* Bottom Controls */}
      <View style={styles.bottomContainer}>
        {/* Pagination */}
        {renderPagination()}

        {/* Navigation Buttons */}
        <View style={styles.buttonContainer}>
          {/* Previous Button */}
          {currentSlide > 0 && (
            <TouchableOpacity 
              style={styles.navButton}
              onPress={previousSlide}
            >
              <Text style={styles.navButtonText} variant="button">
                {isRTL ? 'قبلی' : 'Previous'}
              </Text>
            </TouchableOpacity>
          )}

          {/* Spacer */}
          <View style={styles.buttonSpacer} />

          {/* Next/Get Started Button */}
          <Button
            title={
              currentSlide === slides.length - 1
                ? (isRTL ? 'شروع کنید' : 'Get Started')
                : (isRTL ? 'بعدی' : 'Next')
            }
            onPress={nextSlide}
            variant="secondary"
            size="large"
            style={styles.nextButton}
          />
        </View>
      </View>

      {/* Afghan Flag Footer */}
      <View style={styles.footer}>
        <Text style={styles.footerText} variant="caption">
          🇦🇫 {isRTL ? 'به آذوقه خوش آمدید' : 'Welcome to Azkuja'}
        </Text>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.primary[500],
  },
  skipButton: {
    position: 'absolute',
    top: 60,
    right: spacing[6],
    zIndex: 1,
    paddingHorizontal: spacing[4],
    paddingVertical: spacing[2],
  },
  skipText: {
    fontSize: typography.body.medium.fontSize,
    color: colors.white,
    fontWeight: '500',
  },
  slide: {
    width: screenWidth,
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: spacing[8],
  },
  slideContent: {
    alignItems: 'center',
    maxWidth: 340,
  },
  iconContainer: {
    width: 140,
    height: 140,
    borderRadius: 70,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: spacing[8],
    ...shadows.lg,
  },
  slideIcon: {
    fontSize: 64,
  },
  textContainer: {
    alignItems: 'center',
  },
  slideTitle: {
    fontSize: typography.heading.h1.fontSize,
    fontWeight: typography.heading.h1.fontWeight,
    color: colors.white,
    marginBottom: spacing[4],
    textAlign: 'center',
  },
  slideDescription: {
    fontSize: typography.body.large.fontSize,
    color: colors.white,
    textAlign: 'center',
    lineHeight: 28,
    opacity: 0.9,
  },
  bottomContainer: {
    paddingHorizontal: spacing[6],
    paddingBottom: spacing[6],
  },
  pagination: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: spacing[8],
  },
  paginationDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    marginHorizontal: spacing[1],
  },
  paginationDotActive: {
    backgroundColor: colors.white,
    width: 24,
  },
  buttonContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  navButton: {
    paddingHorizontal: spacing[6],
    paddingVertical: spacing[3],
  },
  navButtonText: {
    fontSize: typography.body.medium.fontSize,
    color: colors.white,
    fontWeight: '500',
  },
  buttonSpacer: {
    flex: 1,
  },
  nextButton: {
    backgroundColor: colors.white,
    minWidth: 120,
    ...shadows.md,
  },
  footer: {
    alignItems: 'center',
    paddingVertical: spacing[3],
  },
  footerText: {
    fontSize: typography.body.small.fontSize,
    color: 'rgba(255, 255, 255, 0.7)',
  },
});
