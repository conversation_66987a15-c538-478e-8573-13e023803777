import React, { useState } from 'react';
import {
  View,
  ScrollView,
  StyleSheet,
  Alert,
  TouchableOpacity,
  Image,
  TextInput,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';

import { GlobalText } from '../components/GlobalText';
import { Button } from '../components/ui/Button';
import { useTranslation } from '../i18n';
import { useCart, CartItem } from '../contexts/CartContext';
import { usePayment } from '../contexts/PaymentContext';

type RootStackParamList = {
  OrderConfirmation: { orderId: string };
};

type NavigationProp = StackNavigationProp<RootStackParamList>;

export const CartScreen: React.FC = () => {
  const { t } = useTranslation();
  const navigation = useNavigation<NavigationProp>();
  const { 
    cartItems, 
    restaurant, 
    isLoading, 
    removeFromCart, 
    updateCartItem, 
    clearCart, 
    getCartTotal, 
    getCartItemCount,
    createOrder 
  } = useCart();
  const { 
    paymentMethods, 
    defaultPaymentMethod, 
    processPayment 
  } = usePayment();

  const [deliveryAddress, setDeliveryAddress] = useState('');
  const [specialInstructions, setSpecialInstructions] = useState('');
  const [isCreatingOrder, setIsCreatingOrder] = useState(false);
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState(defaultPaymentMethod);

  const handleQuantityChange = async (itemId: string, newQuantity: number) => {
    try {
      await updateCartItem(itemId, newQuantity);
    } catch (error) {
      Alert.alert('خطا', 'خطا در به‌روزرسانی سبد خرید');
    }
  };

  const handleRemoveItem = async (itemId: string) => {
    Alert.alert(
      'حذف آیتم',
      'آیا مطمئن هستید که می‌خواهید این آیتم را از سبد خرید حذف کنید؟',
      [
        { text: 'لغو', style: 'cancel' },
        { 
          text: 'حذف', 
          style: 'destructive',
          onPress: async () => {
            try {
              await removeFromCart(itemId);
            } catch (error) {
              Alert.alert('خطا', 'خطا در حذف آیتم');
            }
          }
        }
      ]
    );
  };

  const handleClearCart = () => {
    Alert.alert(
      'پاک کردن سبد خرید',
      'آیا مطمئن هستید که می‌خواهید تمام آیتم‌ها را از سبد خرید حذف کنید؟',
      [
        { text: 'لغو', style: 'cancel' },
        { 
          text: 'پاک کردن', 
          style: 'destructive',
          onPress: async () => {
            try {
              await clearCart();
            } catch (error) {
              Alert.alert('خطا', 'خطا در پاک کردن سبد خرید');
            }
          }
        }
      ]
    );
  };

  const handleCreateOrder = async () => {
    if (!deliveryAddress.trim()) {
      Alert.alert('خطا', 'لطفاً آدرس تحویل را وارد کنید');
      return;
    }

    if (!selectedPaymentMethod) {
      Alert.alert('خطا', 'لطفاً روش پرداخت را انتخاب کنید');
      return;
    }

    try {
      setIsCreatingOrder(true);
      
      // Create order first
      const order = await createOrder(deliveryAddress, specialInstructions, selectedPaymentMethod);
      
      // Process payment if not cash
      if (selectedPaymentMethod.type !== 'cash') {
        await processPayment({
          order_id: order.id,
          payment_method: selectedPaymentMethod.type,
          payment_type: 'online',
          amount: getCartTotal(),
        });
      }
      
      Alert.alert(
        'سفارش ثبت شد',
        'سفارش شما با موفقیت ثبت شد',
        [
          {
            text: 'مشاهده سفارش',
            onPress: () => navigation.navigate('OrderConfirmation', { orderId: order.id })
          }
        ]
      );
    } catch (error) {
      console.error('Error creating order:', error);
      Alert.alert('خطا', 'خطا در ثبت سفارش. لطفاً دوباره تلاش کنید.');
    } finally {
      setIsCreatingOrder(false);
    }
  };

  const renderCartItem = (item: CartItem) => (
    <View key={item.id} style={styles.cartItem}>
      <Image
        source={{ 
          uri: item.menuItem.imageUrl || 'https://images.unsplash.com/photo-1555396273-367ea4eb4db5?w=400'
        }}
        style={styles.itemImage}
        resizeMode="cover"
      />
      
      <View style={styles.itemDetails}>
        <GlobalText variant="heading" style={styles.itemName}>
          {item.menuItem.name}
        </GlobalText>
        
        <GlobalText variant="body" style={styles.itemDescription} numberOfLines={2}>
          {item.menuItem.description}
        </GlobalText>
        
        {item.special_instructions && (
          <GlobalText variant="body" style={styles.specialInstructions}>
            توضیحات: {item.special_instructions}
          </GlobalText>
        )}
        
        <View style={styles.itemFooter}>
          <GlobalText variant="heading" style={styles.itemPrice}>
            {(item.menuItem.price * item.quantity).toLocaleString()} ؋
          </GlobalText>
          
          <View style={styles.quantityControls}>
            <TouchableOpacity
              style={styles.quantityButton}
              onPress={() => handleQuantityChange(item.id, item.quantity - 1)}
            >
              <Ionicons name="remove" size={20} color="#e6034b" />
            </TouchableOpacity>
            
            <GlobalText variant="body" style={styles.quantityText}>
              {item.quantity}
            </GlobalText>
            
            <TouchableOpacity
              style={styles.quantityButton}
              onPress={() => handleQuantityChange(item.id, item.quantity + 1)}
            >
              <Ionicons name="add" size={20} color="#e6034b" />
            </TouchableOpacity>
          </View>
          
          <TouchableOpacity
            style={styles.removeButton}
            onPress={() => handleRemoveItem(item.id)}
          >
            <Ionicons name="trash-outline" size={20} color="#e6034b" />
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );

  if (cartItems.length === 0) {
    return (
      <SafeAreaView style={styles.container} edges={['top', 'left', 'right']}>
        <View style={styles.header}>
          <GlobalText variant="heading" style={styles.headerTitle}>
            سبد خرید
          </GlobalText>
        </View>
        
        <View style={styles.emptyContainer}>
          <Ionicons name="basket-outline" size={64} color="#E0E0E0" />
          <GlobalText variant="heading" style={styles.emptyTitle}>
            سبد خرید خالی است
          </GlobalText>
          <GlobalText variant="body" style={styles.emptySubtitle}>
            برای شروع، غذاهای مورد علاقه خود را اضافه کنید
          </GlobalText>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container} edges={['top', 'left', 'right']}>
      {/* Header */}
      <View style={styles.header}>
        <GlobalText variant="heading" style={styles.headerTitle}>
          سبد خرید ({getCartItemCount()} آیتم)
        </GlobalText>
        <TouchableOpacity onPress={handleClearCart}>
          <Ionicons name="trash-outline" size={24} color="#e6034b" />
        </TouchableOpacity>
      </View>

      {/* Restaurant Info */}
      {restaurant && (
        <View style={styles.restaurantInfo}>
          <GlobalText variant="heading" style={styles.restaurantName}>
            {restaurant.name}
          </GlobalText>
          <GlobalText variant="body" style={styles.restaurantAddress}>
            {restaurant.address}
          </GlobalText>
        </View>
      )}

      <ScrollView style={styles.scrollView}>
        {/* Cart Items */}
        <View style={styles.cartItems}>
          {cartItems.map(renderCartItem)}
        </View>

        {/* Delivery Address */}
        <View style={styles.section}>
          <GlobalText variant="heading" style={styles.sectionTitle}>
            آدرس تحویل
          </GlobalText>
          <TextInput
            style={styles.textInput}
            placeholder="آدرس کامل تحویل را وارد کنید..."
            value={deliveryAddress}
            onChangeText={setDeliveryAddress}
            multiline
            numberOfLines={3}
          />
        </View>

        {/* Special Instructions */}
        <View style={styles.section}>
          <GlobalText variant="heading" style={styles.sectionTitle}>
            توضیحات اضافی (اختیاری)
          </GlobalText>
          <TextInput
            style={styles.textInput}
            placeholder="توضیحات خاص برای سفارش..."
            value={specialInstructions}
            onChangeText={setSpecialInstructions}
            multiline
            numberOfLines={2}
          />
        </View>

        {/* Payment Method Selection */}
        <View style={styles.section}>
          <GlobalText variant="heading" style={styles.sectionTitle}>
            روش پرداخت
          </GlobalText>
          {selectedPaymentMethod ? (
            <TouchableOpacity 
              style={styles.paymentMethodCard}
              onPress={() => {
                // TODO: Show payment method selection modal
                Alert.alert('انتخاب روش پرداخت', 'قابلیت انتخاب روش پرداخت به زودی اضافه خواهد شد');
              }}
            >
              <View style={styles.paymentMethodInfo}>
                <GlobalText style={styles.paymentMethodIcon}>
                  {selectedPaymentMethod.icon}
                </GlobalText>
                <View style={styles.paymentMethodDetails}>
                  <GlobalText variant="heading" style={styles.paymentMethodName}>
                    {selectedPaymentMethod.display_name}
                  </GlobalText>
                  {selectedPaymentMethod.description && (
                    <GlobalText variant="body" style={styles.paymentMethodDescription}>
                      {selectedPaymentMethod.description}
                    </GlobalText>
                  )}
                </View>
              </View>
              <Ionicons name="chevron-forward" size={20} color="#757575" />
            </TouchableOpacity>
          ) : (
            <TouchableOpacity 
              style={styles.selectPaymentMethod}
              onPress={() => {
                // TODO: Show payment method selection modal
                Alert.alert('انتخاب روش پرداخت', 'قابلیت انتخاب روش پرداخت به زودی اضافه خواهد شد');
              }}
            >
              <Ionicons name="add-circle-outline" size={24} color="#e6034b" />
              <GlobalText variant="body" style={styles.selectPaymentMethodText}>
                انتخاب روش پرداخت
              </GlobalText>
            </TouchableOpacity>
          )}
        </View>
      </ScrollView>

      {/* Order Summary */}
      <View style={styles.orderSummary}>
        <View style={styles.summaryRow}>
          <GlobalText variant="body" style={styles.summaryLabel}>
            مجموع ({getCartItemCount()} آیتم)
          </GlobalText>
          <GlobalText variant="heading" style={styles.summaryValue}>
            {getCartTotal().toLocaleString()} ؋
          </GlobalText>
        </View>
        
        <Button
          title={isCreatingOrder ? "در حال ثبت سفارش..." : "ثبت سفارش"}
          onPress={handleCreateOrder}
          disabled={isCreatingOrder || isLoading}
          style={styles.orderButton}
        />
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FAFAFA',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#FFFFFF',
    elevation: 2,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  headerTitle: {
    color: '#212121',
    fontSize: 20,
  },
  restaurantInfo: {
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
  },
  restaurantName: {
    color: '#212121',
    fontSize: 16,
  },
  restaurantAddress: {
    color: '#757575',
    fontSize: 12,
    marginTop: 4,
  },
  scrollView: {
    flex: 1,
  },
  cartItems: {
    backgroundColor: '#FFFFFF',
    marginBottom: 8,
  },
  cartItem: {
    flexDirection: 'row',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
  },
  itemImage: {
    width: 80,
    height: 80,
    borderRadius: 8,
    marginRight: 12,
  },
  itemDetails: {
    flex: 1,
  },
  itemName: {
    color: '#212121',
    fontSize: 14,
    marginBottom: 4,
  },
  itemDescription: {
    color: '#757575',
    fontSize: 12,
    marginBottom: 4,
  },
  specialInstructions: {
    color: '#FF9800',
    fontSize: 11,
    fontStyle: 'italic',
    marginBottom: 8,
  },
  itemFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  itemPrice: {
    color: '#e6034b',
    fontSize: 14,
  },
  quantityControls: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  quantityButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: '#e6034b',
    justifyContent: 'center',
    alignItems: 'center',
  },
  quantityText: {
    marginHorizontal: 12,
    fontSize: 14,
    fontWeight: 'bold',
  },
  removeButton: {
    padding: 8,
  },
  section: {
    backgroundColor: '#FFFFFF',
    padding: 16,
    marginBottom: 8,
  },
  sectionTitle: {
    color: '#212121',
    fontSize: 16,
    marginBottom: 12,
  },
  textInput: {
    borderWidth: 1,
    borderColor: '#E0E0E0',
    borderRadius: 8,
    padding: 12,
    fontSize: 14,
    textAlignVertical: 'top',
  },
  orderSummary: {
    backgroundColor: '#FFFFFF',
    padding: 16,
    elevation: 4,
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  summaryLabel: {
    color: '#757575',
    fontSize: 14,
  },
  summaryValue: {
    color: '#212121',
    fontSize: 18,
  },
  orderButton: {
    backgroundColor: '#e6034b',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  emptyTitle: {
    color: '#757575',
    fontSize: 20,
    marginTop: 16,
    textAlign: 'center',
  },
  emptySubtitle: {
    color: '#757575',
    fontSize: 14,
    textAlign: 'center',
    marginTop: 8,
  },
  paymentMethodCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    padding: 16,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderWidth: 1,
    borderColor: '#E0E0E0',
  },
  paymentMethodInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  paymentMethodIcon: {
    fontSize: 24,
    marginRight: 12,
  },
  paymentMethodDetails: {
    flex: 1,
  },
  paymentMethodName: {
    color: '#212121',
    fontSize: 14,
  },
  paymentMethodDescription: {
    color: '#757575',
    fontSize: 12,
    marginTop: 2,
  },
  selectPaymentMethod: {
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    padding: 16,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: '#e6034b',
    borderStyle: 'dashed',
  },
  selectPaymentMethodText: {
    color: '#e6034b',
    fontSize: 14,
    marginLeft: 8,
  },
}); 