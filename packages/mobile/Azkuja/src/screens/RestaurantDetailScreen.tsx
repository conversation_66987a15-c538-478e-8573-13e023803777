import React, { useState, useEffect } from 'react';
import {
  View,
  ScrollView,
  StyleSheet,
  Alert,
  TouchableOpacity,
  Image,
  Dimensions,
  Linking,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';

import { GlobalText } from '../components/GlobalText';
import { Button } from '../components/ui/Button';
import { useTranslation } from '../i18n';
import apiService, { Restaurant, MenuItem } from '../lib/api';

type RootStackParamList = {
  RestaurantDetail: { restaurant: Restaurant };
  MenuItemDetail: { menuItem: MenuItem; restaurant: Restaurant };
};

type RestaurantDetailRouteProp = RouteProp<RootStackParamList, 'RestaurantDetail'>;
type NavigationProp = StackNavigationProp<RootStackParamList>;

const { width, height } = Dimensions.get('window');
const IMAGE_HEIGHT = 250;

export const RestaurantDetailScreen: React.FC = () => {
  const { t } = useTranslation();
  const navigation = useNavigation<NavigationProp>();
  const route = useRoute<RestaurantDetailRouteProp>();

  const [restaurant, setRestaurant] = useState<Restaurant>(route.params.restaurant);
  const [loading, setLoading] = useState(false);
  const [selectedTab, setSelectedTab] = useState<'info' | 'menu' | 'reviews'>('info');

  useEffect(() => {
    loadRestaurantDetails();
  }, []);

  const loadRestaurantDetails = async () => {
    try {
      setLoading(true);
      const detailedRestaurant = await apiService.getRestaurant(restaurant.id);
      setRestaurant(detailedRestaurant);
    } catch (error) {
      console.error('Error loading restaurant details:', error);
      Alert.alert(
        'خطا',
        'خطا در بارگذاری جزئیات رستوران'
      );
    } finally {
      setLoading(false);
    }
  };

  const handleCall = () => {
    Linking.openURL(`tel:${restaurant.phone}`);
  };

  const handleWebsite = () => {
    if (restaurant.website) {
      Linking.openURL(restaurant.website);
    }
  };

  const handleGetDirections = () => {
    if (restaurant.latitude && restaurant.longitude) {
      const url = `https://www.google.com/maps/dir/?api=1&destination=${restaurant.latitude},${restaurant.longitude}`;
      Linking.openURL(url);
    } else {
      const address = encodeURIComponent(`${restaurant.address}, ${restaurant.city}`);
      Linking.openURL(`https://www.google.com/maps/search/?api=1&query=${address}`);
    }
  };

  const handleMenuItemPress = (menuItem: MenuItem) => {
    navigation.navigate('MenuItemDetail', { menuItem, restaurant });
  };

  const getPriceRangeText = (priceRange: number) => {
    return '؋'.repeat(priceRange) + ' '.repeat(4 - priceRange);
  };

  const renderStars = (rating: number) => {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;

    for (let i = 0; i < fullStars; i++) {
      stars.push(
        <Ionicons key={i} name="star" size={16} color="#FFB300" />
      );
    }

    if (hasHalfStar) {
      stars.push(
        <Ionicons key="half" name="star-half" size={16} color="#FFB300" />
      );
    }

    const emptyStars = 5 - Math.ceil(rating);
    for (let i = 0; i < emptyStars; i++) {
      stars.push(
        <Ionicons key={`empty-${i}`} name="star-outline" size={16} color="#E0E0E0" />
      );
    }

    return stars;
  };

  const renderTabButton = (tab: 'info' | 'menu' | 'reviews', title: string) => (
    <TouchableOpacity
      style={[
        styles.tabButton,
        {
          backgroundColor: selectedTab === tab ? '#e6034b' : 'transparent',
          borderBottomColor: selectedTab === tab ? '#e6034b' : 'transparent',
        }
      ]}
      onPress={() => setSelectedTab(tab)}
    >
      <GlobalText
        variant="body"
        style={{
          color: selectedTab === tab ? '#FFFFFF' : '#757575',
          fontWeight: selectedTab === tab ? 'bold' : 'normal',
        }}
      >
        {title}
      </GlobalText>
    </TouchableOpacity>
  );

  const renderInfoTab = () => (
    <View style={styles.tabContent}>
      {/* Description */}
      {restaurant.description && (
        <View style={styles.section}>
          <GlobalText variant="heading" style={styles.sectionTitle}>
            درباره
          </GlobalText>
          <GlobalText variant="body" style={styles.sectionContent}>
            {restaurant.description}
          </GlobalText>
        </View>
      )}

      {/* Contact Info */}
      <View style={styles.section}>
        <GlobalText variant="heading" style={styles.sectionTitle}>
          اطلاعات تماس
        </GlobalText>
        
        <TouchableOpacity style={styles.contactItem} onPress={handleCall}>
          <Ionicons name="call-outline" size={20} color="#e6034b" />
          <GlobalText variant="body" style={styles.contactText}>
            {restaurant.phone}
          </GlobalText>
        </TouchableOpacity>

        <View style={styles.contactItem}>
          <Ionicons name="location-outline" size={20} color="#e6034b" />
          <View style={{ marginLeft: 12, flex: 1 }}>
            <GlobalText variant="body" style={styles.contactText}>
              {restaurant.address}
            </GlobalText>
            <GlobalText variant="body" style={styles.contactSubtext}>
              {restaurant.city}, {restaurant.state} {restaurant.zip_code}
            </GlobalText>
          </View>
        </View>

        {restaurant.email && (
          <View style={styles.contactItem}>
            <Ionicons name="mail-outline" size={20} color="#e6034b" />
            <GlobalText variant="body" style={styles.contactText}>
              {restaurant.email}
            </GlobalText>
          </View>
        )}

        {restaurant.website && (
          <TouchableOpacity style={styles.contactItem} onPress={handleWebsite}>
            <Ionicons name="globe-outline" size={20} color="#e6034b" />
            <GlobalText variant="body" style={styles.linkText}>
              مشاهده وب‌سایت
            </GlobalText>
          </TouchableOpacity>
        )}
      </View>

      {/* Hours */}
      {restaurant.hours_of_operation && (
        <View style={styles.section}>
          <GlobalText variant="heading" style={styles.sectionTitle}>
            ساعات کاری
          </GlobalText>
          <GlobalText variant="body" style={styles.sectionContent}>
            {restaurant.hours_of_operation}
          </GlobalText>
        </View>
      )}
    </View>
  );

  const renderMenuTab = () => (
    <View style={styles.tabContent}>
      {restaurant.menuItems && restaurant.menuItems.length > 0 ? (
        <View>
          {restaurant.menuItems.map((menuItem) => (
            <View
              key={menuItem.id}
              style={styles.menuItem}
            >
              {menuItem.imageUrl && (
                <Image
                  source={{ uri: menuItem.imageUrl }}
                  style={styles.menuItemImage}
                  resizeMode="cover"
                />
              )}
              
              <View style={styles.menuItemContent}>
                <View style={styles.menuItemHeader}>
                  <GlobalText variant="heading" style={styles.menuItemName}>
                    {menuItem.name}
                  </GlobalText>
                  <GlobalText variant="body" style={styles.menuItemPrice}>
                    ؋{menuItem.price}
                  </GlobalText>
                </View>

                <GlobalText 
                  variant="body" 
                  style={styles.menuItemDescription}
                  numberOfLines={2}
                >
                  {menuItem.description}
                </GlobalText>

                <View style={styles.menuItemTags}>
                  {menuItem.isVegetarian && (
                    <View style={styles.tag}>
                      <GlobalText variant="body" style={styles.tagText}>
                        گیاهی
                      </GlobalText>
                    </View>
                  )}
                  {menuItem.isVegan && (
                    <View style={[styles.tag, { backgroundColor: '#4CAF50' }]}>
                      <GlobalText variant="body" style={styles.tagText}>
                        وگان
                      </GlobalText>
                    </View>
                  )}
                  {menuItem.spicyLevel > 0 && (
                    <View style={[styles.tag, { backgroundColor: '#FF5722' }]}>
                      <GlobalText variant="body" style={styles.tagText}>
                        {'🌶️'.repeat(menuItem.spicyLevel)}
                      </GlobalText>
                    </View>
                  )}
                  {!menuItem.isAvailable && (
                    <View style={[styles.tag, { backgroundColor: '#9E9E9E' }]}>
                      <GlobalText variant="body" style={styles.tagText}>
                        ناموجود
                      </GlobalText>
                    </View>
                  )}
                </View>
              </View>
            </View>
          ))}
        </View>
      ) : (
        <View style={styles.emptyState}>
          <Ionicons name="restaurant-outline" size={64} color="#E0E0E0" />
          <GlobalText variant="heading" style={styles.emptyTitle}>
            منویی موجود نیست
          </GlobalText>
          <GlobalText variant="body" style={styles.emptySubtitle}>
            این رستوران هنوز منو خود را اضافه نکرده است
          </GlobalText>
        </View>
      )}
    </View>
  );

  const renderReviewsTab = () => (
    <View style={styles.tabContent}>
      <View style={styles.emptyState}>
        <Ionicons name="chatbubbles-outline" size={64} color="#E0E0E0" />
        <GlobalText variant="heading" style={styles.emptyTitle}>
          نظری ثبت نشده
        </GlobalText>
        <GlobalText variant="body" style={styles.emptySubtitle}>
          اولین نفری باشید که نظر می‌دهید
        </GlobalText>
      </View>
    </View>
  );

  return (
    <SafeAreaView style={styles.container} edges={['left', 'right']}>
      {/* Header Image */}
      <View style={styles.imageContainer}>
        <Image
          source={{ 
            uri: restaurant.photos?.[0]?.url || 'https://images.unsplash.com/photo-1555396273-367ea4eb4db5?w=400'
          }}
          style={styles.headerImage}
          resizeMode="cover"
        />
        
        {/* Back Button */}
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color="#212121" />
        </TouchableOpacity>

        {/* Featured Badge */}
        {restaurant.is_featured && (
          <View style={styles.featuredBadge}>
            <GlobalText variant="body" style={styles.featuredText}>
              ویژه
            </GlobalText>
          </View>
        )}
      </View>

      {/* Restaurant Info */}
      <View style={styles.infoContainer}>
        <View style={styles.titleRow}>
          <View style={styles.titleSection}>
            <GlobalText variant="heading" style={styles.restaurantName}>
              {restaurant.name}
            </GlobalText>
            <View style={styles.ratingRow}>
              <View style={styles.stars}>
                {renderStars(restaurant.avg_rating)}
              </View>
              <GlobalText variant="body" style={styles.ratingText}>
                {restaurant.avg_rating.toFixed(1)}
              </GlobalText>
              <GlobalText variant="body" style={styles.priceText}>
                {getPriceRangeText(restaurant.price_range)}
              </GlobalText>
            </View>
          </View>
        </View>

        {/* Action Buttons */}
        <View style={styles.actionButtons}>
          <Button
            title="تماس"
            onPress={handleCall}
            variant="outline"
            size="small"
            leftIcon={<Ionicons name="call" size={16} color="#e6034b" />}
            style={{ flex: 1, marginRight: 8 }}
          />
          <Button
            title="مسیریابی"
            onPress={handleGetDirections}
            variant="outline"
            size="small"
            leftIcon={<Ionicons name="navigate" size={16} color="#e6034b" />}
            style={{ flex: 1, marginLeft: 8 }}
          />
        </View>
      </View>

      {/* Tabs */}
      <View style={styles.tabContainer}>
        {renderTabButton('info', 'اطلاعات')}
        {renderTabButton('menu', 'منو')}
        {renderTabButton('reviews', 'نظرات')}
      </View>

      {/* Tab Content */}
      <ScrollView style={styles.scrollView}>
        {selectedTab === 'info' && renderInfoTab()}
        {selectedTab === 'menu' && renderMenuTab()}
        {selectedTab === 'reviews' && renderReviewsTab()}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FAFAFA',
  },
  imageContainer: {
    position: 'relative',
  },
  headerImage: {
    width: '100%',
    height: IMAGE_HEIGHT,
  },
  backButton: {
    position: 'absolute',
    top: 44,
    left: 16,
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#FFFFFF',
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 2,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  featuredBadge: {
    position: 'absolute',
    top: 44,
    right: 16,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
    backgroundColor: '#FF9800',
  },
  featuredText: {
    color: '#FFFFFF',
    fontSize: 10,
  },
  infoContainer: {
    padding: 16,
    backgroundColor: '#FFFFFF',
    elevation: 2,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  titleRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  titleSection: {
    flex: 1,
  },
  restaurantName: {
    fontSize: 24,
    color: '#212121',
  },
  ratingRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
  },
  stars: {
    flexDirection: 'row',
  },
  ratingText: {
    color: '#212121',
    marginLeft: 8,
  },
  priceText: {
    color: '#757575',
    marginLeft: 8,
  },
  actionButtons: {
    flexDirection: 'row',
    marginTop: 16,
  },
  tabContainer: {
    flexDirection: 'row',
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
  },
  tabButton: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
    borderBottomWidth: 2,
  },
  scrollView: {
    flex: 1,
  },
  tabContent: {
    padding: 16,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    color: '#212121',
    marginBottom: 12,
  },
  sectionContent: {
    color: '#757575',
    lineHeight: 20,
  },
  contactItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
  },
  contactText: {
    color: '#212121',
    marginLeft: 12,
  },
  contactSubtext: {
    color: '#757575',
    fontSize: 12,
  },
  linkText: {
    color: '#e6034b',
    marginLeft: 12,
  },
  menuItem: {
    flexDirection: 'row',
    marginBottom: 16,
    borderRadius: 12,
    backgroundColor: '#FFFFFF',
    overflow: 'hidden',
    elevation: 1,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  menuItemImage: {
    width: 80,
    height: 80,
  },
  menuItemContent: {
    flex: 1,
    padding: 12,
  },
  menuItemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  menuItemName: {
    fontSize: 16,
    color: '#212121',
  },
  menuItemPrice: {
    color: '#e6034b',
    fontWeight: 'bold',
  },
  menuItemDescription: {
    color: '#757575',
    fontSize: 12,
    marginTop: 4,
  },
  menuItemTags: {
    flexDirection: 'row',
    marginTop: 8,
    flexWrap: 'wrap',
  },
  tag: {
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
    marginRight: 6,
    marginBottom: 4,
    backgroundColor: '#FF9800',
  },
  tagText: {
    color: '#FFFFFF',
    fontSize: 10,
  },
  emptyState: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 40,
  },
  emptyTitle: {
    color: '#757575',
    marginTop: 16,
  },
  emptySubtitle: {
    color: '#757575',
    textAlign: 'center',
    marginTop: 8,
  },
}); 