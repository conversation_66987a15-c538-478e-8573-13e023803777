import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  Alert,
  RefreshControl,
  Animated,
  Dimensions,
  Platform,
  Modal,
  TextInput,
  FlatList,
  Linking
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
// Removed react-native-maps to avoid native module issues in Expo
// import MapView, { <PERSON>er, <PERSON>yline, PROVIDER_GOOGLE } from 'react-native-maps';

import { GlobalText } from '../components/GlobalText';
import { useNotifications } from '../contexts/NotificationsContext';
import webSocketService from '../services/WebSocketService';
import apiService, { 
  LiveOrderTracking, 
  OrderUpdate, 
  LiveChat, 
  ChatMessage, 
  Order 
} from '../lib/api';

type RootStackParamList = {
  LiveOrderTracking: { orderId: string };
};

type LiveOrderTrackingScreenNavigationProp = StackNavigationProp<RootStackParamList, 'LiveOrderTracking'>;
type LiveOrderTrackingScreenRouteProp = RouteProp<RootStackParamList, 'LiveOrderTracking'>;

const { width, height } = Dimensions.get('window');

export const LiveOrderTrackingScreen: React.FC = () => {
  const navigation = useNavigation<LiveOrderTrackingScreenNavigationProp>();
  const route = useRoute<LiveOrderTrackingScreenRouteProp>();
  const { orderId } = route.params;
  
  const { state: notificationState } = useNotifications();
  
  const [trackingData, setTrackingData] = useState<LiveOrderTracking | null>(null);
  const [chatData, setChatData] = useState<LiveChat | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [showChat, setShowChat] = useState(false);
  const [chatMessage, setChatMessage] = useState('');
  const [sendingMessage, setSendingMessage] = useState(false);
  
  const animatedValue = useRef(new Animated.Value(0)).current;
  // const mapRef = useRef<MapView>(null); // Removed for Expo compatibility
  const chatFlatListRef = useRef<FlatList>(null);

  useEffect(() => {
    loadTrackingData();
    loadChatData();
    joinOrderTracking();
    
    // Start pulse animation for active orders
    if (trackingData?.current_status !== 'delivered' && trackingData?.current_status !== 'cancelled') {
      startPulseAnimation();
    }

    return () => {
      leaveOrderTracking();
    };
  }, [orderId]);

  // Listen for real-time updates
  useEffect(() => {
    const orderUpdates = notificationState.orderUpdates.filter(
      update => update.order_id === orderId
    );
    
    if (orderUpdates.length > 0) {
      // Update tracking data with latest status
      const latestUpdate = orderUpdates[0];
      if (trackingData) {
        setTrackingData(prev => prev ? {
          ...prev,
          current_status: latestUpdate.status,
          updates: [latestUpdate, ...prev.updates]
        } : null);
      }
    }
  }, [notificationState.orderUpdates, orderId, trackingData]);

  // Listen for chat messages
  useEffect(() => {
    const newMessages = notificationState.chatMessages.filter(
      message => message.order_id === orderId
    );
    
    if (newMessages.length > 0 && chatData) {
      setChatData(prev => prev ? {
        ...prev,
        messages: [...newMessages, ...prev.messages]
      } : null);
      
      // Auto-scroll to bottom
      setTimeout(() => {
        chatFlatListRef.current?.scrollToOffset({ offset: 0, animated: true });
      }, 100);
    }
  }, [notificationState.chatMessages, orderId, chatData]);

  const loadTrackingData = async () => {
    try {
      setLoading(true);
      const data = await apiService.getLiveOrderTracking(orderId);
      setTrackingData(data);
      
      // Map functionality disabled for Expo compatibility
      // TODO: Implement map view when using bare React Native or expo-dev-client
    } catch (error) {
      console.error('Error loading tracking data:', error);
      Alert.alert('خطا', 'خطا در بارگذاری اطلاعات پیگیری');
    } finally {
      setLoading(false);
    }
  };

  const loadChatData = async () => {
    try {
      const data = await apiService.getOrderChat(orderId);
      setChatData(data);
    } catch (error) {
      console.error('Error loading chat data:', error);
    }
  };

  const joinOrderTracking = () => {
    webSocketService.joinOrderTracking(orderId);
  };

  const leaveOrderTracking = () => {
    webSocketService.leaveOrderTracking(orderId);
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await Promise.all([loadTrackingData(), loadChatData()]);
    setRefreshing(false);
  };

  const startPulseAnimation = () => {
    Animated.loop(
      Animated.sequence([
        Animated.timing(animatedValue, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        }),
        Animated.timing(animatedValue, {
          toValue: 0,
          duration: 1000,
          useNativeDriver: true,
        }),
      ])
    ).start();
  };

  const getStatusColor = (status: Order['status']) => {
    switch (status) {
      case 'pending': return '#F59E0B';
      case 'confirmed': return '#3B82F6';
      case 'preparing': return '#EF4444';
      case 'ready': return '#10B981';
      case 'delivered': return '#059669';
      case 'cancelled': return '#6B7280';
      default: return '#9CA3AF';
    }
  };

  const getStatusLabel = (status: Order['status']) => {
    switch (status) {
      case 'pending': return 'در انتظار تأیید';
      case 'confirmed': return 'تأیید شده';
      case 'preparing': return 'در حال آماده‌سازی';
      case 'ready': return 'آماده تحویل';
      case 'delivered': return 'تحویل داده شده';
      case 'cancelled': return 'لغو شده';
      default: return status;
    }
  };

  const getStatusIcon = (status: Order['status']) => {
    switch (status) {
      case 'pending': return 'time-outline';
      case 'confirmed': return 'checkmark-circle-outline';
      case 'preparing': return 'restaurant-outline';
      case 'ready': return 'bag-check-outline';
      case 'delivered': return 'checkmark-done-circle';
      case 'cancelled': return 'close-circle-outline';
      default: return 'help-circle-outline';
    }
  };

  const formatTime = (timestamp: string) => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString('fa-IR', { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  const getEstimatedTime = () => {
    if (!trackingData?.estimated_delivery) return null;
    
    const now = new Date();
    const estimated = new Date(trackingData.estimated_delivery);
    const diffMinutes = Math.ceil((estimated.getTime() - now.getTime()) / (1000 * 60));
    
    if (diffMinutes <= 0) return 'زمان تحویل گذشته است';
    if (diffMinutes < 60) return `${diffMinutes} دقیقه`;
    
    const hours = Math.floor(diffMinutes / 60);
    const minutes = diffMinutes % 60;
    return `${hours} ساعت و ${minutes} دقیقه`;
  };

  const handleCallRestaurant = () => {
    Alert.alert(
      'تماس با رستوران',
      'آیا می‌خواهید با رستوران تماس بگیرید؟',
      [
        { text: 'انصراف', style: 'cancel' },
        { 
          text: 'تماس', 
          onPress: () => {
            // In a real app, this would use the restaurant's phone number
            Linking.openURL('tel:+93701234567');
          }
        }
      ]
    );
  };

  const handleSendMessage = async () => {
    if (!chatMessage.trim() || sendingMessage) return;

    try {
      setSendingMessage(true);
      const newMessage = await apiService.sendChatMessage(orderId, chatMessage.trim());
      
      // Add message to local state
      setChatData(prev => prev ? {
        ...prev,
        messages: [newMessage, ...prev.messages]
      } : null);
      
      setChatMessage('');
      
      // Auto-scroll to bottom
      setTimeout(() => {
        chatFlatListRef.current?.scrollToOffset({ offset: 0, animated: true });
      }, 100);
      
    } catch (error) {
      console.error('Error sending message:', error);
      Alert.alert('خطا', 'خطا در ارسال پیام');
    } finally {
      setSendingMessage(false);
    }
  };

  const renderStatusTimeline = () => {
    if (!trackingData) return null;

    const allStatuses: Order['status'][] = ['confirmed', 'preparing', 'ready', 'delivered'];
    const currentIndex = allStatuses.indexOf(trackingData.current_status);

    return (
      <View style={styles.timelineContainer}>
        {allStatuses.map((status, index) => {
          const isCompleted = index <= currentIndex;
          const isCurrent = index === currentIndex;
          const statusColor = isCompleted ? getStatusColor(status) : '#E5E7EB';
          
          return (
            <View key={status} style={styles.timelineItem}>
              <View style={styles.timelineIconContainer}>
                <Animated.View
                  style={[
                    styles.timelineIcon,
                    { 
                      backgroundColor: statusColor,
                      transform: isCurrent ? [{
                        scale: animatedValue.interpolate({
                          inputRange: [0, 1],
                          outputRange: [1, 1.2]
                        })
                      }] : []
                    }
                  ]}
                >
                  <Ionicons 
                    name={getStatusIcon(status) as any} 
                    size={16} 
                    color="#FFFFFF" 
                  />
                </Animated.View>
                {index < allStatuses.length - 1 && (
                  <View 
                    style={[
                      styles.timelineLine, 
                      { backgroundColor: index < currentIndex ? getStatusColor(status) : '#E5E7EB' }
                    ]} 
                  />
                )}
              </View>
              <View style={styles.timelineContent}>
                <GlobalText 
                  variant="body" 
                  style={[
                    styles.timelineTitle,
                    { color: isCompleted ? '#374151' : '#9CA3AF' }
                  ]}
                >
                  {getStatusLabel(status)}
                </GlobalText>
                {trackingData.updates.find(u => u.status === status) && (
                  <GlobalText variant="caption" style={styles.timelineTime}>
                    {formatTime(trackingData.updates.find(u => u.status === status)!.timestamp)}
                  </GlobalText>
                )}
              </View>
            </View>
          );
        })}
      </View>
    );
  };

  const renderMapView = () => {
    return (
      <View style={styles.mapPlaceholder}>
        <Ionicons name="map-outline" size={48} color="#9CA3AF" />
        <GlobalText variant="body" style={styles.mapPlaceholderText}>
          نقشه موقتاً غیرفعال است
        </GlobalText>
        <GlobalText variant="caption" style={styles.mapNote}>
          برای نمایش نقشه، از expo-dev-client استفاده کنید
        </GlobalText>
        
        {/* Show location info as text */}
        {trackingData && (
          <View style={styles.locationInfo}>
            <View style={styles.locationItem}>
              <View style={styles.restaurantMarker}>
                <Ionicons name="restaurant" size={16} color="#FFFFFF" />
              </View>
              <GlobalText variant="caption" style={styles.locationText}>
                رستوران: {trackingData.restaurant_location?.address || 'نامشخص'}
              </GlobalText>
            </View>
            
            <View style={styles.locationItem}>
              <View style={styles.deliveryMarker}>
                <Ionicons name="home" size={16} color="#FFFFFF" />
              </View>
              <GlobalText variant="caption" style={styles.locationText}>
                مقصد: {trackingData.delivery_location?.address || 'نامشخص'}
              </GlobalText>
            </View>
            
            {trackingData.driver_location && (
              <View style={styles.locationItem}>
                <View style={styles.driverMarker}>
                  <Ionicons name="car" size={16} color="#FFFFFF" />
                </View>
                <GlobalText variant="caption" style={styles.locationText}>
                  راننده در مسیر است
                </GlobalText>
              </View>
            )}
          </View>
        )}
      </View>
    );
  };

  const renderChatMessage = ({ item }: { item: ChatMessage }) => {
    const isMyMessage = item.sender_type === 'customer';
    
    return (
      <View 
        style={[
          styles.chatMessage,
          { alignSelf: isMyMessage ? 'flex-end' : 'flex-start' }
        ]}
      >
        <View 
          style={[
            styles.chatBubble,
            { 
              backgroundColor: isMyMessage ? '#e6034b' : '#F3F4F6',
              borderTopRightRadius: isMyMessage ? 4 : 16,
              borderTopLeftRadius: isMyMessage ? 16 : 4
            }
          ]}
        >
          {!isMyMessage && (
            <GlobalText variant="caption" style={styles.chatSender}>
              {item.sender_name}
            </GlobalText>
          )}
          <GlobalText 
            variant="body" 
            style={[
              styles.chatText,
              { color: isMyMessage ? '#FFFFFF' : '#374151' }
            ]}
          >
            {item.message}
          </GlobalText>
          <GlobalText 
            variant="caption" 
            style={[
              styles.chatTime,
              { color: isMyMessage ? '#FFFFFF' : '#9CA3AF' }
            ]}
          >
            {formatTime(item.timestamp)}
          </GlobalText>
        </View>
      </View>
    );
  };

  const renderChatModal = () => (
    <Modal
      visible={showChat}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={() => setShowChat(false)}
    >
      <SafeAreaView style={styles.chatContainer} edges={['top', 'left', 'right']}>
        {/* Chat Header */}
        <View style={styles.chatHeader}>
          <TouchableOpacity
            style={styles.chatCloseButton}
            onPress={() => setShowChat(false)}
          >
            <Ionicons name="close" size={24} color="#374151" />
          </TouchableOpacity>
          <GlobalText variant="heading" style={styles.chatTitle}>
            گفتگو با رستوران
          </GlobalText>
          <View style={styles.chatCloseButton} />
        </View>

        {/* Chat Messages */}
        <FlatList
          ref={chatFlatListRef}
          data={chatData?.messages || []}
          renderItem={renderChatMessage}
          keyExtractor={(item) => item.id}
          style={styles.chatMessages}
          contentContainerStyle={styles.chatMessagesContent}
          inverted
          showsVerticalScrollIndicator={false}
        />

        {/* Chat Input */}
        <View style={styles.chatInputContainer}>
          <TextInput
            style={styles.chatInput}
            placeholder="پیام خود را بنویسید..."
            value={chatMessage}
            onChangeText={setChatMessage}
            multiline
            maxLength={500}
          />
          <TouchableOpacity
            style={[
              styles.chatSendButton,
              { opacity: chatMessage.trim() && !sendingMessage ? 1 : 0.5 }
            ]}
            onPress={handleSendMessage}
            disabled={!chatMessage.trim() || sendingMessage}
          >
            <Ionicons 
              name={sendingMessage ? "hourglass" : "send"} 
              size={20} 
              color="#FFFFFF" 
            />
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    </Modal>
  );

  if (loading) {
    return (
      <SafeAreaView style={styles.container} edges={['top', 'left', 'right']}>
        <View style={styles.loadingContainer}>
          <GlobalText variant="body" style={styles.loadingText}>
            در حال بارگذاری...
          </GlobalText>
        </View>
      </SafeAreaView>
    );
  }

  if (!trackingData) {
    return (
      <SafeAreaView style={styles.container} edges={['top', 'left', 'right']}>
        <View style={styles.errorContainer}>
          <Ionicons name="alert-circle-outline" size={64} color="#EF4444" />
          <GlobalText variant="heading" style={styles.errorTitle}>
            خطا در بارگذاری
          </GlobalText>
          <GlobalText variant="body" style={styles.errorDescription}>
            اطلاعات پیگیری سفارش یافت نشد
          </GlobalText>
          <TouchableOpacity style={styles.retryButton} onPress={loadTrackingData}>
            <GlobalText variant="body" style={styles.retryButtonText}>
              تلاش مجدد
            </GlobalText>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container} edges={['top', 'left', 'right']}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color="#374151" />
        </TouchableOpacity>
        <GlobalText variant="heading" style={styles.headerTitle}>
          پیگیری سفارش
        </GlobalText>
        <TouchableOpacity
          style={styles.chatButton}
          onPress={() => setShowChat(true)}
        >
          <Ionicons name="chatbubble-outline" size={24} color="#e6034b" />
          {chatData?.messages.some(m => !m.read && m.sender_type !== 'customer') && (
            <View style={styles.chatBadge} />
          )}
        </TouchableOpacity>
      </View>

      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={['#e6034b']}
          />
        }
        showsVerticalScrollIndicator={false}
      >
        {/* Status Card */}
        <View style={styles.statusCard}>
          <View style={styles.statusHeader}>
            <View 
              style={[
                styles.statusIndicator, 
                { backgroundColor: getStatusColor(trackingData.current_status) }
              ]}
            >
              <Ionicons 
                name={getStatusIcon(trackingData.current_status) as any} 
                size={24} 
                color="#FFFFFF" 
              />
            </View>
            <View style={styles.statusInfo}>
              <GlobalText variant="heading" style={styles.statusTitle}>
                {getStatusLabel(trackingData.current_status)}
              </GlobalText>
              {getEstimatedTime() && (
                <GlobalText variant="body" style={styles.statusSubtitle}>
                  زمان تحویل: {getEstimatedTime()}
                </GlobalText>
              )}
            </View>
          </View>
          
          {/* Action Buttons */}
          <View style={styles.actionButtons}>
            <TouchableOpacity 
              style={styles.actionButton}
              onPress={handleCallRestaurant}
            >
              <Ionicons name="call" size={20} color="#e6034b" />
              <GlobalText variant="body" style={styles.actionButtonText}>
                تماس
              </GlobalText>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={styles.actionButton}
              onPress={() => setShowChat(true)}
            >
              <Ionicons name="chatbubble" size={20} color="#e6034b" />
              <GlobalText variant="body" style={styles.actionButtonText}>
                گفتگو
              </GlobalText>
            </TouchableOpacity>
          </View>
        </View>

        {/* Timeline */}
        <View style={styles.timelineCard}>
          <GlobalText variant="heading" style={styles.sectionTitle}>
            وضعیت سفارش
          </GlobalText>
          {renderStatusTimeline()}
        </View>

        {/* Map */}
        <View style={styles.mapCard}>
          <GlobalText variant="heading" style={styles.sectionTitle}>
            موقعیت
          </GlobalText>
          {renderMapView()}
        </View>

        {/* Updates */}
        {trackingData.updates.length > 0 && (
          <View style={styles.updatesCard}>
            <GlobalText variant="heading" style={styles.sectionTitle}>
              آخرین به‌روزرسانی‌ها
            </GlobalText>
            {trackingData.updates.map((update) => (
              <View key={update.id} style={styles.updateItem}>
                <View 
                  style={[
                    styles.updateIcon, 
                    { backgroundColor: getStatusColor(update.status) }
                  ]}
                >
                  <Ionicons 
                    name={getStatusIcon(update.status) as any} 
                    size={16} 
                    color="#FFFFFF" 
                  />
                </View>
                <View style={styles.updateContent}>
                  <GlobalText variant="body" style={styles.updateMessage}>
                    {update.message}
                  </GlobalText>
                  <GlobalText variant="caption" style={styles.updateTime}>
                    {formatTime(update.timestamp)}
                  </GlobalText>
                </View>
              </View>
            ))}
          </View>
        )}
      </ScrollView>

      {/* Chat Modal */}
      {renderChatModal()}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#374151',
    flex: 1,
    textAlign: 'center',
  },
  chatButton: {
    padding: 8,
    position: 'relative',
  },
  chatBadge: {
    position: 'absolute',
    top: 8,
    right: 8,
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#EF4444',
  },
  scrollView: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingText: {
    color: '#9CA3AF',
  },
  errorContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 32,
  },
  errorTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#374151',
    marginTop: 16,
    marginBottom: 8,
  },
  errorDescription: {
    color: '#9CA3AF',
    textAlign: 'center',
    marginBottom: 24,
  },
  retryButton: {
    backgroundColor: '#e6034b',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  retryButtonText: {
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
  statusCard: {
    backgroundColor: '#FFFFFF',
    margin: 16,
    borderRadius: 12,
    padding: 20,
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      },
      android: {
        elevation: 3,
      },
    }),
  },
  statusHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  statusIndicator: {
    width: 56,
    height: 56,
    borderRadius: 28,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
  },
  statusInfo: {
    flex: 1,
  },
  statusTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#374151',
    marginBottom: 4,
  },
  statusSubtitle: {
    color: '#6B7280',
  },
  actionButtons: {
    flexDirection: 'row',
    gap: 12,
  },
  actionButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#e6034b',
    gap: 8,
  },
  actionButtonText: {
    color: '#e6034b',
    fontWeight: 'bold',
  },
  timelineCard: {
    backgroundColor: '#FFFFFF',
    marginHorizontal: 16,
    marginBottom: 16,
    borderRadius: 12,
    padding: 20,
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      },
      android: {
        elevation: 3,
      },
    }),
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#374151',
    marginBottom: 16,
  },
  timelineContainer: {
    paddingLeft: 8,
  },
  timelineItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  timelineIconContainer: {
    alignItems: 'center',
    marginRight: 16,
  },
  timelineIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  timelineLine: {
    width: 2,
    height: 24,
    marginTop: 8,
  },
  timelineContent: {
    flex: 1,
    paddingTop: 4,
  },
  timelineTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 2,
  },
  timelineTime: {
    color: '#9CA3AF',
    fontSize: 14,
  },
  mapCard: {
    backgroundColor: '#FFFFFF',
    marginHorizontal: 16,
    marginBottom: 16,
    borderRadius: 12,
    padding: 20,
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      },
      android: {
        elevation: 3,
      },
    }),
  },
  map: {
    width: '100%',
    height: 200,
    borderRadius: 8,
  },
  mapPlaceholder: {
    width: '100%',
    height: 200,
    borderRadius: 8,
    backgroundColor: '#F3F4F6',
    alignItems: 'center',
    justifyContent: 'center',
  },
  mapPlaceholderText: {
    color: '#9CA3AF',
    marginTop: 8,
  },
  mapNote: {
    color: '#9CA3AF',
    fontSize: 12,
    marginTop: 4,
    textAlign: 'center',
  },
  locationInfo: {
    marginTop: 16,
    width: '100%',
  },
  locationItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
    paddingHorizontal: 16,
  },
  locationText: {
    color: '#6B7280',
    fontSize: 12,
    marginLeft: 8,
    flex: 1,
  },
  restaurantMarker: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#e6034b',
    alignItems: 'center',
    justifyContent: 'center',
  },
  deliveryMarker: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#10B981',
    alignItems: 'center',
    justifyContent: 'center',
  },
  driverMarker: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#3B82F6',
    alignItems: 'center',
    justifyContent: 'center',
  },
  updatesCard: {
    backgroundColor: '#FFFFFF',
    marginHorizontal: 16,
    marginBottom: 16,
    borderRadius: 12,
    padding: 20,
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      },
      android: {
        elevation: 3,
      },
    }),
  },
  updateItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  updateIcon: {
    width: 24,
    height: 24,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  updateContent: {
    flex: 1,
  },
  updateMessage: {
    color: '#374151',
    marginBottom: 2,
  },
  updateTime: {
    color: '#9CA3AF',
    fontSize: 12,
  },
  chatContainer: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  chatHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  chatCloseButton: {
    padding: 8,
    width: 40,
  },
  chatTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#374151',
  },
  chatMessages: {
    flex: 1,
    padding: 16,
  },
  chatMessagesContent: {
    paddingBottom: 16,
  },
  chatMessage: {
    marginBottom: 12,
    maxWidth: '80%',
  },
  chatBubble: {
    padding: 12,
    borderRadius: 16,
  },
  chatSender: {
    color: '#6B7280',
    fontSize: 12,
    marginBottom: 4,
  },
  chatText: {
    fontSize: 16,
    lineHeight: 20,
    marginBottom: 4,
  },
  chatTime: {
    fontSize: 12,
    textAlign: 'right',
  },
  chatInputContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#FFFFFF',
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
  },
  chatInput: {
    flex: 1,
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 12,
    maxHeight: 100,
    marginRight: 12,
    fontSize: 16,
  },
  chatSendButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: '#e6034b',
    alignItems: 'center',
    justifyContent: 'center',
  },
}); 