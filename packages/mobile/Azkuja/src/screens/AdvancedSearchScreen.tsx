import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ScrollView,
  StyleSheet,
  Modal,
  FlatList,
  Alert,
  ActivityIndicator,
  Dimensions,
  Platform
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
// Using custom slider implementation instead of external package

import { GlobalText } from '../components/GlobalText';

// Simple custom slider component
interface SliderProps {
  minimumValue: number;
  maximumValue: number;
  value: number;
  onValueChange: (value: number) => void;
  step?: number;
  style?: any;
  minimumTrackTintColor?: string;
  maximumTrackTintColor?: string;
  thumbStyle?: any;
}

const CustomSlider: React.FC<SliderProps> = ({ 
  minimumValue, 
  maximumValue, 
  value, 
  onValueChange, 
  step = 1,
  minimumTrackTintColor = '#e6034b',
  maximumTrackTintColor = '#E5E7EB'
}) => {
  const [sliderValue, setSliderValue] = useState(value);
  
  const handlePress = (event: any) => {
    const { locationX } = event.nativeEvent;
    const sliderWidth = 280; // Approximate slider width
    const percentage = Math.max(0, Math.min(1, locationX / sliderWidth));
    const newValue = minimumValue + (maximumValue - minimumValue) * percentage;
    const steppedValue = Math.round(newValue / step) * step;
    setSliderValue(steppedValue);
    onValueChange(steppedValue);
  };

  return (
    <TouchableOpacity onPress={handlePress} style={{ height: 40, justifyContent: 'center' }}>
      <View style={{
        height: 4,
        backgroundColor: maximumTrackTintColor,
        borderRadius: 2,
        width: 280,
      }}>
        <View style={{
          height: 4,
          backgroundColor: minimumTrackTintColor,
          borderRadius: 2,
          width: `${((sliderValue - minimumValue) / (maximumValue - minimumValue)) * 100}%`,
        }} />
        <View style={{
          position: 'absolute',
          left: `${((sliderValue - minimumValue) / (maximumValue - minimumValue)) * 100}%`,
          top: -8,
          width: 20,
          height: 20,
          borderRadius: 10,
          backgroundColor: minimumTrackTintColor,
          marginLeft: -10,
        }} />
      </View>
    </TouchableOpacity>
  );
};
import { useSearch } from '../contexts/SearchContext';
import { SearchSuggestion, CuisineOption, LocationSuggestion, Restaurant } from '../lib/api';

type RootStackParamList = {
  AdvancedSearch: undefined;
  RestaurantDetail: { restaurant: Restaurant };
};

type AdvancedSearchScreenNavigationProp = StackNavigationProp<RootStackParamList, 'AdvancedSearch'>;
type AdvancedSearchScreenRouteProp = RouteProp<RootStackParamList, 'AdvancedSearch'>;

const { width } = Dimensions.get('window');

export const AdvancedSearchScreen: React.FC = () => {
  const navigation = useNavigation<AdvancedSearchScreenNavigationProp>();
  const route = useRoute<AdvancedSearchScreenRouteProp>();
  const {
    state,
    setQuery,
    setFilters,
    performSearch,
    clearSearch,
    resetFilters,
    loadSuggestions,
    showSuggestions,
    hideSuggestions,
    loadLocationSuggestions,
    setShowFilters
  } = useSearch();

  const [localQuery, setLocalQuery] = useState(state.currentQuery);
  const [localLocation, setLocalLocation] = useState(state.currentFilters.location);
  const [showLocationSuggestions, setShowLocationSuggestions] = useState(false);
  const [activeTab, setActiveTab] = useState<'search' | 'filters' | 'results'>('search');

  const queryInputRef = useRef<TextInput>(null);
  const locationInputRef = useRef<TextInput>(null);

  useEffect(() => {
    if (localQuery !== state.currentQuery) {
      setLocalQuery(state.currentQuery);
    }
  }, [state.currentQuery]);

  useEffect(() => {
    if (localLocation !== state.currentFilters.location) {
      setLocalLocation(state.currentFilters.location);
    }
  }, [state.currentFilters.location]);

  // Load suggestions when query changes
  useEffect(() => {
    if (localQuery.length > 0) {
      const timer = setTimeout(() => {
        loadSuggestions(localQuery);
      }, 300);
      return () => clearTimeout(timer);
    }
  }, [localQuery, loadSuggestions]);

  // Load location suggestions when location changes
  useEffect(() => {
    if (localLocation.length > 0) {
      const timer = setTimeout(() => {
        loadLocationSuggestions(localLocation);
      }, 300);
      return () => clearTimeout(timer);
    }
  }, [localLocation, loadLocationSuggestions]);

  const handleSearch = async () => {
    setQuery(localQuery);
    setFilters({ location: localLocation });
    hideSuggestions();
    setShowLocationSuggestions(false);
    setActiveTab('results');
    await performSearch();
  };

  const handleSuggestionPress = (suggestion: SearchSuggestion) => {
    switch (suggestion.type) {
      case 'restaurant':
        // Navigate to restaurant detail if we have restaurant data
        if (suggestion.data?.restaurantId) {
          // In a real app, you'd fetch the restaurant details
          navigation.navigate('RestaurantDetail', { restaurant: suggestion.data });
        }
        break;
      case 'cuisine':
        setFilters({ cuisine: [suggestion.data?.cuisineId || suggestion.text] });
        setActiveTab('results');
        performSearch();
        break;
      case 'location':
        setLocalLocation(suggestion.text);
        setFilters({ location: suggestion.text });
        break;
      default:
        setLocalQuery(suggestion.text);
        setQuery(suggestion.text);
        break;
    }
    hideSuggestions();
  };

  const handleLocationSuggestionPress = (location: LocationSuggestion) => {
    setLocalLocation(`${location.name}, ${location.city}`);
    setFilters({ location: `${location.name}, ${location.city}` });
    setShowLocationSuggestions(false);
  };

  const handleCuisineToggle = (cuisineId: string) => {
    const currentCuisines = state.currentFilters.cuisine;
    const newCuisines = currentCuisines.includes(cuisineId)
      ? currentCuisines.filter(id => id !== cuisineId)
      : [...currentCuisines, cuisineId];
    setFilters({ cuisine: newCuisines });
  };

  const handlePriceRangeChange = (values: number[]) => {
    setFilters({ priceRange: [values[0], values[1]] });
  };

  const handleRatingChange = (rating: number) => {
    setFilters({ rating });
  };

  const handleDeliveryTimeChange = (time: number) => {
    setFilters({ deliveryTime: time });
  };

  const handleSortChange = (sortBy: string) => {
    setFilters({ sortBy: sortBy as any });
  };

  const clearAllFilters = () => {
    setLocalQuery('');
    setLocalLocation('');
    resetFilters();
    setActiveTab('search');
  };

  const getActiveFiltersCount = () => {
    let count = 0;
    const filters = state.currentFilters;
    if (filters.location) count++;
    if (filters.cuisine.length > 0) count++;
    if (filters.priceRange[0] > 1 || filters.priceRange[1] < 4) count++;
    if (filters.rating > 0) count++;
    if (filters.deliveryTime < 60) count++;
    if (filters.isOpen) count++;
    if (filters.hasDelivery) count++;
    if (filters.hasPromotion) count++;
    return count;
  };

  const renderSuggestion = ({ item }: { item: SearchSuggestion }) => (
    <TouchableOpacity
      style={styles.suggestionItem}
      onPress={() => handleSuggestionPress(item)}
    >
      <Text style={styles.suggestionIcon}>{item.icon}</Text>
      <View style={styles.suggestionContent}>
        <GlobalText variant="body" style={styles.suggestionText}>
          {item.text}
        </GlobalText>
        {item.subtitle && (
          <GlobalText variant="caption" style={styles.suggestionSubtitle}>
            {item.subtitle}
          </GlobalText>
        )}
      </View>
      <Ionicons name="arrow-forward" size={16} color="#9CA3AF" />
    </TouchableOpacity>
  );

  const renderLocationSuggestion = ({ item }: { item: LocationSuggestion }) => (
    <TouchableOpacity
      style={styles.suggestionItem}
      onPress={() => handleLocationSuggestionPress(item)}
    >
      <Ionicons name="location" size={20} color="#e6034b" />
      <View style={styles.suggestionContent}>
        <GlobalText variant="body" style={styles.suggestionText}>
          {item.name}, {item.city}
        </GlobalText>
        <GlobalText variant="caption" style={styles.suggestionSubtitle}>
          {item.country}
        </GlobalText>
      </View>
    </TouchableOpacity>
  );

  const renderCuisineChip = (cuisine: CuisineOption) => {
    const isSelected = state.currentFilters.cuisine.includes(cuisine.id);
    return (
      <TouchableOpacity
        key={cuisine.id}
        style={[
          styles.cuisineChip,
          { backgroundColor: isSelected ? '#e6034b' : '#F3F4F6' }
        ]}
        onPress={() => handleCuisineToggle(cuisine.id)}
      >
        <Text style={styles.cuisineIcon}>{cuisine.icon}</Text>
        <GlobalText
          variant="body"
          style={[
            styles.cuisineText,
            { color: isSelected ? '#FFFFFF' : '#374151' }
          ]}
        >
          {cuisine.name}
        </GlobalText>
        <GlobalText
          variant="caption"
          style={[
            styles.cuisineCount,
            { color: isSelected ? '#FFFFFF' : '#9CA3AF' }
          ]}
        >
          {cuisine.count}
        </GlobalText>
      </TouchableOpacity>
    );
  };

  const renderRestaurantCard = ({ item }: { item: Restaurant }) => (
    <TouchableOpacity
      style={styles.restaurantCard}
      onPress={() => navigation.navigate('RestaurantDetail', { restaurant: item })}
    >
      <View style={styles.restaurantInfo}>
        <GlobalText variant="heading" style={styles.restaurantName}>
          {item.name}
        </GlobalText>
        <GlobalText variant="body" style={styles.restaurantDescription} numberOfLines={2}>
          {item.description}
        </GlobalText>
        <View style={styles.restaurantMeta}>
          <View style={styles.metaItem}>
            <Ionicons name="star" size={14} color="#FFB300" />
            <GlobalText variant="caption" style={styles.metaText}>
              {item.avg_rating.toFixed(1)}
            </GlobalText>
          </View>
          <View style={styles.metaItem}>
            <Ionicons name="location" size={14} color="#9CA3AF" />
            <GlobalText variant="caption" style={styles.metaText}>
              {item.city}
            </GlobalText>
          </View>
          <View style={styles.metaItem}>
            <Ionicons name="card" size={14} color="#9CA3AF" />
            <GlobalText variant="caption" style={styles.metaText}>
              {'؋'.repeat(item.price_range)}
            </GlobalText>
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );

  const renderSearchTab = () => (
    <ScrollView style={styles.tabContent} showsVerticalScrollIndicator={false}>
      {/* Search Input */}
      <View style={styles.searchContainer}>
        <View style={styles.searchBox}>
          <Ionicons name="search" size={20} color="#9CA3AF" />
          <TextInput
            ref={queryInputRef}
            style={styles.searchInput}
            placeholder="جستجوی رستوران، غذا یا منطقه..."
            value={localQuery}
            onChangeText={setLocalQuery}
            onFocus={showSuggestions}
            onSubmitEditing={handleSearch}
            returnKeyType="search"
          />
          {localQuery.length > 0 && (
            <TouchableOpacity onPress={() => setLocalQuery('')}>
              <Ionicons name="close-circle" size={20} color="#9CA3AF" />
            </TouchableOpacity>
          )}
        </View>
      </View>

      {/* Location Input */}
      <View style={styles.searchContainer}>
        <View style={styles.searchBox}>
          <Ionicons name="location" size={20} color="#9CA3AF" />
          <TextInput
            ref={locationInputRef}
            style={styles.searchInput}
            placeholder="موقعیت مکانی..."
            value={localLocation}
            onChangeText={setLocalLocation}
            onFocus={() => setShowLocationSuggestions(true)}
            onSubmitEditing={handleSearch}
            returnKeyType="search"
          />
          {localLocation.length > 0 && (
            <TouchableOpacity onPress={() => setLocalLocation('')}>
              <Ionicons name="close-circle" size={20} color="#9CA3AF" />
            </TouchableOpacity>
          )}
        </View>
      </View>

      {/* Search Button */}
      <TouchableOpacity style={styles.searchButton} onPress={handleSearch}>
        <Ionicons name="search" size={20} color="#FFFFFF" />
        <GlobalText variant="body" style={styles.searchButtonText}>
          جستجو
        </GlobalText>
      </TouchableOpacity>

      {/* Recent Searches */}
      {state.searchHistory.length > 0 && (
        <View style={styles.section}>
          <GlobalText variant="heading" style={styles.sectionTitle}>
            جستجوهای اخیر
          </GlobalText>
          {state.searchHistory.slice(0, 5).map((item) => (
            <TouchableOpacity
              key={item.id}
              style={styles.historyItem}
              onPress={() => {
                setLocalQuery(item.query);
                setQuery(item.query);
                setFilters(item.filters);
              }}
            >
              <Ionicons name="time" size={16} color="#9CA3AF" />
              <GlobalText variant="body" style={styles.historyText}>
                {item.query}
              </GlobalText>
              <GlobalText variant="caption" style={styles.historyCount}>
                {item.resultCount} نتیجه
              </GlobalText>
            </TouchableOpacity>
          ))}
        </View>
      )}

      {/* Popular Searches */}
      {state.popularSearches.length > 0 && (
        <View style={styles.section}>
          <GlobalText variant="heading" style={styles.sectionTitle}>
            جستجوهای محبوب
          </GlobalText>
          <View style={styles.popularContainer}>
            {state.popularSearches.map((item) => (
              <TouchableOpacity
                key={item.id}
                style={styles.popularChip}
                onPress={() => {
                  setLocalQuery(item.query);
                  setQuery(item.query);
                  handleSearch();
                }}
              >
                <GlobalText variant="body" style={styles.popularText}>
                  {item.query}
                </GlobalText>
                <GlobalText variant="caption" style={styles.popularCount}>
                  {item.count}
                </GlobalText>
              </TouchableOpacity>
            ))}
          </View>
        </View>
      )}
    </ScrollView>
  );

  const renderFiltersTab = () => (
    <ScrollView style={styles.tabContent} showsVerticalScrollIndicator={false}>
      {/* Cuisine Filter */}
      <View style={styles.section}>
        <GlobalText variant="heading" style={styles.sectionTitle}>
          نوع غذا
        </GlobalText>
        <View style={styles.cuisineContainer}>
          {state.cuisineOptions.map(renderCuisineChip)}
        </View>
      </View>

      {/* Price Range Filter */}
      <View style={styles.section}>
        <GlobalText variant="heading" style={styles.sectionTitle}>
          محدوده قیمت
        </GlobalText>
        <View style={styles.priceContainer}>
          <GlobalText variant="body" style={styles.priceLabel}>
            {'؋'.repeat(state.currentFilters.priceRange[0])} - {'؋'.repeat(state.currentFilters.priceRange[1])}
          </GlobalText>
          <View style={styles.sliderContainer}>
            <CustomSlider
              minimumValue={1}
              maximumValue={4}
              step={1}
              value={state.currentFilters.priceRange[0]}
              onValueChange={(value: number) => handlePriceRangeChange([value, state.currentFilters.priceRange[1]])}
              minimumTrackTintColor="#e6034b"
              maximumTrackTintColor="#E5E7EB"
            />
          </View>
        </View>
      </View>

      {/* Rating Filter */}
      <View style={styles.section}>
        <GlobalText variant="heading" style={styles.sectionTitle}>
          حداقل امتیاز
        </GlobalText>
        <View style={styles.ratingContainer}>
          {[0, 3, 3.5, 4, 4.5].map((rating) => (
            <TouchableOpacity
              key={rating}
              style={[
                styles.ratingChip,
                { backgroundColor: state.currentFilters.rating === rating ? '#e6034b' : '#F3F4F6' }
              ]}
              onPress={() => handleRatingChange(rating)}
            >
              <Ionicons
                name="star"
                size={16}
                color={state.currentFilters.rating === rating ? '#FFFFFF' : '#FFB300'}
              />
              <GlobalText
                variant="body"
                style={[
                  styles.ratingText,
                  { color: state.currentFilters.rating === rating ? '#FFFFFF' : '#374151' }
                ]}
              >
                {rating === 0 ? 'همه' : rating.toString()}
              </GlobalText>
            </TouchableOpacity>
          ))}
        </View>
      </View>

      {/* Delivery Time Filter */}
      <View style={styles.section}>
        <GlobalText variant="heading" style={styles.sectionTitle}>
          زمان تحویل (دقیقه)
        </GlobalText>
        <View style={styles.deliveryContainer}>
          <GlobalText variant="body" style={styles.deliveryLabel}>
            حداکثر {state.currentFilters.deliveryTime} دقیقه
          </GlobalText>
          <CustomSlider
            minimumValue={15}
            maximumValue={90}
            step={15}
            value={state.currentFilters.deliveryTime}
            onValueChange={handleDeliveryTimeChange}
            minimumTrackTintColor="#e6034b"
            maximumTrackTintColor="#E5E7EB"
          />
        </View>
      </View>

      {/* Quick Filters */}
      <View style={styles.section}>
        <GlobalText variant="heading" style={styles.sectionTitle}>
          فیلترهای سریع
        </GlobalText>
        <View style={styles.quickFiltersContainer}>
          <TouchableOpacity
            style={[
              styles.quickFilterChip,
              { backgroundColor: state.currentFilters.isOpen ? '#e6034b' : '#F3F4F6' }
            ]}
            onPress={() => setFilters({ isOpen: !state.currentFilters.isOpen })}
          >
            <Ionicons
              name="checkmark-circle"
              size={16}
              color={state.currentFilters.isOpen ? '#FFFFFF' : '#10B981'}
            />
            <GlobalText
              variant="body"
              style={[
                styles.quickFilterText,
                { color: state.currentFilters.isOpen ? '#FFFFFF' : '#374151' }
              ]}
            >
              فقط باز
            </GlobalText>
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              styles.quickFilterChip,
              { backgroundColor: state.currentFilters.hasDelivery ? '#e6034b' : '#F3F4F6' }
            ]}
            onPress={() => setFilters({ hasDelivery: !state.currentFilters.hasDelivery })}
          >
            <Ionicons
              name="bicycle"
              size={16}
              color={state.currentFilters.hasDelivery ? '#FFFFFF' : '#3B82F6'}
            />
            <GlobalText
              variant="body"
              style={[
                styles.quickFilterText,
                { color: state.currentFilters.hasDelivery ? '#FFFFFF' : '#374151' }
              ]}
            >
              ارسال دارد
            </GlobalText>
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              styles.quickFilterChip,
              { backgroundColor: state.currentFilters.hasPromotion ? '#e6034b' : '#F3F4F6' }
            ]}
            onPress={() => setFilters({ hasPromotion: !state.currentFilters.hasPromotion })}
          >
            <Ionicons
              name="pricetag"
              size={16}
              color={state.currentFilters.hasPromotion ? '#FFFFFF' : '#F59E0B'}
            />
            <GlobalText
              variant="body"
              style={[
                styles.quickFilterText,
                { color: state.currentFilters.hasPromotion ? '#FFFFFF' : '#374151' }
              ]}
            >
              تخفیف دار
            </GlobalText>
          </TouchableOpacity>
        </View>
      </View>

      {/* Sort Options */}
      <View style={styles.section}>
        <GlobalText variant="heading" style={styles.sectionTitle}>
          مرتب‌سازی
        </GlobalText>
        <View style={styles.sortContainer}>
          {[
            { key: 'relevance', label: 'مرتبط‌ترین' },
            { key: 'rating', label: 'بالاترین امتیاز' },
            { key: 'delivery_time', label: 'سریع‌ترین ارسال' },
            { key: 'price_low', label: 'ارزان‌ترین' },
            { key: 'price_high', label: 'گران‌ترین' }
          ].map((sort) => (
            <TouchableOpacity
              key={sort.key}
              style={[
                styles.sortChip,
                { backgroundColor: state.currentFilters.sortBy === sort.key ? '#e6034b' : '#F3F4F6' }
              ]}
              onPress={() => handleSortChange(sort.key)}
            >
              <GlobalText
                variant="body"
                style={[
                  styles.sortText,
                  { color: state.currentFilters.sortBy === sort.key ? '#FFFFFF' : '#374151' }
                ]}
              >
                {sort.label}
              </GlobalText>
            </TouchableOpacity>
          ))}
        </View>
      </View>
    </ScrollView>
  );

  const renderResultsTab = () => (
    <View style={styles.tabContent}>
      {state.isSearching ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#e6034b" />
          <GlobalText variant="body" style={styles.loadingText}>
            در حال جستجو...
          </GlobalText>
        </View>
      ) : state.searchResults.restaurants.length > 0 ? (
        <FlatList
          data={state.searchResults.restaurants}
          renderItem={renderRestaurantCard}
          keyExtractor={(item) => item.id}
          showsVerticalScrollIndicator={false}
          ListHeaderComponent={
            <View style={styles.resultsHeader}>
              <GlobalText variant="body" style={styles.resultsInfo}>
                {state.searchResults.totalResults} نتیجه در {state.searchResults.searchTime} میلی‌ثانیه
              </GlobalText>
            </View>
          }
        />
      ) : (
        <View style={styles.emptyContainer}>
          <Ionicons name="search" size={64} color="#9CA3AF" />
          <GlobalText variant="heading" style={styles.emptyTitle}>
            نتیجه‌ای یافت نشد
          </GlobalText>
          <GlobalText variant="body" style={styles.emptyDescription}>
            لطفاً کلمات کلیدی یا فیلترهای خود را تغییر دهید
          </GlobalText>
        </View>
      )}
    </View>
  );

  return (
    <SafeAreaView style={styles.container} edges={['top', 'left', 'right']}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color="#374151" />
        </TouchableOpacity>
        <GlobalText variant="heading" style={styles.headerTitle}>
          جستجوی پیشرفته
        </GlobalText>
        <TouchableOpacity
          style={styles.clearButton}
          onPress={clearAllFilters}
        >
          <GlobalText variant="body" style={styles.clearButtonText}>
            پاک کردن
          </GlobalText>
        </TouchableOpacity>
      </View>

      {/* Tabs */}
      <View style={styles.tabBar}>
        <TouchableOpacity
          style={[styles.tab, { backgroundColor: activeTab === 'search' ? '#e6034b' : 'transparent' }]}
          onPress={() => setActiveTab('search')}
        >
          <Ionicons
            name="search"
            size={20}
            color={activeTab === 'search' ? '#FFFFFF' : '#9CA3AF'}
          />
          <GlobalText
            variant="body"
            style={[
              styles.tabText,
              { color: activeTab === 'search' ? '#FFFFFF' : '#9CA3AF' }
            ]}
          >
            جستجو
          </GlobalText>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.tab, { backgroundColor: activeTab === 'filters' ? '#e6034b' : 'transparent' }]}
          onPress={() => setActiveTab('filters')}
        >
          <Ionicons
            name="options"
            size={20}
            color={activeTab === 'filters' ? '#FFFFFF' : '#9CA3AF'}
          />
          <GlobalText
            variant="body"
            style={[
              styles.tabText,
              { color: activeTab === 'filters' ? '#FFFFFF' : '#9CA3AF' }
            ]}
          >
            فیلترها
          </GlobalText>
          {getActiveFiltersCount() > 0 && (
            <View style={styles.filterBadge}>
              <GlobalText variant="caption" style={styles.filterBadgeText}>
                {getActiveFiltersCount()}
              </GlobalText>
            </View>
          )}
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.tab, { backgroundColor: activeTab === 'results' ? '#e6034b' : 'transparent' }]}
          onPress={() => setActiveTab('results')}
        >
          <Ionicons
            name="list"
            size={20}
            color={activeTab === 'results' ? '#FFFFFF' : '#9CA3AF'}
          />
          <GlobalText
            variant="body"
            style={[
              styles.tabText,
              { color: activeTab === 'results' ? '#FFFFFF' : '#9CA3AF' }
            ]}
          >
            نتایج
          </GlobalText>
          {state.searchResults.totalResults > 0 && (
            <View style={styles.filterBadge}>
              <GlobalText variant="caption" style={styles.filterBadgeText}>
                {state.searchResults.totalResults}
              </GlobalText>
            </View>
          )}
        </TouchableOpacity>
      </View>

      {/* Content */}
      {activeTab === 'search' && renderSearchTab()}
      {activeTab === 'filters' && renderFiltersTab()}
      {activeTab === 'results' && renderResultsTab()}

      {/* Suggestions Modal */}
      <Modal
        visible={state.showSuggestions && state.suggestions.length > 0}
        transparent
        animationType="fade"
        onRequestClose={hideSuggestions}
      >
        <TouchableOpacity
          style={styles.modalOverlay}
          activeOpacity={1}
          onPress={hideSuggestions}
        >
          <View style={styles.suggestionsContainer}>
            <FlatList
              data={state.suggestions}
              renderItem={renderSuggestion}
              keyExtractor={(item) => item.id}
              showsVerticalScrollIndicator={false}
            />
          </View>
        </TouchableOpacity>
      </Modal>

      {/* Location Suggestions Modal */}
      <Modal
        visible={showLocationSuggestions && state.locationSuggestions.length > 0}
        transparent
        animationType="fade"
        onRequestClose={() => setShowLocationSuggestions(false)}
      >
        <TouchableOpacity
          style={styles.modalOverlay}
          activeOpacity={1}
          onPress={() => setShowLocationSuggestions(false)}
        >
          <View style={styles.suggestionsContainer}>
            <FlatList
              data={state.locationSuggestions}
              renderItem={renderLocationSuggestion}
              keyExtractor={(item) => item.id}
              showsVerticalScrollIndicator={false}
            />
          </View>
        </TouchableOpacity>
      </Modal>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#374151',
    flex: 1,
    textAlign: 'center',
  },
  clearButton: {
    padding: 8,
  },
  clearButtonText: {
    color: '#e6034b',
    fontSize: 14,
  },
  tabBar: {
    flexDirection: 'row',
    backgroundColor: '#F9FAFB',
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  tab: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    marginHorizontal: 4,
    borderRadius: 8,
    position: 'relative',
  },
  tabText: {
    fontSize: 14,
    marginLeft: 8,
  },
  filterBadge: {
    position: 'absolute',
    top: -4,
    right: -4,
    backgroundColor: '#EF4444',
    borderRadius: 10,
    minWidth: 20,
    height: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  filterBadgeText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: 'bold',
  },
  tabContent: {
    flex: 1,
    padding: 16,
  },
  searchContainer: {
    marginBottom: 16,
  },
  searchBox: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F9FAFB',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    marginLeft: 12,
    color: '#374151',
  },
  searchButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#e6034b',
    borderRadius: 12,
    paddingVertical: 16,
    marginBottom: 24,
  },
  searchButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 8,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#374151',
    marginBottom: 12,
  },
  historyItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    backgroundColor: '#F9FAFB',
    borderRadius: 8,
    marginBottom: 8,
  },
  historyText: {
    flex: 1,
    marginLeft: 12,
    color: '#374151',
  },
  historyCount: {
    color: '#9CA3AF',
    fontSize: 12,
  },
  popularContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  popularChip: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F3F4F6',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
    marginBottom: 8,
  },
  popularText: {
    color: '#374151',
    fontSize: 14,
  },
  popularCount: {
    color: '#9CA3AF',
    fontSize: 12,
    marginLeft: 4,
  },
  cuisineContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  cuisineChip: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
    marginBottom: 8,
  },
  cuisineIcon: {
    fontSize: 16,
    marginRight: 4,
  },
  cuisineText: {
    fontSize: 14,
    marginRight: 4,
  },
  cuisineCount: {
    fontSize: 12,
  },
  priceContainer: {
    alignItems: 'center',
  },
  priceLabel: {
    fontSize: 16,
    color: '#374151',
    marginBottom: 16,
  },
  sliderContainer: {
    width: '100%',
  },
  ratingContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  ratingChip: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
    marginBottom: 8,
  },
  ratingText: {
    fontSize: 14,
    marginLeft: 4,
  },
  deliveryContainer: {
    alignItems: 'center',
  },
  deliveryLabel: {
    fontSize: 16,
    color: '#374151',
    marginBottom: 16,
  },
  quickFiltersContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  quickFilterChip: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
    marginBottom: 8,
  },
  quickFilterText: {
    fontSize: 14,
    marginLeft: 4,
  },
  sortContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  sortChip: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
    marginBottom: 8,
  },
  sortText: {
    fontSize: 14,
  },
  loadingContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingText: {
    marginTop: 16,
    color: '#9CA3AF',
  },
  resultsHeader: {
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
    marginBottom: 16,
  },
  resultsInfo: {
    color: '#9CA3AF',
    fontSize: 14,
  },
  restaurantCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  restaurantInfo: {
    flex: 1,
  },
  restaurantName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#374151',
    marginBottom: 4,
  },
  restaurantDescription: {
    color: '#6B7280',
    fontSize: 14,
    marginBottom: 8,
  },
  restaurantMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16,
  },
  metaItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  metaText: {
    color: '#9CA3AF',
    fontSize: 12,
  },
  emptyContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 32,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#374151',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyDescription: {
    color: '#9CA3AF',
    textAlign: 'center',
    lineHeight: 20,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  suggestionsContainer: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    width: width * 0.9,
    maxHeight: 300,
    padding: 8,
  },
  suggestionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
  },
  suggestionIcon: {
    fontSize: 20,
    marginRight: 12,
  },
  suggestionContent: {
    flex: 1,
  },
  suggestionText: {
    fontSize: 16,
    color: '#374151',
  },
  suggestionSubtitle: {
    fontSize: 14,
    color: '#9CA3AF',
    marginTop: 2,
  },
}); 