import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Alert,
  Modal,
  ScrollView,
  ActivityIndicator,
  RefreshControl,
  TextInput,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { SafeAreaView } from 'react-native-safe-area-context';
import { usePayment } from '../contexts/PaymentContext';
import { Payment } from '../lib/api';

interface PaymentHistoryScreenProps {
  navigation: any;
}

const PaymentHistoryScreen: React.FC<PaymentHistoryScreenProps> = ({ navigation }) => {
  const {
    paymentHistory,
    isLoadingHistory,
    loadPaymentHistory,
    processRefund,
    paymentStats,
    loadPaymentStats,
  } = usePayment();

  const [selectedPayment, setSelectedPayment] = useState<Payment | null>(null);
  const [isDetailsModalVisible, setIsDetailsModalVisible] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterStatus, setFilterStatus] = useState<Payment['payment_status'] | 'all'>('all');
  const [page, setPage] = useState(1);
  const [isLoadingMore, setIsLoadingMore] = useState(false);

  useEffect(() => {
    loadPaymentStats();
  }, []);

  const filteredPayments = paymentHistory.filter(payment => {
    const matchesSearch = payment.transaction_id?.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         payment.amount.toString().includes(searchQuery);
    const matchesStatus = filterStatus === 'all' || payment.payment_status === filterStatus;
    return matchesSearch && matchesStatus;
  });

  const handleRefresh = async () => {
    setPage(1);
    await loadPaymentHistory(1);
    await loadPaymentStats();
  };

  const handleLoadMore = async () => {
    if (!isLoadingMore) {
      setIsLoadingMore(true);
      const nextPage = page + 1;
      await loadPaymentHistory(nextPage);
      setPage(nextPage);
      setIsLoadingMore(false);
    }
  };

  const handlePaymentPress = (payment: Payment) => {
    setSelectedPayment(payment);
    setIsDetailsModalVisible(true);
  };

  const handleRefund = async (payment: Payment) => {
    Alert.alert(
      'بازپرداخت',
      `آیا از بازپرداخت مبلغ ${formatPrice(payment.amount)} اطمینان دارید؟`,
      [
        { text: 'لغو', style: 'cancel' },
        {
          text: 'بازپرداخت',
          style: 'destructive',
          onPress: async () => {
            try {
              await processRefund(payment.id);
              Alert.alert('موفق', 'بازپرداخت با موفقیت انجام شد');
              setIsDetailsModalVisible(false);
            } catch (error) {
              // Error already handled in context
            }
          },
        },
      ]
    );
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('fa-IR', {
      style: 'currency',
      currency: 'IRR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(price);
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('fa-IR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    }).format(date);
  };

  const getStatusColor = (status: Payment['payment_status']) => {
    switch (status) {
      case 'completed':
        return '#10B981';
      case 'pending':
        return '#F59E0B';
      case 'failed':
        return '#EF4444';
      case 'refunded':
        return '#6B7280';
      default:
        return '#6B7280';
    }
  };

  const getStatusText = (status: Payment['payment_status']) => {
    switch (status) {
      case 'completed':
        return 'تکمیل شده';
      case 'pending':
        return 'در انتظار';
      case 'failed':
        return 'ناموفق';
      case 'refunded':
        return 'بازپرداخت شده';
      default:
        return 'نامشخص';
    }
  };

  const getPaymentMethodText = (method: Payment['payment_method']) => {
    switch (method) {
      case 'cash':
        return 'نقدی';
      case 'credit_card':
        return 'کارت اعتباری';
      case 'debit_card':
        return 'کارت نقدی';
      case 'mobile_wallet':
        return 'کیف پول موبایل';
      case 'bank_transfer':
        return 'انتقال بانکی';
      default:
        return 'نامشخص';
    }
  };

  const getPaymentMethodIcon = (method: Payment['payment_method']) => {
    switch (method) {
      case 'cash':
        return '💵';
      case 'credit_card':
      case 'debit_card':
        return '💳';
      case 'mobile_wallet':
        return '📱';
      case 'bank_transfer':
        return '🏦';
      default:
        return '💳';
    }
  };

  const renderPaymentItem = ({ item }: { item: Payment }) => (
    <TouchableOpacity
      style={styles.paymentCard}
      onPress={() => handlePaymentPress(item)}
    >
      <View style={styles.paymentHeader}>
        <View style={styles.paymentIcon}>
          <Text style={styles.paymentIconText}>
            {getPaymentMethodIcon(item.payment_method)}
          </Text>
        </View>
        <View style={styles.paymentInfo}>
          <Text style={styles.paymentAmount}>{formatPrice(item.amount)}</Text>
          <Text style={styles.paymentMethod}>
            {getPaymentMethodText(item.payment_method)}
          </Text>
          <Text style={styles.paymentDate}>{formatDate(item.created_at)}</Text>
        </View>
        <View style={styles.paymentStatus}>
          <View
            style={[
              styles.statusBadge,
              { backgroundColor: getStatusColor(item.payment_status) + '20' },
            ]}
          >
            <Text
              style={[
                styles.statusText,
                { color: getStatusColor(item.payment_status) },
              ]}
            >
              {getStatusText(item.payment_status)}
            </Text>
          </View>
          <Ionicons name="chevron-forward" size={16} color="#9CA3AF" />
        </View>
      </View>

      {item.transaction_id && (
        <View style={styles.paymentFooter}>
          <Text style={styles.transactionId}>
            شناسه تراکنش: {item.transaction_id}
          </Text>
        </View>
      )}
    </TouchableOpacity>
  );

  const renderStatsCard = () => {
    if (!paymentStats) return null;

    return (
      <View style={styles.statsCard}>
        <Text style={styles.statsTitle}>آمار پرداخت‌ها</Text>
        <View style={styles.statsGrid}>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>{paymentStats.total_payments}</Text>
            <Text style={styles.statLabel}>کل پرداخت‌ها</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={[styles.statValue, { color: '#10B981' }]}>
              {paymentStats.successful_payments}
            </Text>
            <Text style={styles.statLabel}>موفق</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={[styles.statValue, { color: '#F59E0B' }]}>
              {paymentStats.pending_payments}
            </Text>
            <Text style={styles.statLabel}>در انتظار</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={[styles.statValue, { color: '#EF4444' }]}>
              {paymentStats.failed_payments}
            </Text>
            <Text style={styles.statLabel}>ناموفق</Text>
          </View>
        </View>
        <View style={styles.statsRow}>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>
              {formatPrice(paymentStats.total_amount)}
            </Text>
            <Text style={styles.statLabel}>کل مبلغ</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>
              {paymentStats.success_rate.toFixed(1)}%
            </Text>
            <Text style={styles.statLabel}>نرخ موفقیت</Text>
          </View>
        </View>
      </View>
    );
  };

  const renderFilterButtons = () => (
    <ScrollView
      horizontal
      showsHorizontalScrollIndicator={false}
      style={styles.filterContainer}
      contentContainerStyle={styles.filterContent}
    >
      {[
        { key: 'all', label: 'همه' },
        { key: 'completed', label: 'تکمیل شده' },
        { key: 'pending', label: 'در انتظار' },
        { key: 'failed', label: 'ناموفق' },
        { key: 'refunded', label: 'بازپرداخت شده' },
      ].map((filter) => (
        <TouchableOpacity
          key={filter.key}
          style={[
            styles.filterButton,
            filterStatus === filter.key && styles.filterButtonActive,
          ]}
          onPress={() => setFilterStatus(filter.key as Payment['payment_status'] | 'all')}
        >
          <Text
            style={[
              styles.filterButtonText,
              filterStatus === filter.key && styles.filterButtonTextActive,
            ]}
          >
            {filter.label}
          </Text>
        </TouchableOpacity>
      ))}
    </ScrollView>
  );

  const renderPaymentDetails = () => {
    if (!selectedPayment) return null;

    return (
      <ScrollView style={styles.detailsContainer}>
        <View style={styles.detailsHeader}>
          <View style={styles.detailsIcon}>
            <Text style={styles.detailsIconText}>
              {getPaymentMethodIcon(selectedPayment.payment_method)}
            </Text>
          </View>
          <Text style={styles.detailsAmount}>
            {formatPrice(selectedPayment.amount)}
          </Text>
          <View
            style={[
              styles.detailsStatus,
              { backgroundColor: getStatusColor(selectedPayment.payment_status) + '20' },
            ]}
          >
            <Text
              style={[
                styles.detailsStatusText,
                { color: getStatusColor(selectedPayment.payment_status) },
              ]}
            >
              {getStatusText(selectedPayment.payment_status)}
            </Text>
          </View>
        </View>

        <View style={styles.detailsSection}>
          <Text style={styles.detailsSectionTitle}>اطلاعات پرداخت</Text>
          <View style={styles.detailsRow}>
            <Text style={styles.detailsLabel}>شناسه پرداخت:</Text>
            <Text style={styles.detailsValue}>{selectedPayment.id}</Text>
          </View>
          <View style={styles.detailsRow}>
            <Text style={styles.detailsLabel}>روش پرداخت:</Text>
            <Text style={styles.detailsValue}>
              {getPaymentMethodText(selectedPayment.payment_method)}
            </Text>
          </View>
          <View style={styles.detailsRow}>
            <Text style={styles.detailsLabel}>نوع پرداخت:</Text>
            <Text style={styles.detailsValue}>
              {selectedPayment.payment_type === 'online' ? 'آنلاین' : 'حضوری'}
            </Text>
          </View>
          {selectedPayment.transaction_id && (
            <View style={styles.detailsRow}>
              <Text style={styles.detailsLabel}>شناسه تراکنش:</Text>
              <Text style={styles.detailsValue}>{selectedPayment.transaction_id}</Text>
            </View>
          )}
          <View style={styles.detailsRow}>
            <Text style={styles.detailsLabel}>تاریخ ایجاد:</Text>
            <Text style={styles.detailsValue}>
              {formatDate(selectedPayment.created_at)}
            </Text>
          </View>
          <View style={styles.detailsRow}>
            <Text style={styles.detailsLabel}>آخرین به‌روزرسانی:</Text>
            <Text style={styles.detailsValue}>
              {formatDate(selectedPayment.updated_at)}
            </Text>
          </View>
        </View>

        {selectedPayment.gateway_response && (
          <View style={styles.detailsSection}>
            <Text style={styles.detailsSectionTitle}>پاسخ درگاه پرداخت</Text>
            <View style={styles.gatewayResponse}>
              <Text style={styles.gatewayResponseText}>
                {JSON.stringify(selectedPayment.gateway_response, null, 2)}
              </Text>
            </View>
          </View>
        )}

        {selectedPayment.payment_status === 'completed' && (
          <TouchableOpacity
            style={styles.refundButton}
            onPress={() => handleRefund(selectedPayment)}
          >
            <Ionicons name="return-up-back-outline" size={20} color="#EF4444" />
            <Text style={styles.refundButtonText}>درخواست بازپرداخت</Text>
          </TouchableOpacity>
        )}
      </ScrollView>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color="#1F2937" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>تاریخچه پرداخت</Text>
        <View style={styles.headerSpacer} />
      </View>

      <View style={styles.searchContainer}>
        <View style={styles.searchBox}>
          <Ionicons name="search" size={20} color="#9CA3AF" />
          <TextInput
            style={styles.searchInput}
            placeholder="جستجو در تاریخچه..."
            value={searchQuery}
            onChangeText={setSearchQuery}
            placeholderTextColor="#9CA3AF"
          />
        </View>
      </View>

      {renderFilterButtons()}

      <FlatList
        data={filteredPayments}
        renderItem={renderPaymentItem}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.listContainer}
        refreshControl={
          <RefreshControl
            refreshing={isLoadingHistory}
            onRefresh={handleRefresh}
            colors={['#3B82F6']}
          />
        }
        onEndReached={handleLoadMore}
        onEndReachedThreshold={0.5}
        ListHeaderComponent={renderStatsCard}
        ListEmptyComponent={
          <View style={styles.emptyState}>
            <Text style={styles.emptyStateIcon}>💳</Text>
            <Text style={styles.emptyStateTitle}>تاریخچه پرداختی وجود ندارد</Text>
            <Text style={styles.emptyStateDescription}>
              پرداخت‌های شما در اینجا نمایش داده می‌شود
            </Text>
          </View>
        }
        ListFooterComponent={
          isLoadingMore ? (
            <View style={styles.loadingMore}>
              <ActivityIndicator size="small" color="#3B82F6" />
            </View>
          ) : null
        }
      />

      {/* Payment Details Modal */}
      <Modal
        visible={isDetailsModalVisible}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <SafeAreaView style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <TouchableOpacity
              style={styles.modalCloseButton}
              onPress={() => setIsDetailsModalVisible(false)}
            >
              <Text style={styles.modalCloseButtonText}>بستن</Text>
            </TouchableOpacity>
            <Text style={styles.modalTitle}>جزئیات پرداخت</Text>
            <View style={styles.modalSpacer} />
          </View>
          {renderPaymentDetails()}
        </SafeAreaView>
      </Modal>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#F3F4F6',
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937',
  },
  headerSpacer: {
    width: 40,
  },
  searchContainer: {
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#FFFFFF',
  },
  searchBox: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F3F4F6',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
  },
  searchInput: {
    flex: 1,
    marginLeft: 8,
    fontSize: 16,
    color: '#1F2937',
  },
  filterContainer: {
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  filterContent: {
    paddingHorizontal: 20,
    paddingVertical: 12,
  },
  filterButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: '#F3F4F6',
    marginRight: 8,
  },
  filterButtonActive: {
    backgroundColor: '#3B82F6',
  },
  filterButtonText: {
    fontSize: 14,
    color: '#6B7280',
    fontWeight: '500',
  },
  filterButtonTextActive: {
    color: '#FFFFFF',
  },
  listContainer: {
    padding: 20,
  },
  statsCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  statsTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 16,
  },
  statsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  statsRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  statItem: {
    alignItems: 'center',
  },
  statValue: {
    fontSize: 18,
    fontWeight: '700',
    color: '#1F2937',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: '#6B7280',
  },
  paymentCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  paymentHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  paymentIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#F3F4F6',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  paymentIconText: {
    fontSize: 20,
  },
  paymentInfo: {
    flex: 1,
  },
  paymentAmount: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 4,
  },
  paymentMethod: {
    fontSize: 14,
    color: '#6B7280',
    marginBottom: 2,
  },
  paymentDate: {
    fontSize: 12,
    color: '#9CA3AF',
  },
  paymentStatus: {
    alignItems: 'flex-end',
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginBottom: 8,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '500',
  },
  paymentFooter: {
    marginTop: 12,
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
  },
  transactionId: {
    fontSize: 12,
    color: '#9CA3AF',
  },
  emptyState: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 60,
  },
  emptyStateIcon: {
    fontSize: 48,
    marginBottom: 16,
  },
  emptyStateTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 8,
  },
  emptyStateDescription: {
    fontSize: 14,
    color: '#6B7280',
    textAlign: 'center',
  },
  loadingMore: {
    paddingVertical: 20,
    alignItems: 'center',
  },
  modalContainer: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  modalCloseButton: {
    paddingVertical: 8,
  },
  modalCloseButtonText: {
    fontSize: 16,
    color: '#3B82F6',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937',
  },
  modalSpacer: {
    width: 40,
  },
  detailsContainer: {
    flex: 1,
    padding: 20,
  },
  detailsHeader: {
    alignItems: 'center',
    marginBottom: 24,
  },
  detailsIcon: {
    width: 64,
    height: 64,
    borderRadius: 32,
    backgroundColor: '#F3F4F6',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 16,
  },
  detailsIconText: {
    fontSize: 28,
  },
  detailsAmount: {
    fontSize: 24,
    fontWeight: '700',
    color: '#1F2937',
    marginBottom: 8,
  },
  detailsStatus: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  detailsStatusText: {
    fontSize: 14,
    fontWeight: '500',
  },
  detailsSection: {
    marginBottom: 24,
  },
  detailsSectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 12,
  },
  detailsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
  },
  detailsLabel: {
    fontSize: 14,
    color: '#6B7280',
  },
  detailsValue: {
    fontSize: 14,
    color: '#1F2937',
    fontWeight: '500',
    flex: 1,
    textAlign: 'right',
  },
  gatewayResponse: {
    backgroundColor: '#F3F4F6',
    borderRadius: 8,
    padding: 12,
  },
  gatewayResponseText: {
    fontSize: 12,
    color: '#6B7280',
    fontFamily: 'monospace',
  },
  refundButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#FEF2F2',
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 16,
    marginTop: 16,
  },
  refundButtonText: {
    fontSize: 16,
    color: '#EF4444',
    fontWeight: '500',
    marginLeft: 8,
  },
});

export default PaymentHistoryScreen; 