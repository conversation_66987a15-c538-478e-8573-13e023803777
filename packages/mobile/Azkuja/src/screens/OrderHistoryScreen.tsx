import React, { useState, useEffect } from 'react';
import {
  View,
  ScrollView,
  StyleSheet,
  Alert,
  RefreshControl,
  TouchableOpacity,
  Image,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';

import { GlobalText } from '../components/GlobalText';

type RootStackParamList = {
  LiveOrderTracking: { orderId: string };
};

type OrderHistoryScreenNavigationProp = StackNavigationProp<RootStackParamList, 'LiveOrderTracking'>;
import { Button } from '../components/ui/Button';
import { useTranslation } from '../i18n';
import apiService, { Order, PaginatedResponse } from '../lib/api';

export const OrderHistoryScreen: React.FC = () => {
  const { t } = useTranslation();
  const navigation = useNavigation<OrderHistoryScreenNavigationProp>();
  
  const getStatusLabel = (status: Order['status']) => {
    switch (status) {
      case 'pending': return 'در انتظار تأیید';
      case 'confirmed': return 'تأیید شده';
      case 'preparing': return 'در حال آماده‌سازی';
      case 'ready': return 'آماده تحویل';
      case 'delivered': return 'تحویل داده شده';
      case 'cancelled': return 'لغو شده';
      default: return status;
    }
  };

  const [orders, setOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);

  useEffect(() => {
    loadOrders();
  }, []);

  const loadOrders = async (pageNum = 1, refresh = false) => {
    try {
      if (refresh) {
        setRefreshing(true);
      } else {
        setLoading(true);
      }

      const response: PaginatedResponse<Order> = await apiService.getUserOrders(pageNum, 10);
      
      if (refresh || pageNum === 1) {
        setOrders(response.data);
      } else {
        setOrders(prev => [...prev, ...response.data]);
      }

      setPage(pageNum);
      setHasMore(pageNum < response.totalPages);

    } catch (error) {
      console.error('Error loading orders:', error);
      Alert.alert('خطا', 'خطا در بارگذاری سفارشات');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const onRefresh = () => {
    loadOrders(1, true);
  };

  const loadMoreOrders = () => {
    if (hasMore && !loading) {
      loadOrders(page + 1);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return '#FF9800';
      case 'confirmed': return '#2196F3';
      case 'preparing': return '#FF5722';
      case 'ready': return '#4CAF50';
      case 'delivered': return '#8BC34A';
      case 'cancelled': return '#F44336';
      default: return '#757575';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending': return 'در انتظار تأیید';
      case 'confirmed': return 'تأیید شده';
      case 'preparing': return 'در حال آماده‌سازی';
      case 'ready': return 'آماده تحویل';
      case 'delivered': return 'تحویل داده شده';
      case 'cancelled': return 'لغو شده';
      default: return status;
    }
  };

  const handleCancelOrder = (orderId: string) => {
    Alert.alert(
      'لغو سفارش',
      'آیا مطمئن هستید که می‌خواهید این سفارش را لغو کنید؟',
      [
        { text: 'خیر', style: 'cancel' },
        {
          text: 'بله، لغو کن',
          style: 'destructive',
          onPress: async () => {
            try {
              await apiService.cancelOrder(orderId);
              Alert.alert('موفق', 'سفارش با موفقیت لغو شد');
              onRefresh();
            } catch (error) {
              Alert.alert('خطا', 'خطا در لغو سفارش');
            }
          }
        }
      ]
    );
  };

  const renderOrderItem = (order: Order) => (
    <View key={order.id} style={styles.orderCard}>
      {/* Order Header */}
      <View style={styles.orderHeader}>
        <View style={styles.orderInfo}>
          <GlobalText variant="heading" style={styles.restaurantName}>
            {order.restaurant?.name}
          </GlobalText>
          <GlobalText variant="body" style={styles.orderDate}>
            {new Date(order.created_at).toLocaleDateString('fa-IR')}
          </GlobalText>
        </View>
        
        <View style={[styles.statusBadge, { backgroundColor: getStatusColor(order.status) }]}>
          <GlobalText variant="body" style={styles.statusText}>
            {getStatusText(order.status)}
          </GlobalText>
        </View>
      </View>

      {/* Order Items */}
      <View style={styles.orderItems}>
        {order.items?.slice(0, 2).map((item, index) => (
          <View key={index} style={styles.orderItem}>
            <Image
              source={{ 
                uri: item.menuItem?.imageUrl || 'https://images.unsplash.com/photo-1555396273-367ea4eb4db5?w=400'
              }}
              style={styles.itemImage}
              resizeMode="cover"
            />
            <View style={styles.itemDetails}>
              <GlobalText variant="body" style={styles.itemName}>
                {item.menuItem?.name}
              </GlobalText>
              <GlobalText variant="body" style={styles.itemQuantity}>
                تعداد: {item.quantity}
              </GlobalText>
            </View>
            <GlobalText variant="body" style={styles.itemPrice}>
              {(item.price * item.quantity).toLocaleString()} ؋
            </GlobalText>
          </View>
        ))}
        
        {order.items && order.items.length > 2 && (
          <GlobalText variant="body" style={styles.moreItems}>
            و {order.items.length - 2} آیتم دیگر...
          </GlobalText>
        )}
      </View>

      {/* Order Footer */}
      <View style={styles.orderFooter}>
        <View style={styles.totalSection}>
          <GlobalText variant="heading" style={styles.totalLabel}>
            مجموع:
          </GlobalText>
          <GlobalText variant="heading" style={styles.totalAmount}>
            {order.total_amount.toLocaleString()} ؋
          </GlobalText>
        </View>

        <View style={styles.orderActions}>
          {order.status === 'pending' && (
            <Button
              title="لغو سفارش"
              onPress={() => handleCancelOrder(order.id)}
              variant="outline"
              size="small"
              style={styles.cancelButton}
            />
          )}
          
          <Button
            title="جزئیات"
                            onPress={() => {
                  if (order.status === 'delivered' || order.status === 'cancelled') {
                    Alert.alert('جزئیات سفارش', `سفارش ${order.id} - ${getStatusLabel(order.status)}`);
                  } else {
                    navigation.navigate('LiveOrderTracking', { orderId: order.id });
                  }
                }}
            size="small"
            style={styles.detailsButton}
          />
        </View>
      </View>

      {/* Delivery Address */}
      {order.delivery_address && (
        <View style={styles.deliveryInfo}>
          <Ionicons name="location-outline" size={16} color="#757575" />
          <GlobalText variant="body" style={styles.deliveryAddress}>
            {order.delivery_address}
          </GlobalText>
        </View>
      )}
    </View>
  );

  if (loading && orders.length === 0) {
    return (
      <SafeAreaView style={styles.container} edges={['top', 'left', 'right']}>
        <View style={styles.header}>
          <GlobalText variant="heading" style={styles.headerTitle}>
            تاریخچه سفارشات
          </GlobalText>
        </View>
        
        <View style={styles.loadingContainer}>
          <GlobalText variant="body" style={styles.loadingText}>
            در حال بارگذاری...
          </GlobalText>
        </View>
      </SafeAreaView>
    );
  }

  if (orders.length === 0) {
    return (
      <SafeAreaView style={styles.container} edges={['top', 'left', 'right']}>
        <View style={styles.header}>
          <GlobalText variant="heading" style={styles.headerTitle}>
            تاریخچه سفارشات
          </GlobalText>
        </View>
        
        <View style={styles.emptyContainer}>
          <Ionicons name="receipt-outline" size={64} color="#E0E0E0" />
          <GlobalText variant="heading" style={styles.emptyTitle}>
            هنوز سفارشی ندارید
          </GlobalText>
          <GlobalText variant="body" style={styles.emptySubtitle}>
            اولین سفارش خود را از رستوران‌های مورد علاقه‌تان ثبت کنید
          </GlobalText>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container} edges={['top', 'left', 'right']}>
      {/* Header */}
      <View style={styles.header}>
        <GlobalText variant="heading" style={styles.headerTitle}>
          تاریخچه سفارشات ({orders.length})
        </GlobalText>
      </View>

      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={['#e6034b']}
          />
        }
        onScroll={({ nativeEvent }) => {
          const { layoutMeasurement, contentOffset, contentSize } = nativeEvent;
          const isCloseToBottom = layoutMeasurement.height + contentOffset.y >= contentSize.height - 20;
          
          if (isCloseToBottom) {
            loadMoreOrders();
          }
        }}
        scrollEventThrottle={400}
      >
        <View style={styles.ordersContainer}>
          {orders.map(renderOrderItem)}
          
          {loading && orders.length > 0 && (
            <View style={styles.loadingMore}>
              <GlobalText variant="body" style={styles.loadingText}>
                در حال بارگذاری بیشتر...
              </GlobalText>
            </View>
          )}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FAFAFA',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#FFFFFF',
    elevation: 2,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  headerTitle: {
    color: '#212121',
    fontSize: 20,
  },
  scrollView: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    color: '#757575',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  emptyTitle: {
    color: '#757575',
    fontSize: 20,
    marginTop: 16,
    textAlign: 'center',
  },
  emptySubtitle: {
    color: '#757575',
    fontSize: 14,
    textAlign: 'center',
    marginTop: 8,
  },
  ordersContainer: {
    padding: 16,
  },
  orderCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    marginBottom: 16,
    elevation: 2,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    overflow: 'hidden',
  },
  orderHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  orderInfo: {
    flex: 1,
  },
  restaurantName: {
    color: '#212121',
    fontSize: 16,
    marginBottom: 4,
  },
  orderDate: {
    color: '#757575',
    fontSize: 12,
  },
  statusBadge: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  statusText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: 'bold',
  },
  orderItems: {
    padding: 16,
  },
  orderItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  itemImage: {
    width: 50,
    height: 50,
    borderRadius: 8,
    marginRight: 12,
  },
  itemDetails: {
    flex: 1,
  },
  itemName: {
    color: '#212121',
    fontSize: 14,
    marginBottom: 2,
  },
  itemQuantity: {
    color: '#757575',
    fontSize: 12,
  },
  itemPrice: {
    color: '#e6034b',
    fontSize: 14,
    fontWeight: 'bold',
  },
  moreItems: {
    color: '#757575',
    fontSize: 12,
    fontStyle: 'italic',
    textAlign: 'center',
    marginTop: 8,
  },
  orderFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#F0F0F0',
  },
  totalSection: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  totalLabel: {
    color: '#212121',
    fontSize: 16,
    marginRight: 8,
  },
  totalAmount: {
    color: '#e6034b',
    fontSize: 18,
  },
  orderActions: {
    flexDirection: 'row',
  },
  cancelButton: {
    marginRight: 8,
    borderColor: '#F44336',
  },
  detailsButton: {
    backgroundColor: '#e6034b',
  },
  deliveryInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingBottom: 16,
  },
  deliveryAddress: {
    color: '#757575',
    fontSize: 12,
    marginLeft: 8,
    flex: 1,
  },
  loadingMore: {
    padding: 16,
    alignItems: 'center',
  },
}); 