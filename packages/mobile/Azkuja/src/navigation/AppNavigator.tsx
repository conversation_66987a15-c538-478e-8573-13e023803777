import React, { useEffect } from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { createStackNavigator } from '@react-navigation/stack';
import { View, StyleSheet, TouchableOpacity, Platform } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

import { useAuth } from '../contexts/AuthContext';
import { useTranslation } from '../i18n';
import { GlobalText } from '../components/GlobalText';

// Auth Screens
import { PhoneLoginScreen, OtpVerificationScreen, RegisterScreen, ForgotPasswordScreen } from '../screens/auth';
import { OnboardingScreen } from '../screens/onboarding';

// Main App Screens
import { ExploreScreen } from '../screens/ExploreScreen';
import { RestaurantDetailScreen } from '../screens/RestaurantDetailScreen';
import { CartScreen } from '../screens/CartScreen';
import { OrderHistoryScreen } from '../screens/OrderHistoryScreen';
import { ProfileScreen } from '../screens/ProfileScreen';
import { FavoritesScreen } from '../screens/FavoritesScreen';
import PaymentMethodsScreen from '../screens/PaymentMethodsScreen';
import PaymentHistoryScreen from '../screens/PaymentHistoryScreen';
import { AdvancedSearchScreen } from '../screens/AdvancedSearchScreen';
import { NotificationsScreen } from '../screens/NotificationsScreen';
import { LiveOrderTrackingScreen } from '../screens/LiveOrderTrackingScreen';
// import { SearchFiltersScreen } from '../screens/SearchFiltersScreen';

// Temporary placeholder screens
const PlaceholderScreen = ({ title, icon }: { title: string; icon: string }) => {
  const { t } = useTranslation();
  
  return (
    <View style={styles.placeholderContainer}>
      <GlobalText style={styles.placeholderIcon}>{icon}</GlobalText>
      <GlobalText variant="heading" style={styles.placeholderTitle}>
        {title}
      </GlobalText>
      <GlobalText variant="body" style={styles.placeholderSubtitle}>
        قریباً راه‌اندازی می‌شود...
      </GlobalText>
    </View>
  );
};

const HomeScreen = () => (
  <PlaceholderScreen title="خانه" icon="🏠" />
);

// OrdersScreen is now replaced with OrderHistoryScreen

// ProfileScreen is now imported from '../screens/ProfileScreen'

const Tab = createBottomTabNavigator();
const Stack = createStackNavigator();
const AuthStack = createStackNavigator();

// Authentication Stack
const AuthNavigator = () => {
  const { hasCompletedOnboarding } = useAuth();
  console.log('🔐 AppNavigator: Rendering AuthNavigator (unauthenticated state)');
  console.log('📋 AppNavigator: Has completed onboarding:', hasCompletedOnboarding);
  
  return (
    <AuthStack.Navigator 
      screenOptions={{ headerShown: false }}
      initialRouteName={hasCompletedOnboarding ? "Login" : "Onboarding"}
    >
      <AuthStack.Screen name="Onboarding" component={OnboardingScreen} />
      <AuthStack.Screen name="Login" component={PhoneLoginScreen} />
      <AuthStack.Screen name="OtpVerification" component={OtpVerificationScreen} />
      <AuthStack.Screen name="Register" component={RegisterScreen} />
      <AuthStack.Screen name="ForgotPassword" component={ForgotPasswordScreen} />
    </AuthStack.Navigator>
  );
};

// Main App Tab Navigator
const MainTabNavigator = () => {
  const { t } = useTranslation();
  const insets = useSafeAreaInsets();
  console.log('📱 AppNavigator: Rendering MainTabNavigator (authenticated state)');

  return (
    <Tab.Navigator
      screenOptions={{
        headerShown: false,
        tabBarStyle: {
          backgroundColor: '#FFFFFF',
          borderTopColor: '#E0E0E0',
          height: Platform.OS === 'android' ? 70 + insets.bottom : 60 + insets.bottom,
          paddingBottom: Platform.OS === 'android' ? Math.max(insets.bottom + 10, 16) : Math.max(insets.bottom, 8),
          paddingTop: 8,
          elevation: 8, // Android shadow
          shadowColor: '#000', // iOS shadow
          shadowOffset: { width: 0, height: -2 },
          shadowOpacity: 0.1,
          shadowRadius: 4,
        },
        tabBarActiveTintColor: '#e6034b',
        tabBarInactiveTintColor: '#757575',
        tabBarLabelStyle: {
          fontSize: 12,
          fontWeight: '500',
        },
      }}
    >
      <Tab.Screen 
        name="Home" 
        component={HomeScreen}
        options={{
          title: 'خانه',
          tabBarIcon: ({ color, size }) => (
            <Ionicons name="home-outline" size={size} color={color} />
          ),
        }}
      />
      <Tab.Screen 
        name="Explore" 
        component={ExploreScreen}
        options={{
          title: 'کاوش',
          tabBarIcon: ({ color, size }) => (
            <Ionicons name="search-outline" size={size} color={color} />
          ),
        }}
      />
      <Tab.Screen 
        name="Favorites" 
        component={FavoritesScreen}
        options={{
          title: 'علاقه‌مندی‌ها',
          tabBarIcon: ({ color, size }) => (
            <Ionicons name="heart-outline" size={size} color={color} />
          ),
        }}
      />
      <Tab.Screen 
        name="Orders" 
        component={OrderHistoryScreen}
        options={{
          title: 'سفارشات',
          tabBarIcon: ({ color, size }) => (
            <Ionicons name="receipt-outline" size={size} color={color} />
          ),
        }}
      />
      <Tab.Screen 
        name="Notifications" 
        component={NotificationsScreen}
        options={{
          title: 'اعلان‌ها',
          tabBarIcon: ({ color, size }) => (
            <Ionicons name="notifications-outline" size={size} color={color} />
          ),
        }}
      />
      <Tab.Screen 
        name="Profile" 
        component={ProfileScreen}
        options={{
          title: 'پروفایل',
          tabBarIcon: ({ color, size }) => (
            <Ionicons name="person-outline" size={size} color={color} />
          ),
        }}
      />
    </Tab.Navigator>
  );
};

// Main App Stack Navigator (for screens that open over tabs)
const AppStackNavigator = () => {
  console.log('🏗️ AppNavigator: Rendering AppStackNavigator (main app)');
  
  return (
    <Stack.Navigator>
      <Stack.Screen 
        name="MainTabs" 
        component={MainTabNavigator} 
        options={{ headerShown: false }}
      />
      <Stack.Screen 
        name="RestaurantDetail" 
        component={RestaurantDetailScreen}
        options={{ headerShown: false }}
      />
      <Stack.Screen 
        name="Cart" 
        component={CartScreen}
        options={{ headerShown: false }}
      />
      <Stack.Screen 
        name="PaymentMethods" 
        component={PaymentMethodsScreen}
        options={{ headerShown: false }}
      />
      <Stack.Screen 
        name="PaymentHistory" 
        component={PaymentHistoryScreen}
        options={{ headerShown: false }}
      />
      <Stack.Screen 
        name="AdvancedSearch" 
        component={AdvancedSearchScreen}
        options={{ headerShown: false }}
      />
      <Stack.Screen 
        name="LiveOrderTracking" 
        component={LiveOrderTrackingScreen}
        options={{ headerShown: false }}
      />
      {/* SearchFilters screen - will be added when screen is created
      <Stack.Screen 
        name="SearchFilters" 
        component={SearchFiltersScreen}
        options={{ headerShown: false }}
      />
      */}
    </Stack.Navigator>
  );
};

// Root Navigator with Authentication Guard
export const AppNavigator = () => {
  const { user, isLoading, isAuthenticated, hasCompletedOnboarding } = useAuth();

  // Debug logging for navigation decisions
  useEffect(() => {
    console.log('🧭 AppNavigator: Auth state changed');
    console.log('👤 AppNavigator: User:', user ? `${user.name} (${user.phone_number})` : 'null');
    console.log('⏳ AppNavigator: Loading:', isLoading);
    console.log('🔒 AppNavigator: Authenticated:', isAuthenticated);
    console.log('📋 AppNavigator: Onboarding completed:', hasCompletedOnboarding);
    console.log('📍 AppNavigator: Will render:', isLoading ? 'Loading' : (user ? 'Main App' : 'Auth Flow'));
  }, [user, isLoading, isAuthenticated, hasCompletedOnboarding]);

  if (isLoading) {
    console.log('⏳ AppNavigator: Rendering loading screen');
    return (
      <View style={styles.loadingContainer}>
        <GlobalText variant="body">در حال بارگذاری...</GlobalText>
      </View>
    );
  }

  console.log('🎯 AppNavigator: Final navigation decision - User exists:', !!user);
  
  return (
    <NavigationContainer>
      {user ? <AppStackNavigator /> : <AuthNavigator />}
    </NavigationContainer>
  );
};

const styles = StyleSheet.create({
  placeholderContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
    backgroundColor: '#FAFAFA',
  },
  placeholderIcon: {
    fontSize: 64,
    marginBottom: 16,
  },
  placeholderTitle: {
    fontSize: 24,
    marginBottom: 8,
    textAlign: 'center',
    color: '#212121',
  },
  placeholderSubtitle: {
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 32,
    color: '#757575',
  },

  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#FAFAFA',
  },
}); 