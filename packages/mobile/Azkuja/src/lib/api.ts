// Mobile API Service - matches backend endpoints exactly
import AsyncStorage from '@react-native-async-storage/async-storage';

const API_BASE_URL = process.env.EXPO_PUBLIC_API_URL || 'http://192.168.222.17:7000';

// Mock mode flag - set to false to use real backend API
const MOCK_MODE = false;

// Types matching backend entities
export interface User {
  id: string;
  phone_number: string;
  email?: string;
  name: string;
  role: 'admin' | 'restaurant_owner' | 'customer';
  created_at: string;
  updated_at: string;
}

export interface AuthResponse {
  accessToken: string;
  user: User;
}

// DTOs matching backend
export interface RegisterDto {
  phone_number: string;
  name: string;
  email?: string;
  role?: 'customer' | 'restaurant_owner';
}

export interface LoginDto {
  phone_number: string;
}

export interface VerifyOtpDto {
  phone_number: string;
  otp_code: string;
}

// API Response types
export interface ApiResponse<T = any> {
  data?: T;
  message?: string;
  error?: string;
}

export interface Restaurant {
  id: string;
  name: string;
  description?: string;
  address: string;
  city: string;
  state: string;
  zip_code: string;
  phone: string;
  email?: string;
  website?: string;
  hours_of_operation?: string;
  latitude?: number;
  longitude?: number;
  avg_rating: number;
  price_range: number;
  is_featured: boolean;
  is_open?: boolean;
  has_delivery?: boolean;
  delivery_time?: number; // in minutes
  status: 'active' | 'pending' | 'suspended';
  created_at: string;
  updated_at: string;
  owner?: {
    id: string;
    name: string;
  };
  photos?: Photo[];
  menuItems?: MenuItem[];
  categories?: Category[];
}

export interface Photo {
  id: string;
  url: string;
  caption?: string;
}

export interface MenuItem {
  id: string;
  name: string;
  description: string;
  price: number;
  category: string;
  imageUrl?: string;
  dietaryInfo?: string;
  isAvailable: boolean;
  isVegetarian: boolean;
  isVegan: boolean;
  isGlutenFree: boolean;
  spicyLevel: number;
}

export interface Category {
  id: string;
  name: string;
  description?: string;
}

export interface RestaurantFilters {
  search?: string;
  city?: string;
  state?: string;
  category_id?: string;
  price_range?: number;
  min_rating?: number;
  status?: string;
  featured?: boolean;
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'ASC' | 'DESC';
}

export interface Order {
  id: string;
  status: 'pending' | 'confirmed' | 'preparing' | 'ready' | 'delivered' | 'cancelled';
  total_amount: number;
  delivery_fee?: number;
  tax_amount?: number;
  delivery_address?: string;
  special_instructions?: string;
  estimated_delivery_time?: string;
  created_at: string;
  updated_at: string;
  customer?: User;
  restaurant?: Restaurant;
  items?: OrderItem[];
}

export interface OrderItem {
  id: string;
  quantity: number;
  price: number;
  special_instructions?: string;
  menuItem?: MenuItem;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface OrderTracking {
  id: string;
  orderId: string;
  status: 'confirmed' | 'preparing' | 'ready' | 'picked_up' | 'on_the_way' | 'delivered' | 'cancelled';
  estimatedTime: string;
  actualTime?: string;
  location?: {
    lat: number;
    lng: number;
  };
  driverInfo?: {
    name: string;
    phone: string;
    photo?: string;
  };
  createdAt: string;
  updatedAt: string;
}

export interface Favorite {
  id: string;
  userId: string;
  restaurantId: string;
  restaurant?: Restaurant;
  created_at: string;
  updated_at: string;
}

// Payment related interfaces
export interface PaymentMethod {
  id: string;
  type: 'cash' | 'credit_card' | 'debit_card' | 'mobile_wallet' | 'bank_transfer';
  name: string;
  display_name: string;
  is_default: boolean;
  is_active: boolean;
  icon: string;
  description?: string;
  // For cards
  card_number?: string; // Masked
  card_holder_name?: string;
  expiry_date?: string;
  card_type?: 'visa' | 'mastercard' | 'amex' | 'discover';
  // For mobile wallets
  wallet_id?: string;
  wallet_provider?: string;
  // For bank transfers
  bank_name?: string;
  account_number?: string; // Masked
  created_at?: string;
  updated_at?: string;
}

export interface Payment {
  id: string;
  order_id: string;
  user_id: string;
  amount: number;
  payment_method: PaymentMethod['type'];
  payment_status: 'pending' | 'completed' | 'failed' | 'refunded';
  payment_type: 'in_person' | 'online';
  transaction_id?: string;
  gateway_response?: Record<string, any>;
  created_at: string;
  updated_at: string;
}

export interface PaymentGateway {
  id: string;
  name: string;
  provider: 'stripe' | 'paypal' | 'razorpay' | 'custom';
  is_active: boolean;
  configuration: Record<string, any>;
}

export interface CreatePaymentRequest {
  order_id: string;
  payment_method: PaymentMethod['type'];
  payment_type: 'in_person' | 'online';
  amount: number;
  transaction_id?: string;
  gateway_response?: Record<string, any>;
}

export interface PaymentStats {
  total_payments: number;
  successful_payments: number;
  failed_payments: number;
  pending_payments: number;
  refunded_payments: number;
  total_amount: number;
  success_rate: number;
  average_amount: number;
}

// Advanced Search & Filtering interfaces
export interface SearchFilters {
  query: string;
  location: string;
  cuisine: string[];
  priceRange: [number, number];
  rating: number;
  deliveryTime: number;
  isOpen: boolean;
  hasDelivery: boolean;
  hasPromotion: boolean;
  sortBy: 'relevance' | 'rating' | 'delivery_time' | 'price_low' | 'price_high' | 'distance';
}

export interface SearchSuggestion {
  id: string;
  type: 'restaurant' | 'cuisine' | 'location' | 'dish' | 'category';
  text: string;
  subtitle?: string;
  icon?: string;
  data?: any; // Additional data for the suggestion
}

export interface SearchHistory {
  id: string;
  query: string;
  filters: Partial<SearchFilters>;
  timestamp: string;
  resultCount: number;
}

export interface PopularSearch {
  id: string;
  query: string;
  count: number;
  category?: string;
}

export interface SearchAnalytics {
  totalSearches: number;
  popularQueries: PopularSearch[];
  recentSearches: SearchHistory[];
  topCuisines: { cuisine: string; count: number }[];
  topLocations: { location: string; count: number }[];
}

export interface CuisineOption {
  id: string;
  name: string;
  count: number;
  icon?: string;
}

export interface LocationSuggestion {
  id: string;
  name: string;
  city: string;
  state?: string;
  country: string;
  coordinates?: {
    latitude: number;
    longitude: number;
  };
}

export interface AdvancedSearchRequest {
  filters: SearchFilters;
  pagination?: {
    page: number;
    limit: number;
  };
  location?: {
    latitude: number;
    longitude: number;
    radius?: number; // in km
  };
}

export interface AdvancedSearchResponse {
  restaurants: Restaurant[];
  menuItems: MenuItem[];
  totalResults: number;
  suggestions: SearchSuggestion[];
  facets: {
    cuisines: CuisineOption[];
    priceRanges: { range: [number, number]; count: number }[];
    ratings: { rating: number; count: number }[];
    locations: LocationSuggestion[];
  };
  searchTime: number; // in ms
}

// Real-time Features & Notifications interfaces
export interface NotificationSettings {
  orderUpdates: boolean;
  promotions: boolean;
  newRestaurants: boolean;
  reminders: boolean;
  marketing: boolean;
  pushEnabled: boolean;
  emailEnabled: boolean;
  smsEnabled: boolean;
}

export interface PushNotification {
  id: string;
  type: 'order_update' | 'promotion' | 'reminder' | 'marketing' | 'system';
  title: string;
  body: string;
  data?: Record<string, any>;
  read: boolean;
  created_at: string;
  scheduled_at?: string;
  image_url?: string;
  action_url?: string;
  priority: 'low' | 'normal' | 'high';
}

export interface OrderUpdate {
  id: string;
  order_id: string;
  status: Order['status'];
  message: string;
  timestamp: string;
  estimated_time?: string;
  location?: {
    latitude: number;
    longitude: number;
  };
  driver_info?: {
    name: string;
    phone: string;
    photo?: string;
    rating?: number;
  };
}

export interface LiveOrderTracking {
  order_id: string;
  current_status: Order['status'];
  updates: OrderUpdate[];
  estimated_delivery: string;
  preparation_time?: number;
  delivery_time?: number;
  restaurant_location: {
    latitude: number;
    longitude: number;
    address: string;
  };
  delivery_location: {
    latitude: number;
    longitude: number;
    address: string;
  };
  driver_location?: {
    latitude: number;
    longitude: number;
    last_updated: string;
  };
}

export interface WebSocketMessage {
  type: 'order_update' | 'notification' | 'system' | 'chat';
  data: any;
  timestamp: string;
  id: string;
}

export interface ConnectionStatus {
  connected: boolean;
  reconnecting: boolean;
  lastConnected?: string;
  reconnectAttempts: number;
}

export interface ChatMessage {
  id: string;
  order_id: string;
  sender_type: 'customer' | 'restaurant' | 'driver' | 'support';
  sender_id: string;
  sender_name: string;
  message: string;
  timestamp: string;
  read: boolean;
  message_type: 'text' | 'image' | 'location' | 'system';
  metadata?: Record<string, any>;
}

export interface LiveChat {
  id: string;
  order_id: string;
  participants: {
    customer: { id: string; name: string; avatar?: string };
    restaurant: { id: string; name: string; avatar?: string };
    driver?: { id: string; name: string; avatar?: string };
  };
  messages: ChatMessage[];
  status: 'active' | 'closed';
  created_at: string;
  updated_at: string;
}

export interface SystemStatus {
  server_status: 'online' | 'maintenance' | 'offline';
  features: {
    orders: boolean;
    payments: boolean;
    notifications: boolean;
    chat: boolean;
  };
  maintenance_message?: string;
  estimated_downtime?: string;
}

class ApiService {
  private token: string | null = null;
  private baseURL: string;

  constructor() {
    this.baseURL = API_BASE_URL;
    this.loadToken();
  }

  private async loadToken() {
    try {
      const token = await AsyncStorage.getItem('accessToken');
      this.token = token;
    } catch (error) {
      console.warn('Failed to load token from storage:', error);
    }
  }

  private async saveToken(token: string) {
    try {
      this.token = token;
      await AsyncStorage.setItem('accessToken', token);
    } catch (error) {
      console.error('Failed to save token:', error);
    }
  }

  private async removeToken() {
    try {
      this.token = null;
      await AsyncStorage.removeItem('accessToken');
      await AsyncStorage.removeItem('user');
    } catch (error) {
      console.error('Failed to remove token:', error);
    }
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseURL}${endpoint}`;
    
    console.log('🌐 API Request Debug:');
    console.log('  → URL:', url);
    console.log('  → Method:', options.method || 'GET');
    console.log('  → Base URL:', this.baseURL);
    console.log('  → Endpoint:', endpoint);
    
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      ...(options.headers as Record<string, string>),
    };

    if (this.token) {
      headers.Authorization = `Bearer ${this.token}`;
      console.log('  → Token present:', this.token.substring(0, 20) + '...');
    } else {
      console.log('  → No token present');
    }

    console.log('  → Headers:', JSON.stringify(headers, null, 2));
    if (options.body) {
      console.log('  → Body:', options.body);
    }

    try {
      console.log('🚀 Making fetch request...');
      const response = await fetch(url, {
        ...options,
        headers,
      });

      console.log('📡 Response received:');
      console.log('  → Status:', response.status);
      console.log('  → Status Text:', response.statusText);
      console.log('  → OK:', response.ok);

      const data = await response.json();
      console.log('  → Response Data:', JSON.stringify(data, null, 2));

      if (!response.ok) {
        console.error('❌ API Error - Response not OK');
        throw new Error(data.message || `HTTP ${response.status}: ${response.statusText}`);
      }

      console.log('✅ API Request successful');
      return data;
    } catch (error) {
      console.error('💥 API Request failed:');
      console.error('  → Error type:', error.constructor.name);
      console.error('  → Error message:', error.message);
      console.error('  → Full error:', error);
      
      // Enhanced error detection
      if (error instanceof TypeError) {
        if (error.message.includes('fetch')) {
          console.error('🌐 Network Error Detected: Fetch failed');
          console.error('  → This usually means:');
          console.error('    1. Backend server is not running');
          console.error('    2. Wrong URL/port');
          console.error('    3. CORS issues');
          console.error('    4. Network connectivity issues');
        }
      }
      
      throw error;
    }
  }

  // Authentication methods matching backend endpoints
  async register(data: RegisterDto): Promise<{ message: string }> {
    return this.request<{ message: string }>('/api/auth/register', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async login(data: LoginDto): Promise<{ message: string }> {
    return this.request<{ message: string }>('/api/auth/login', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async verifyOtp(data: VerifyOtpDto): Promise<AuthResponse> {
    const response = await this.request<AuthResponse>('/api/auth/verify', {
      method: 'POST',
      body: JSON.stringify(data),
    });
    
    // Store token and user data
    if (response.accessToken) {
      await this.saveToken(response.accessToken);
      await AsyncStorage.setItem('user', JSON.stringify(response.user));
    }
    
    return response;
  }

  async logout(): Promise<{ message: string }> {
    try {
      const response = await this.request<{ message: string }>('/api/auth/logout', {
        method: 'POST',
      });
      await this.removeToken();
      return response;
    } catch (error) {
      // Even if backend logout fails, clear local data
      await this.removeToken();
      throw error;
    }
  }

  // User methods
  async getCurrentUser(): Promise<User | null> {
    try {
      const userString = await AsyncStorage.getItem('user');
      return userString ? JSON.parse(userString) : null;
    } catch (error) {
      console.error('Failed to get current user:', error);
      return null;
    }
  }

  async getToken(): Promise<string | null> {
    if (!this.token) {
      await this.loadToken();
    }
    return this.token;
  }

  async isAuthenticated(): Promise<boolean> {
    const token = await this.getToken();
    const user = await this.getCurrentUser();
    return !!(token && user);
  }

  // Restaurant methods
  async getRestaurants(filters?: RestaurantFilters): Promise<Restaurant[]> {
    if (MOCK_MODE) {
      return this.getMockRestaurants(filters);
    }

    const params = new URLSearchParams();
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          params.append(key, value.toString());
        }
      });
    }

    const query = params.toString();
    const url = query ? `/api/restaurants?${query}` : '/api/restaurants';
    
    const response = await this.request<Restaurant[]>(url);
    return response;
  }

  async getRestaurant(id: string): Promise<Restaurant> {
    if (MOCK_MODE) {
      return this.getMockRestaurant(id);
    }

    const response = await this.request<Restaurant>(`/restaurants/${id}`);
    return response;
  }

  async getCategories(): Promise<Category[]> {
    if (MOCK_MODE) {
      return this.getMockCategories();
    }

    const response = await this.request<Category[]>('/categories');
    return response;
  }

  // Mock data methods
  private getMockRestaurants(filters?: RestaurantFilters): Restaurant[] {
    const mockRestaurants: Restaurant[] = [
      {
        id: '1',
        name: 'کباب خانه کابل',
        description: 'بهترین کباب‌های افغانی در شهر با طعم اصیل و سنتی',
        address: 'خیابان شیرپور، نزدیک پارک شیرپور',
        city: 'کابل',
        state: 'کابل',
        zip_code: '1001',
        phone: '0781234567',
        email: '<EMAIL>',
        website: 'https://kabulkebab.af',
        latitude: 34.5553,
        longitude: 69.2075,
        avg_rating: 4.5,
        price_range: 3,
        is_featured: true,
        status: 'active' as const,
        created_at: '2024-01-15T10:00:00Z',
        updated_at: '2024-01-15T10:00:00Z',
        owner: {
          id: 'owner1',
          name: 'احمد کریمی'
        },
        photos: [
          {
            id: 'photo1',
            url: 'https://images.unsplash.com/photo-1555396273-367ea4eb4db5?w=400',
            caption: 'کباب کوبیده'
          }
        ]
      },
      {
        id: '2',
        name: 'رستوران هرات',
        description: 'غذاهای محلی هرات با کیفیت عالی و قیمت مناسب',
        address: 'خیابان جادۀ میدان، کارته چهار',
        city: 'کابل',
        state: 'کابل',
        zip_code: '1002',
        phone: '0789876543',
        latitude: 34.5289,
        longitude: 69.1720,
        avg_rating: 4.2,
        price_range: 2,
        is_featured: false,
        status: 'active' as const,
        created_at: '2024-01-10T14:30:00Z',
        updated_at: '2024-01-20T09:15:00Z',
        owner: {
          id: 'owner2',
          name: 'فرید احمدی'
        },
        photos: [
          {
            id: 'photo2',
            url: 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=400',
            caption: 'قابلی پلاو'
          }
        ]
      },
      {
        id: '3',
        name: 'کافه بامیان',
        description: 'کافه مدرن با منظره زیبا و غذاهای بین‌المللی',
        address: 'خیابان دارالامان، نزدیک شهرنو',
        city: 'کابل',
        state: 'کابل',
        zip_code: '1003',
        phone: '0701122334',
        latitude: 34.4827,
        longitude: 69.1761,
        avg_rating: 4.7,
        price_range: 4,
        is_featured: true,
        status: 'active' as const,
        created_at: '2024-01-05T16:45:00Z',
        updated_at: '2024-01-25T11:20:00Z',
        owner: {
          id: 'owner3',
          name: 'مریم رضایی'
        },
        photos: [
          {
            id: 'photo3',
            url: 'https://images.unsplash.com/photo-1517248135467-4c7edcad34c4?w=400',
            caption: 'فضای داخلی کافه'
          }
        ]
      },
      {
        id: '4',
        name: 'پیتزا تندور',
        description: 'پیتزاهای تازه پخت با مواد اولیه درجه یک',
        address: 'خیابان کلولا پشته، ناحیه دهم',
        city: 'کابل',
        state: 'کابل',
        zip_code: '1004',
        phone: '0755667788',
        latitude: 34.5167, 
        longitude: 69.1833,
        avg_rating: 4.0,
        price_range: 2,
        is_featured: false,
        status: 'active' as const,
        created_at: '2024-01-12T12:00:00Z',
        updated_at: '2024-01-22T15:30:00Z',
        owner: {
          id: 'owner4',
          name: 'علی محمودی'
        },
        photos: [
          {
            id: 'photo4',
            url: 'https://images.unsplash.com/photo-1513104890138-7c749659a591?w=400',
            caption: 'پیتزا مارگاریتا'
          }
        ]
      },
      {
        id: '5',
        name: 'چای خانه سنتی',
        description: 'چای و قهوه سنتی افغانی در فضایی دنج و آرام',
        address: 'بازار پل خشتی، کهنه شهر',
        city: 'کابل',
        state: 'کابل',
        zip_code: '1005',
        phone: '0744556677',
        latitude: 34.5123,
        longitude: 69.1654,
        avg_rating: 3.8,
        price_range: 1,
        is_featured: false,
        status: 'active' as const,
        created_at: '2024-01-08T08:15:00Z',
        updated_at: '2024-01-18T10:45:00Z',
        owner: {
          id: 'owner5',
          name: 'عبدالرحیم خان'
        },
        photos: [
          {
            id: 'photo5',
            url: 'https://images.unsplash.com/photo-1559056199-641a0ac8b55e?w=400',
            caption: 'چای سبز افغانی'
          }
        ]
      }
    ];

    let filtered = mockRestaurants;

    // Apply filters
    if (filters?.search) {
      const searchTerm = filters.search.toLowerCase();
      filtered = filtered.filter(restaurant => 
        restaurant.name.toLowerCase().includes(searchTerm) ||
        restaurant.description?.toLowerCase().includes(searchTerm)
      );
    }

    if (filters?.city) {
      filtered = filtered.filter(restaurant => 
        restaurant.city.toLowerCase().includes(filters.city!.toLowerCase())
      );
    }

    if (filters?.min_rating) {
      filtered = filtered.filter(restaurant => 
        restaurant.avg_rating >= filters.min_rating!
      );
    }

    if (filters?.price_range) {
      filtered = filtered.filter(restaurant => 
        restaurant.price_range === filters.price_range
      );
    }

    if (filters?.featured !== undefined) {
      filtered = filtered.filter(restaurant => 
        restaurant.is_featured === filters.featured
      );
    }

    // Apply sorting
    if (filters?.sortBy) {
      filtered.sort((a, b) => {
        const aValue = (a as any)[filters.sortBy!];
        const bValue = (b as any)[filters.sortBy!];
        const order = filters.sortOrder === 'DESC' ? -1 : 1;
        
        if (aValue < bValue) return -1 * order;
        if (aValue > bValue) return 1 * order;
        return 0;
      });
    }

    // Apply pagination
    if (filters?.page && filters?.limit) {
      const start = (filters.page - 1) * filters.limit;
      const end = start + filters.limit;
      filtered = filtered.slice(start, end);
    }

    return filtered;
  }

  private getMockRestaurant(id: string): Restaurant {
    const restaurants = this.getMockRestaurants();
    const restaurant = restaurants.find(r => r.id === id);
    
    if (!restaurant) {
      throw new Error('Restaurant not found');
    }

    // Add menu items for detailed view
    restaurant.menuItems = [
      {
        id: 'menu1',
        name: 'کباب کوبیده',
        description: 'کباب کوبیده تازه با برنج و سالاد',
        price: 250,
        category: 'کباب‌ها',
        imageUrl: 'https://images.unsplash.com/photo-1529692236671-f1f6cf9683ba?w=400',
        isAvailable: true,
        isVegetarian: false,
        isVegan: false,
        isGlutenFree: false,
        spicyLevel: 1
      },
      {
        id: 'menu2', 
        name: 'قابلی پلاو',
        description: 'برنج با گوشت قره قل و آجیل',
        price: 350,
        category: 'غذاهای اصلی',
        imageUrl: 'https://images.unsplash.com/photo-1586190848861-99aa4a171e90?w=400',
        isAvailable: true,
        isVegetarian: false,
        isVegan: false,
        isGlutenFree: true,
        spicyLevel: 0
      }
    ];

    return restaurant;
  }

  private getMockCategories(): Category[] {
    return [
      { id: '1', name: 'کباب‌ها', description: 'انواع کباب‌های سنتی' },
      { id: '2', name: 'غذاهای اصلی', description: 'پلاو، قورمه و غذاهای اصلی' },
      { id: '3', name: 'کافه', description: 'چای، قهوه و نوشیدنی‌ها' },
      { id: '4', name: 'فست فود', description: 'پیتزا، برگر و غذاهای سریع' },
      { id: '5', name: 'سنتی', description: 'غذاهای محلی و سنتی افغانی' }
    ];
  }

  // Order Management Methods
  async createOrder(data: {
    restaurantId: string;
    items: { menuItemId: string; quantity: number; special_instructions?: string }[];
    delivery_address?: string;
    special_instructions?: string;
    customer_phone?: string;
    customer_name?: string;
  }): Promise<Order> {
    const backendData = {
      restaurantId: data.restaurantId,
      items: data.items.map(item => ({
        menuItemId: item.menuItemId,
        quantity: item.quantity,
        special_instructions: item.special_instructions
      })),
      delivery_address: data.delivery_address,
      special_instructions: data.special_instructions,
      customer_phone: data.customer_phone,
      customer_name: data.customer_name,
      userId: (await this.getCurrentUser())?.id || null
    };

    return this.request<Order>('/api/orders', {
      method: 'POST',
      body: JSON.stringify(backendData),
    });
  }

  async getUserOrders(page = 1, limit = 10): Promise<PaginatedResponse<Order>> {
    const backendOrders = await this.request<any[]>('/api/orders/user/me');
    
    // Transform backend response to match frontend Order interface
    const transformedOrders: Order[] = backendOrders.map(order => ({
      id: order.id,
      status: order.status,
      total_amount: Number(order.total),
      delivery_fee: Number(order.delivery_fee) || 0,
      tax_amount: Number(order.tax) || 0,
      delivery_address: order.delivery_address,
      special_instructions: order.special_instructions,
      estimated_delivery_time: order.estimated_delivery_time,
      created_at: order.created_at,
      updated_at: order.updated_at,
      customer: order.user,
      restaurant: order.restaurant,
      items: order.order_items?.map((item: any) => ({
        id: item.id,
        quantity: item.quantity,
        price: Number(item.unit_price),
        special_instructions: item.notes,
        menuItem: {
          id: item.menu_item?.id,
          name: item.menu_item?.name,
          description: item.menu_item?.description,
          price: Number(item.menu_item?.price),
          category: item.menu_item?.category,
          imageUrl: item.menu_item?.image_url,
          isAvailable: item.menu_item?.is_available !== false,
          isVegetarian: item.menu_item?.is_vegetarian || false,
          isVegan: item.menu_item?.is_vegan || false,
          dietaryInfo: item.menu_item?.dietary_info,
          created_at: item.menu_item?.created_at,
          updated_at: item.menu_item?.updated_at,
          restaurant: order.restaurant
        }
      })) || []
    }));
    
    // Apply pagination
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedData = transformedOrders.slice(startIndex, endIndex);
    
    return {
      data: paginatedData,
      total: transformedOrders.length,
      page: page,
      limit: limit,
      totalPages: Math.ceil(transformedOrders.length / limit)
    };
  }

  async getOrder(orderId: string): Promise<Order> {
    return this.request<Order>(`/api/orders/${orderId}`);
  }

  async cancelOrder(orderId: string): Promise<void> {
    return this.request<void>(`/api/orders/${orderId}/cancel`, {
      method: 'PATCH',
    });
  }

  async getOrderTracking(orderId: string): Promise<OrderTracking[]> {
    const response = await this.request<{ data: OrderTracking[] }>(`/api/orders/${orderId}/tracking`);
    return response.data || [];
  }

  // Profile methods
  async updateProfile(data: { name?: string; email?: string }): Promise<User> {
    if (MOCK_MODE) {
      // In mock mode, simulate profile update
      const currentUser = await this.getCurrentUser();
      if (!currentUser) {
        throw new Error('User not authenticated');
      }

      const updatedUser: User = {
        ...currentUser,
        ...data,
        updated_at: new Date().toISOString(),
      };

      // Save updated user to storage
      await AsyncStorage.setItem('user', JSON.stringify(updatedUser));
      return updatedUser;
    }

    const response = await this.request<User>('/api/users/profile', {
      method: 'PUT',
      body: JSON.stringify(data),
    });

    // Update stored user data
    await AsyncStorage.setItem('user', JSON.stringify(response));
    return response;
  }

  // Favorites methods
  async getFavorites(): Promise<Favorite[]> {
    if (MOCK_MODE) {
      // In mock mode, get favorites from local storage
      try {
        const favoritesString = await AsyncStorage.getItem('favorites');
        const favoriteIds = favoritesString ? JSON.parse(favoritesString) : [];
        
        // Get restaurant details for each favorite
        const restaurants = this.getMockRestaurants();
        const favorites: Favorite[] = favoriteIds.map((restaurantId: string, index: number) => {
          const restaurant = restaurants.find(r => r.id === restaurantId);
          return {
            id: `fav_${index}`,
            userId: 'mock_user',
            restaurantId,
            restaurant,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
          };
        }).filter((fav: Favorite) => fav.restaurant); // Only include valid restaurants
        
        return favorites;
      } catch (error) {
        console.error('Error getting favorites from storage:', error);
        return [];
      }
    }

    const response = await this.request<Favorite[]>('/api/favorites');
    return response;
  }

  async addToFavorites(restaurantId: string): Promise<Favorite> {
    if (MOCK_MODE) {
      // In mock mode, save to local storage
      try {
        const favoritesString = await AsyncStorage.getItem('favorites');
        const favoriteIds = favoritesString ? JSON.parse(favoritesString) : [];
        
        if (!favoriteIds.includes(restaurantId)) {
          favoriteIds.push(restaurantId);
          await AsyncStorage.setItem('favorites', JSON.stringify(favoriteIds));
        }
        
        const restaurant = this.getMockRestaurants().find(r => r.id === restaurantId);
        return {
          id: `fav_${Date.now()}`,
          userId: 'mock_user',
          restaurantId,
          restaurant,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        };
      } catch (error) {
        console.error('Error adding to favorites:', error);
        throw error;
      }
    }

    const response = await this.request<Favorite>('/api/favorites', {
      method: 'POST',
      body: JSON.stringify({ restaurantId }),
    });
    return response;
  }

  async removeFromFavorites(restaurantId: string): Promise<void> {
    if (MOCK_MODE) {
      // In mock mode, remove from local storage
      try {
        const favoritesString = await AsyncStorage.getItem('favorites');
        const favoriteIds = favoritesString ? JSON.parse(favoritesString) : [];
        
        const updatedFavorites = favoriteIds.filter((id: string) => id !== restaurantId);
        await AsyncStorage.setItem('favorites', JSON.stringify(updatedFavorites));
      } catch (error) {
        console.error('Error removing from favorites:', error);
        throw error;
      }
      return;
    }

    await this.request<void>(`/api/favorites/${restaurantId}`, {
      method: 'DELETE',
    });
  }

  async isFavorite(restaurantId: string): Promise<boolean> {
    if (MOCK_MODE) {
      // In mock mode, check local storage
      try {
        const favoritesString = await AsyncStorage.getItem('favorites');
        const favoriteIds = favoritesString ? JSON.parse(favoritesString) : [];
        return favoriteIds.includes(restaurantId);
      } catch (error) {
        console.error('Error checking favorite status:', error);
        return false;
      }
    }

    try {
      const favorites = await this.getFavorites();
      return favorites.some(fav => fav.restaurantId === restaurantId);
    } catch (error) {
      console.error('Error checking favorite status:', error);
      return false;
    }
  }

  // Payment Methods Management
  async getPaymentMethods(): Promise<PaymentMethod[]> {
    if (MOCK_MODE) {
      const stored = await AsyncStorage.getItem('payment_methods');
      if (stored) {
        return JSON.parse(stored);
      }
      
      // Default payment methods
      const defaultMethods: PaymentMethod[] = [
        {
          id: '1',
          type: 'cash',
          name: 'نقدی',
          display_name: 'نقدی',
          is_default: true,
          is_active: true,
          icon: '💵',
          description: 'پرداخت نقدی هنگام تحویل'
        },
        {
          id: '2',
          type: 'credit_card',
          name: 'کارت اعتباری',
          display_name: 'کارت اعتباری',
          is_default: false,
          is_active: true,
          icon: '💳',
          description: 'پرداخت با کارت اعتباری',
          card_number: '**** **** **** 1234',
          card_holder_name: 'John Doe',
          expiry_date: '12/25',
          card_type: 'visa'
        },
        {
          id: '3',
          type: 'mobile_wallet',
          name: 'کیف پول موبایل',
          display_name: 'کیف پول موبایل',
          is_default: false,
          is_active: true,
          icon: '📱',
          description: 'پرداخت با کیف پول موبایل',
          wallet_provider: 'Apple Pay'
        }
      ];
      
      await AsyncStorage.setItem('payment_methods', JSON.stringify(defaultMethods));
      return defaultMethods;
    }

    try {
      const response = await this.request<PaymentMethod[]>('/payments/methods');
      return response;
    } catch (error) {
      console.error('Error fetching payment methods:', error);
      throw error;
    }
  }

  async addPaymentMethod(method: Omit<PaymentMethod, 'id' | 'created_at' | 'updated_at'>): Promise<PaymentMethod> {
    if (MOCK_MODE) {
      const methods = await this.getPaymentMethods();
      const newMethod: PaymentMethod = {
        ...method,
        id: Date.now().toString(),
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };
      
      const updatedMethods = [...methods, newMethod];
      await AsyncStorage.setItem('payment_methods', JSON.stringify(updatedMethods));
      return newMethod;
    }

    try {
      const response = await this.request<PaymentMethod>('/payments/methods', {
        method: 'POST',
        body: JSON.stringify(method),
      });
      return response;
    } catch (error) {
      console.error('Error adding payment method:', error);
      throw error;
    }
  }

  async updatePaymentMethod(id: string, updates: Partial<PaymentMethod>): Promise<PaymentMethod> {
    if (MOCK_MODE) {
      const methods = await this.getPaymentMethods();
      const methodIndex = methods.findIndex(m => m.id === id);
      
      if (methodIndex === -1) {
        throw new Error('Payment method not found');
      }
      
      const updatedMethod = {
        ...methods[methodIndex],
        ...updates,
        updated_at: new Date().toISOString()
      };
      
      methods[methodIndex] = updatedMethod;
      await AsyncStorage.setItem('payment_methods', JSON.stringify(methods));
      return updatedMethod;
    }

    try {
      const response = await this.request<PaymentMethod>(`/payments/methods/${id}`, {
        method: 'PATCH',
        body: JSON.stringify(updates),
      });
      return response;
    } catch (error) {
      console.error('Error updating payment method:', error);
      throw error;
    }
  }

  async deletePaymentMethod(id: string): Promise<void> {
    if (MOCK_MODE) {
      const methods = await this.getPaymentMethods();
      const filteredMethods = methods.filter(m => m.id !== id);
      await AsyncStorage.setItem('payment_methods', JSON.stringify(filteredMethods));
      return;
    }

    try {
      await this.request<void>(`/payments/methods/${id}`, {
        method: 'DELETE',
      });
    } catch (error) {
      console.error('Error deleting payment method:', error);
      throw error;
    }
  }

  async setDefaultPaymentMethod(id: string): Promise<void> {
    if (MOCK_MODE) {
      const methods = await this.getPaymentMethods();
      const updatedMethods = methods.map(method => ({
        ...method,
        is_default: method.id === id,
        updated_at: new Date().toISOString()
      }));
      await AsyncStorage.setItem('payment_methods', JSON.stringify(updatedMethods));
      return;
    }

    try {
      await this.request<void>(`/payments/methods/${id}/default`, {
        method: 'PATCH',
      });
    } catch (error) {
      console.error('Error setting default payment method:', error);
      throw error;
    }
  }

  // Payment Processing
  async createPayment(paymentData: CreatePaymentRequest): Promise<Payment> {
    if (MOCK_MODE) {
      const payment: Payment = {
        id: Date.now().toString(),
        user_id: 'mock-user-id',
        ...paymentData,
        payment_status: paymentData.payment_method === 'cash' ? 'pending' : 'completed',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };
      
      // Store payment
      const payments = await this.getPaymentHistory();
      const updatedPayments = [payment, ...payments];
      await AsyncStorage.setItem('payment_history', JSON.stringify(updatedPayments));
      
      return payment;
    }

    try {
      const response = await this.request<Payment>('/payments', {
        method: 'POST',
        body: JSON.stringify(paymentData),
      });
      return response;
    } catch (error) {
      console.error('Error creating payment:', error);
      throw error;
    }
  }

  async getPayment(id: string): Promise<Payment> {
    if (MOCK_MODE) {
      const payments = await this.getPaymentHistory();
      const payment = payments.find(p => p.id === id);
      if (!payment) {
        throw new Error('Payment not found');
      }
      return payment;
    }

    try {
      const response = await this.request<Payment>(`/payments/${id}`);
      return response;
    } catch (error) {
      console.error('Error fetching payment:', error);
      throw error;
    }
  }

  async getPaymentHistory(page: number = 1, limit: number = 10): Promise<Payment[]> {
    if (MOCK_MODE) {
      const stored = await AsyncStorage.getItem('payment_history');
      if (stored) {
        const payments = JSON.parse(stored);
        const start = (page - 1) * limit;
        const end = start + limit;
        return payments.slice(start, end);
      }
      return [];
    }

    try {
      const response = await this.request<Payment[]>('/payments', {
        params: { page, limit }
      });
      return response;
    } catch (error) {
      console.error('Error fetching payment history:', error);
      throw error;
    }
  }

  async getOrderPayments(orderId: string): Promise<Payment[]> {
    if (MOCK_MODE) {
      const payments = await this.getPaymentHistory();
      return payments.filter(p => p.order_id === orderId);
    }

    try {
      const response = await this.request<Payment[]>(`/payments/order/${orderId}`);
      return response;
    } catch (error) {
      console.error('Error fetching order payments:', error);
      throw error;
    }
  }

  async updatePaymentStatus(id: string, status: Payment['payment_status'], transactionId?: string): Promise<Payment> {
    if (MOCK_MODE) {
      const payments = await this.getPaymentHistory();
      const paymentIndex = payments.findIndex(p => p.id === id);
      
      if (paymentIndex === -1) {
        throw new Error('Payment not found');
      }
      
      const updatedPayment = {
        ...payments[paymentIndex],
        payment_status: status,
        transaction_id: transactionId || payments[paymentIndex].transaction_id,
        updated_at: new Date().toISOString()
      };
      
      payments[paymentIndex] = updatedPayment;
      await AsyncStorage.setItem('payment_history', JSON.stringify(payments));
      return updatedPayment;
    }

    try {
      const response = await this.request<Payment>(`/payments/${id}/status`, {
        method: 'PATCH',
        body: JSON.stringify({ status, transactionId }),
      });
      return response;
    } catch (error) {
      console.error('Error updating payment status:', error);
      throw error;
    }
  }

  async processRefund(id: string): Promise<Payment> {
    if (MOCK_MODE) {
      return this.updatePaymentStatus(id, 'refunded');
    }

    try {
      const response = await this.request<Payment>(`/payments/${id}/refund`, {
        method: 'PATCH',
      });
      return response;
    } catch (error) {
      console.error('Error processing refund:', error);
      throw error;
    }
  }

  async getPaymentStats(): Promise<PaymentStats> {
    if (MOCK_MODE) {
      const payments = await this.getPaymentHistory();
      const totalPayments = payments.length;
      const successfulPayments = payments.filter(p => p.payment_status === 'completed').length;
      const failedPayments = payments.filter(p => p.payment_status === 'failed').length;
      const pendingPayments = payments.filter(p => p.payment_status === 'pending').length;
      const refundedPayments = payments.filter(p => p.payment_status === 'refunded').length;
      const totalAmount = payments.reduce((sum, p) => sum + p.amount, 0);
      
      return {
        total_payments: totalPayments,
        successful_payments: successfulPayments,
        failed_payments: failedPayments,
        pending_payments: pendingPayments,
        refunded_payments: refundedPayments,
        total_amount: totalAmount,
        success_rate: totalPayments > 0 ? (successfulPayments / totalPayments) * 100 : 0,
        average_amount: totalPayments > 0 ? totalAmount / totalPayments : 0
      };
    }

    try {
      const response = await this.request<PaymentStats>('/payments/stats');
      return response;
    } catch (error) {
      console.error('Error fetching payment stats:', error);
      throw error;
    }
  }

  async getPaymentGateways(): Promise<PaymentGateway[]> {
    if (MOCK_MODE) {
      return [
        {
          id: '1',
          name: 'Stripe',
          provider: 'stripe',
          is_active: true,
          configuration: {}
        },
        {
          id: '2',
          name: 'PayPal',
          provider: 'paypal',
          is_active: false,
          configuration: {}
        }
      ];
    }

    try {
      const response = await this.request<PaymentGateway[]>('/payments/gateways');
      return response;
    } catch (error) {
      console.error('Error fetching payment gateways:', error);
      throw error;
    }
  }

  // Advanced Search & Filtering Methods
  async getSearchSuggestions(query: string, limit: number = 10): Promise<SearchSuggestion[]> {
    if (MOCK_MODE) {
      const mockSuggestions: SearchSuggestion[] = [
        {
          id: '1',
          type: 'restaurant',
          text: 'رستوران کابل استار',
          subtitle: 'غذاهای افغانی • 4.8 ⭐',
          icon: '🏪',
          data: { restaurantId: '1', cuisine: 'afghan' }
        },
        {
          id: '2',
          type: 'dish',
          text: 'کباب کوبیده',
          subtitle: '15 رستوران موجود',
          icon: '🍖',
          data: { dishType: 'kebab' }
        },
        {
          id: '3',
          type: 'cuisine',
          text: 'غذاهای افغانی',
          subtitle: '45 رستوران',
          icon: '🍽️',
          data: { cuisineId: 'afghan' }
        },
        {
          id: '4',
          type: 'location',
          text: 'شهر نو، کابل',
          subtitle: '23 رستوران',
          icon: '📍',
          data: { locationId: 'kabul_shahar_naw' }
        },
        {
          id: '5',
          type: 'category',
          text: 'فست فود',
          subtitle: '28 رستوران',
          icon: '🍔',
          data: { categoryId: 'fast_food' }
        }
      ];

      return mockSuggestions.filter(suggestion =>
        suggestion.text.toLowerCase().includes(query.toLowerCase()) ||
        suggestion.subtitle?.toLowerCase().includes(query.toLowerCase())
      ).slice(0, limit);
    }

    try {
      const params = new URLSearchParams({ q: query, limit: limit.toString() });
      const response = await this.request<SearchSuggestion[]>(`/search/suggestions?${params.toString()}`);
      return response;
    } catch (error) {
      console.error('Error fetching search suggestions:', error);
      throw error;
    }
  }

  async performAdvancedSearch(searchRequest: AdvancedSearchRequest): Promise<AdvancedSearchResponse> {
    if (MOCK_MODE) {
      // Simulate search delay
      await new Promise(resolve => setTimeout(resolve, 500));

      const { filters, pagination = { page: 1, limit: 20 } } = searchRequest;
      
      // Mock restaurants data
      const allRestaurants = await this.getRestaurants({ limit: 100 });
      
      // Apply filters
      let filteredRestaurants = allRestaurants.filter(restaurant => {
                 // Query filter
         if (filters.query) {
           const queryMatch = restaurant.name.toLowerCase().includes(filters.query.toLowerCase()) ||
                             (restaurant.description && restaurant.description.toLowerCase().includes(filters.query.toLowerCase()));
           if (!queryMatch) return false;
         }

        // Location filter
        if (filters.location) {
          const locationMatch = restaurant.city.toLowerCase().includes(filters.location.toLowerCase()) ||
                               restaurant.address.toLowerCase().includes(filters.location.toLowerCase());
          if (!locationMatch) return false;
        }

        // Cuisine filter
        if (filters.cuisine.length > 0) {
          // Mock cuisine matching
          const restaurantCuisine = restaurant.name.includes('کابل') ? 'afghan' : 
                                  restaurant.name.includes('پیتزا') ? 'italian' : 'afghan';
          if (!filters.cuisine.includes(restaurantCuisine)) return false;
        }

        // Price range filter
        if (filters.priceRange[0] > 1 || filters.priceRange[1] < 4) {
          if (restaurant.price_range < filters.priceRange[0] || 
              restaurant.price_range > filters.priceRange[1]) return false;
        }

        // Rating filter
        if (filters.rating > 0) {
          if (restaurant.avg_rating < filters.rating) return false;
        }

        // Open status filter
        if (filters.isOpen) {
          if (!restaurant.is_open) return false;
        }

        // Delivery filter
        if (filters.hasDelivery) {
          if (!restaurant.has_delivery) return false;
        }

        return true;
      });

      // Apply sorting
      switch (filters.sortBy) {
        case 'rating':
          filteredRestaurants.sort((a, b) => b.avg_rating - a.avg_rating);
          break;
        case 'price_low':
          filteredRestaurants.sort((a, b) => a.price_range - b.price_range);
          break;
        case 'price_high':
          filteredRestaurants.sort((a, b) => b.price_range - a.price_range);
          break;
        case 'delivery_time':
          filteredRestaurants.sort((a, b) => (a.delivery_time || 30) - (b.delivery_time || 30));
          break;
        default: // relevance or distance
          // Keep current order or implement relevance scoring
          break;
      }

      // Pagination
      const startIndex = (pagination.page - 1) * pagination.limit;
      const paginatedRestaurants = filteredRestaurants.slice(startIndex, startIndex + pagination.limit);

      // Mock menu items search
      const menuItems: MenuItem[] = [];

      // Mock facets
      const facets = {
        cuisines: [
          { id: 'afghan', name: 'افغانی', count: 45, icon: '🍽️' },
          { id: 'italian', name: 'ایتالیایی', count: 28, icon: '🍝' },
          { id: 'chinese', name: 'چینی', count: 24, icon: '🥢' },
          { id: 'indian', name: 'هندی', count: 20, icon: '🍛' },
          { id: 'fast_food', name: 'فست‌فود', count: 18, icon: '🍔' }
        ],
        priceRanges: [
          { range: [1, 1] as [number, number], count: 12 },
          { range: [2, 2] as [number, number], count: 25 },
          { range: [3, 3] as [number, number], count: 18 },
          { range: [4, 4] as [number, number], count: 8 }
        ],
        ratings: [
          { rating: 4.5, count: 15 },
          { rating: 4.0, count: 28 },
          { rating: 3.5, count: 22 },
          { rating: 3.0, count: 12 }
        ],
        locations: [
          { id: '1', name: 'شهر نو', city: 'کابل', country: 'افغانستان' },
          { id: '2', name: 'کارته چهار', city: 'کابل', country: 'افغانستان' },
          { id: '3', name: 'وزیر اکبر خان', city: 'کابل', country: 'افغانستان' }
        ]
      };

      const suggestions = await this.getSearchSuggestions(filters.query, 5);

      return {
        restaurants: paginatedRestaurants,
        menuItems,
        totalResults: filteredRestaurants.length,
        suggestions,
        facets,
        searchTime: 250
      };
    }

    try {
      const response = await this.request<AdvancedSearchResponse>('/search/advanced', {
        method: 'POST',
        body: JSON.stringify(searchRequest),
      });
      return response;
    } catch (error) {
      console.error('Error performing advanced search:', error);
      throw error;
    }
  }

  async getSearchHistory(limit: number = 10): Promise<SearchHistory[]> {
    if (MOCK_MODE) {
      const stored = await AsyncStorage.getItem('search_history');
      if (stored) {
        const history: SearchHistory[] = JSON.parse(stored);
        return history.slice(0, limit);
      }
      return [];
    }

    try {
      const params = new URLSearchParams({ limit: limit.toString() });
      const response = await this.request<SearchHistory[]>(`/search/history?${params.toString()}`);
      return response;
    } catch (error) {
      console.error('Error fetching search history:', error);
      throw error;
    }
  }

  async saveSearchHistory(query: string, filters: Partial<SearchFilters>, resultCount: number): Promise<void> {
    if (MOCK_MODE) {
      const history = await this.getSearchHistory(50);
      const newEntry: SearchHistory = {
        id: Date.now().toString(),
        query,
        filters,
        timestamp: new Date().toISOString(),
        resultCount
      };

      // Remove duplicate queries
      const filteredHistory = history.filter(item => item.query !== query);
      const updatedHistory = [newEntry, ...filteredHistory].slice(0, 50);

      await AsyncStorage.setItem('search_history', JSON.stringify(updatedHistory));
      return;
    }

    try {
      await this.request<void>('/search/history', {
        method: 'POST',
        body: JSON.stringify({ query, filters, resultCount }),
      });
    } catch (error) {
      console.error('Error saving search history:', error);
      // Don't throw error for history saving
    }
  }

  async clearSearchHistory(): Promise<void> {
    if (MOCK_MODE) {
      await AsyncStorage.removeItem('search_history');
      return;
    }

    try {
      await this.request<void>('/search/history', {
        method: 'DELETE',
      });
    } catch (error) {
      console.error('Error clearing search history:', error);
      throw error;
    }
  }

  async getPopularSearches(limit: number = 10): Promise<PopularSearch[]> {
    if (MOCK_MODE) {
      return [
        { id: '1', query: 'کباب', count: 1250, category: 'dish' },
        { id: '2', query: 'پیتزا', count: 980, category: 'dish' },
        { id: '3', query: 'غذاهای افغانی', count: 850, category: 'cuisine' },
        { id: '4', query: 'فست فود', count: 720, category: 'cuisine' },
        { id: '5', query: 'شهر نو', count: 650, category: 'location' },
        { id: '6', query: 'برگر', count: 580, category: 'dish' },
        { id: '7', query: 'دسر', count: 420, category: 'category' },
        { id: '8', query: 'کافه', count: 380, category: 'category' }
      ].slice(0, limit);
    }

    try {
      const params = new URLSearchParams({ limit: limit.toString() });
      const response = await this.request<PopularSearch[]>(`/search/popular?${params.toString()}`);
      return response;
    } catch (error) {
      console.error('Error fetching popular searches:', error);
      throw error;
    }
  }

  async getCuisineOptions(): Promise<CuisineOption[]> {
    if (MOCK_MODE) {
      return [
        { id: 'afghan', name: 'افغانی', count: 45, icon: '🍽️' },
        { id: 'middle_eastern', name: 'خاورمیانه‌ای', count: 32, icon: '🥙' },
        { id: 'italian', name: 'ایتالیایی', count: 28, icon: '🍝' },
        { id: 'chinese', name: 'چینی', count: 24, icon: '🥢' },
        { id: 'indian', name: 'هندی', count: 20, icon: '🍛' },
        { id: 'fast_food', name: 'فست‌فود', count: 18, icon: '🍔' },
        { id: 'cafe', name: 'کافه', count: 15, icon: '☕' },
        { id: 'dessert', name: 'دسر', count: 12, icon: '🍰' }
      ];
    }

    try {
      const response = await this.request<CuisineOption[]>('/cuisines');
      return response;
    } catch (error) {
      console.error('Error fetching cuisine options:', error);
      throw error;
    }
  }

  async getLocationSuggestions(query: string, limit: number = 10): Promise<LocationSuggestion[]> {
    if (MOCK_MODE) {
      const mockLocations: LocationSuggestion[] = [
        { id: '1', name: 'شهر نو', city: 'کابل', country: 'افغانستان' },
        { id: '2', name: 'کارته چهار', city: 'کابل', country: 'افغانستان' },
        { id: '3', name: 'وزیر اکبر خان', city: 'کابل', country: 'افغانستان' },
        { id: '4', name: 'خیابان جمهوریت', city: 'هرات', country: 'افغانستان' },
        { id: '5', name: 'شهر نو', city: 'مزار شریف', country: 'افغانستان' },
        { id: '6', name: 'مرکز شهر', city: 'قندهار', country: 'افغانستان' }
      ];

      return mockLocations.filter(location =>
        location.name.includes(query) || location.city.includes(query)
      ).slice(0, limit);
    }

    try {
      const params = new URLSearchParams({ q: query, limit: limit.toString() });
      const response = await this.request<LocationSuggestion[]>(`/locations/suggestions?${params.toString()}`);
      return response;
    } catch (error) {
      console.error('Error fetching location suggestions:', error);
      throw error;
    }
  }

  async getSearchAnalytics(): Promise<SearchAnalytics> {
    if (MOCK_MODE) {
      const popularQueries = await this.getPopularSearches();
      const recentSearches = await this.getSearchHistory();

      return {
        totalSearches: 15420,
        popularQueries,
        recentSearches,
        topCuisines: [
          { cuisine: 'افغانی', count: 3250 },
          { cuisine: 'فست‌فود', count: 2180 },
          { cuisine: 'ایتالیایی', count: 1850 },
          { cuisine: 'چینی', count: 1420 }
        ],
        topLocations: [
          { location: 'شهر نو، کابل', count: 2850 },
          { location: 'کارته چهار، کابل', count: 1950 },
          { location: 'وزیر اکبر خان، کابل', count: 1420 }
        ]
      };
    }

    try {
      const response = await this.request<SearchAnalytics>('/search/analytics');
      return response;
    } catch (error) {
      console.error('Error fetching search analytics:', error);
      throw error;
    }
  }

  // Real-time Features & Notifications Methods
  async getNotificationSettings(): Promise<NotificationSettings> {
    if (MOCK_MODE) {
      const stored = await AsyncStorage.getItem('notification_settings');
      if (stored) {
        return JSON.parse(stored);
      }
      
      const defaultSettings: NotificationSettings = {
        orderUpdates: true,
        promotions: true,
        newRestaurants: false,
        reminders: true,
        marketing: false,
        pushEnabled: true,
        emailEnabled: true,
        smsEnabled: false
      };
      
      await AsyncStorage.setItem('notification_settings', JSON.stringify(defaultSettings));
      return defaultSettings;
    }

    try {
      const response = await this.request<NotificationSettings>('/notifications/settings');
      return response;
    } catch (error) {
      console.error('Error fetching notification settings:', error);
      throw error;
    }
  }

  async updateNotificationSettings(settings: Partial<NotificationSettings>): Promise<NotificationSettings> {
    if (MOCK_MODE) {
      const currentSettings = await this.getNotificationSettings();
      const updatedSettings = { ...currentSettings, ...settings };
      await AsyncStorage.setItem('notification_settings', JSON.stringify(updatedSettings));
      return updatedSettings;
    }

    try {
      const response = await this.request<NotificationSettings>('/notifications/settings', {
        method: 'PUT',
        body: JSON.stringify(settings),
      });
      return response;
    } catch (error) {
      console.error('Error updating notification settings:', error);
      throw error;
    }
  }

  async getNotifications(page: number = 1, limit: number = 20): Promise<PushNotification[]> {
    if (MOCK_MODE) {
      const mockNotifications: PushNotification[] = [
        {
          id: '1',
          type: 'order_update',
          title: 'سفارش شما آماده است!',
          body: 'سفارش شما از رستوران کابل استار آماده تحویل است.',
          data: { order_id: '1', status: 'ready' },
          read: false,
          created_at: new Date(Date.now() - 5 * 60 * 1000).toISOString(),
          priority: 'high'
        },
        {
          id: '2',
          type: 'promotion',
          title: 'تخفیف ویژه!',
          body: '20% تخفیف برای سفارش بعدی شما در رستوران‌های منتخب',
          data: { promotion_id: 'promo_123' },
          read: false,
          created_at: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
          image_url: 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=400',
          priority: 'normal'
        },
        {
          id: '3',
          type: 'order_update',
          title: 'سفارش تحویل داده شد',
          body: 'سفارش شما با موفقیت تحویل داده شد. از انتخاب ما متشکریم!',
          data: { order_id: '2', status: 'delivered' },
          read: true,
          created_at: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
          priority: 'normal'
        },
        {
          id: '4',
          type: 'reminder',
          title: 'یادآوری سفارش',
          body: 'هنوز سفارش نداده‌اید؟ رستوران‌های جدید را کشف کنید!',
          data: {},
          read: true,
          created_at: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
          priority: 'low'
        }
      ];

      return mockNotifications.slice((page - 1) * limit, page * limit);
    }

    try {
      const params = new URLSearchParams({ 
        page: page.toString(), 
        limit: limit.toString() 
      });
      const response = await this.request<PushNotification[]>(`/notifications?${params.toString()}`);
      return response;
    } catch (error) {
      console.error('Error fetching notifications:', error);
      throw error;
    }
  }

  async markNotificationAsRead(notificationId: string): Promise<void> {
    if (MOCK_MODE) {
      // In real implementation, this would update the notification status
      return;
    }

    try {
      await this.request<void>(`/notifications/${notificationId}/read`, {
        method: 'PUT',
      });
    } catch (error) {
      console.error('Error marking notification as read:', error);
      throw error;
    }
  }

  async markAllNotificationsAsRead(): Promise<void> {
    if (MOCK_MODE) {
      return;
    }

    try {
      await this.request<void>('/notifications/read-all', {
        method: 'PUT',
      });
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
      throw error;
    }
  }

  async deleteNotification(notificationId: string): Promise<void> {
    if (MOCK_MODE) {
      return;
    }

    try {
      await this.request<void>(`/notifications/${notificationId}`, {
        method: 'DELETE',
      });
    } catch (error) {
      console.error('Error deleting notification:', error);
      throw error;
    }
  }

  async getLiveOrderTracking(orderId: string): Promise<LiveOrderTracking> {
    if (MOCK_MODE) {
      const mockTracking: LiveOrderTracking = {
        order_id: orderId,
        current_status: 'preparing',
        updates: [
          {
            id: '1',
            order_id: orderId,
            status: 'confirmed',
            message: 'سفارش شما تأیید شد',
            timestamp: new Date(Date.now() - 20 * 60 * 1000).toISOString()
          },
          {
            id: '2',
            order_id: orderId,
            status: 'preparing',
            message: 'رستوران شروع به آماده‌سازی سفارش کرده است',
            timestamp: new Date(Date.now() - 10 * 60 * 1000).toISOString(),
            estimated_time: new Date(Date.now() + 15 * 60 * 1000).toISOString()
          }
        ],
        estimated_delivery: new Date(Date.now() + 30 * 60 * 1000).toISOString(),
        preparation_time: 25,
        delivery_time: 15,
        restaurant_location: {
          latitude: 34.5553,
          longitude: 69.2075,
          address: 'شهر نو، کابل'
        },
        delivery_location: {
          latitude: 34.5563,
          longitude: 69.2085,
          address: 'کارته چهار، کابل'
        }
      };

      return mockTracking;
    }

    try {
      const response = await this.request<LiveOrderTracking>(`/orders/${orderId}/tracking`);
      return response;
    } catch (error) {
      console.error('Error fetching live order tracking:', error);
      throw error;
    }
  }

  async getOrderChat(orderId: string): Promise<LiveChat> {
    if (MOCK_MODE) {
      const mockChat: LiveChat = {
        id: `chat_${orderId}`,
        order_id: orderId,
        participants: {
          customer: { id: 'user_1', name: 'احمد محمدی' },
          restaurant: { id: 'rest_1', name: 'رستوران کابل استار' }
        },
        messages: [
          {
            id: '1',
            order_id: orderId,
            sender_type: 'restaurant',
            sender_id: 'rest_1',
            sender_name: 'رستوران کابل استار',
            message: 'سلام! سفارش شما در حال آماده‌سازی است.',
            timestamp: new Date(Date.now() - 10 * 60 * 1000).toISOString(),
            read: true,
            message_type: 'text'
          },
          {
            id: '2',
            order_id: orderId,
            sender_type: 'customer',
            sender_id: 'user_1',
            sender_name: 'احمد محمدی',
            message: 'ممنون! تقریباً چه زمانی آماده می‌شود؟',
            timestamp: new Date(Date.now() - 8 * 60 * 1000).toISOString(),
            read: true,
            message_type: 'text'
          },
          {
            id: '3',
            order_id: orderId,
            sender_type: 'restaurant',
            sender_id: 'rest_1',
            sender_name: 'رستوران کابل استار',
            message: 'حدود 15 دقیقه دیگر آماده خواهد بود.',
            timestamp: new Date(Date.now() - 5 * 60 * 1000).toISOString(),
            read: false,
            message_type: 'text'
          }
        ],
        status: 'active',
        created_at: new Date(Date.now() - 20 * 60 * 1000).toISOString(),
        updated_at: new Date(Date.now() - 5 * 60 * 1000).toISOString()
      };

      return mockChat;
    }

    try {
      const response = await this.request<LiveChat>(`/orders/${orderId}/chat`);
      return response;
    } catch (error) {
      console.error('Error fetching order chat:', error);
      throw error;
    }
  }

  async sendChatMessage(orderId: string, message: string, messageType: ChatMessage['message_type'] = 'text'): Promise<ChatMessage> {
    if (MOCK_MODE) {
      const newMessage: ChatMessage = {
        id: Date.now().toString(),
        order_id: orderId,
        sender_type: 'customer',
        sender_id: 'user_1',
        sender_name: 'شما',
        message,
        timestamp: new Date().toISOString(),
        read: false,
        message_type: messageType
      };

      return newMessage;
    }

    try {
      const response = await this.request<ChatMessage>(`/orders/${orderId}/chat/messages`, {
        method: 'POST',
        body: JSON.stringify({ message, messageType }),
      });
      return response;
    } catch (error) {
      console.error('Error sending chat message:', error);
      throw error;
    }
  }

  async getSystemStatus(): Promise<SystemStatus> {
    if (MOCK_MODE) {
      return {
        server_status: 'online',
        features: {
          orders: true,
          payments: true,
          notifications: true,
          chat: true
        }
      };
    }

    try {
      const response = await this.request<SystemStatus>('/system/status');
      return response;
    } catch (error) {
      console.error('Error fetching system status:', error);
      // Return offline status if can't reach server
      return {
        server_status: 'offline',
        features: {
          orders: false,
          payments: false,
          notifications: false,
          chat: false
        }
      };
    }
  }

  async registerForPushNotifications(token: string, platform: 'ios' | 'android'): Promise<void> {
    if (MOCK_MODE) {
      await AsyncStorage.setItem('push_token', JSON.stringify({ token, platform }));
      return;
    }

    try {
      await this.request<void>('/notifications/register', {
        method: 'POST',
        body: JSON.stringify({ token, platform }),
      });
    } catch (error) {
      console.error('Error registering for push notifications:', error);
      throw error;
    }
  }

  async unregisterFromPushNotifications(): Promise<void> {
    if (MOCK_MODE) {
      await AsyncStorage.removeItem('push_token');
      return;
    }

    try {
      await this.request<void>('/notifications/unregister', {
        method: 'POST',
      });
    } catch (error) {
      console.error('Error unregistering from push notifications:', error);
      throw error;
    }
  }
}

// Create singleton instance
const apiService = new ApiService();
export default apiService; 