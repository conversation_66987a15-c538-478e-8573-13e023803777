import FontAwesome from '@expo/vector-icons/FontAwesome';
import { DarkTheme, DefaultTheme, ThemeProvider } from '@react-navigation/native';
import { useFonts } from 'expo-font';
import * as SplashScreen from 'expo-splash-screen';
import { useEffect } from 'react';
import { useColorScheme } from 'react-native';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import 'react-native-reanimated';

// Import i18n
import './src/i18n';

// Import our navigation structure
import { AppNavigator } from './src/navigation/AppNavigator';

// Import providers
import { AuthProvider } from './src/contexts/AuthContext';
import { CartProvider } from './src/contexts/CartContext';
import { FavoritesProvider } from './src/contexts/FavoritesContext';
import { PaymentProvider } from './src/contexts/PaymentContext';
import { SearchProvider } from './src/contexts/SearchContext';
import { NotificationsProvider } from './src/contexts/NotificationsContext';

// Prevent the splash screen from auto-hiding before asset loading is complete.
SplashScreen.preventAutoHideAsync();

export default function App() {
  const [loaded, error] = useFonts({
    // YekanBakh fonts - using the actual file names
    'YekanBakh-Thin': require('./assets/fonts/yekanBakh/YekanBakhFaNum-Thin.ttf'),
    'YekanBakh-Light': require('./assets/fonts/yekanBakh/YekanBakhFaNum-Light.ttf'),
    'YekanBakh-Regular': require('./assets/fonts/yekanBakh/YekanBakhFaNum-Regular.ttf'),
    'YekanBakh-SemiBold': require('./assets/fonts/yekanBakh/YekanBakhFaNum-SemiBold.ttf'),
    'YekanBakh-Bold': require('./assets/fonts/yekanBakh/YekanBakhFaNum-Bold.ttf'),
    'YekanBakh-ExtraBold': require('./assets/fonts/yekanBakh/YekanBakhFaNum-ExtraBold.ttf'),
    'YekanBakh-Black': require('./assets/fonts/yekanBakh/YekanBakhFaNum-Black.ttf'),
    'YekanBakh-ExtraBlack': require('./assets/fonts/yekanBakh/YekanBakhFaNum-ExtraBlack.ttf'),
    ...FontAwesome.font,
  });

  const colorScheme = useColorScheme();

  useEffect(() => {
    if (error) throw error;
  }, [error]);

  useEffect(() => {
    if (loaded) {
      SplashScreen.hideAsync();
    }
  }, [loaded]);

  if (!loaded) {
    return null;
  }

  return (
    <SafeAreaProvider>
      <AuthProvider>
        <NotificationsProvider>
          <FavoritesProvider>
            <CartProvider>
              <PaymentProvider>
                <SearchProvider>
                  <ThemeProvider value={colorScheme === 'dark' ? DarkTheme : DefaultTheme}>
                    <AppNavigator />
                  </ThemeProvider>
                </SearchProvider>
              </PaymentProvider>
            </CartProvider>
          </FavoritesProvider>
        </NotificationsProvider>
      </AuthProvider>
    </SafeAreaProvider>
  );
} 