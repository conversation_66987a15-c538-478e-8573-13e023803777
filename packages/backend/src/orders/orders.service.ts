import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Order, OrderStatus } from './entities/order.entity';
import { OrderItem } from './entities/order-item.entity';
import { CreateOrderDto } from './dto/create-order.dto';
import { MenuItem } from '../restaurants/entities/menu-item.entity';
import { Restaurant } from '../restaurants/entities/restaurant.entity';
import { User } from '../users/entities/user.entity';
import { OrdersGateway } from './orders.gateway';

@Injectable()
export class OrdersService {
  constructor(
    @InjectRepository(Order)
    private orderRepository: Repository<Order>,
    @InjectRepository(OrderItem)
    private orderItemRepository: Repository<OrderItem>,
    @InjectRepository(MenuItem)
    private menuItemRepository: Repository<MenuItem>,
    @InjectRepository(Restaurant)
    private restaurantRepository: Repository<Restaurant>,
    @InjectRepository(User)
    private userRepository: Repository<User>,
    private ordersGateway: OrdersGateway,
  ) {}

  async create(createOrderDto: any): Promise<Order> {
    const { restaurant_id, user_id, order_items, credit_amount_used, customer_phone, customer_name, ...orderData } = createOrderDto;

    // Verify restaurant exists
    const restaurant = await this.restaurantRepository.findOne({
      where: { id: restaurant_id },
    });
    if (!restaurant) {
      throw new NotFoundException('Restaurant not found');
    }

    // For guest orders, user_id can be null
    let user = null;
    if (user_id) {
      user = await this.userRepository.findOne({
        where: { id: user_id },
      });
      if (!user) {
        throw new NotFoundException('User not found');
      }
    } else {
      // For guest orders, we need customer_phone at minimum
      if (!customer_phone) {
        throw new BadRequestException('Customer phone is required for guest orders');
      }
    }

    // Calculate order totals
    let subtotal = 0;
    const orderItemsData = [];

    for (const item of order_items) {
      const menuItem = await this.menuItemRepository.findOne({
        where: { id: item.menu_item_id },
      });
      if (!menuItem) {
        throw new NotFoundException(`Menu item ${item.menu_item_id} not found`);
      }

      const itemTotal = Number(menuItem.price) * item.quantity;
      subtotal += itemTotal;

      orderItemsData.push({
        menu_item: menuItem,
        quantity: item.quantity,
        unit_price: Number(menuItem.price),
        notes: item.notes,
      });
    }

    const tax = subtotal * 0.05; // 5% tax
    const delivery_fee = 50; // Fixed delivery fee
    const total = subtotal + tax + delivery_fee;
    const creditUsed = credit_amount_used || 0;
    const remainingAmount = total - creditUsed;

    if (remainingAmount < 0) {
      throw new BadRequestException('Credit amount exceeds order total');
    }

    // Generate order number
    const orderNumber = `ORD-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

    // Create order with guest support
    const orderCreateData: any = {
      ...orderData,
      restaurant,
      order_number: orderNumber,
      subtotal,
      tax,
      delivery_fee,
      total,
      credit_amount_used: creditUsed,
      remaining_amount: remainingAmount,
      status: 'pending',
    };

    // Add user if authenticated, otherwise store guest info
    if (user) {
      orderCreateData.user = user;
    } else {
      orderCreateData.customer_phone = customer_phone;
      orderCreateData.customer_name = customer_name || 'Guest Customer';
    }

    const order = this.orderRepository.create(orderCreateData);
    const insertResult = await this.orderRepository.insert(order);
    const savedOrderId = insertResult.identifiers[0].id;

    // Create basic order object for immediate notification
    const basicOrderForNotification = {
      id: savedOrderId,
      order_number: orderNumber,
      status: 'pending',
      total: total,
      subtotal: subtotal,
      customer_name: customer_name || user?.name || 'Guest Customer',
      customer_phone: customer_phone || user?.phone || '',
      delivery_address: orderCreateData.delivery_address || '',
      special_instructions: orderCreateData.special_instructions || '',
      created_at: new Date(),
      restaurant: restaurant,
      user: user,
    };

    // 🚀 EMIT WEBSOCKET EVENT IMMEDIATELY for instant notifications
    console.log('🚀 Emitting immediate WebSocket notification for order:', savedOrderId);
    this.ordersGateway.emitNewOrder(restaurant_id, basicOrderForNotification);

    // Create order items (this can happen after notification)
    for (const itemData of orderItemsData) {
      const orderItem = this.orderItemRepository.create({
        ...itemData,
        order: { id: savedOrderId },
      });
      await this.orderItemRepository.save(orderItem);
    }

    // Return complete order with all relations
    const newOrder = await this.findOne(savedOrderId);
    return newOrder;
  }

  async findAll(): Promise<Order[]> {
    return this.orderRepository.find({
      relations: ['restaurant', 'user', 'order_items', 'order_items.menu_item'],
      order: { created_at: 'DESC' },
    });
  }

  async findOne(id: string): Promise<Order> {
    const order = await this.orderRepository.findOne({
      where: { id },
      relations: ['restaurant', 'user', 'order_items', 'order_items.menu_item'],
    });

    if (!order) {
      throw new NotFoundException('Order not found');
    }

    return order;
  }

  async findByUser(userId: string): Promise<Order[]> {
    return this.orderRepository.find({
      where: { user: { id: userId } },
      relations: ['restaurant', 'order_items', 'order_items.menu_item'],
      order: { created_at: 'DESC' },
    });
  }

  async findByRestaurant(restaurantId: string): Promise<Order[]> {
    return this.orderRepository.find({
      where: { restaurant: { id: restaurantId } },
      relations: ['user', 'order_items', 'order_items.menu_item'],
      order: { created_at: 'DESC' },
    });
  }

  async findByRestaurantOwner(ownerId: string, filters?: { status?: string; restaurantId?: string }): Promise<Order[]> {
    console.log('Finding orders for restaurant owner:', ownerId, 'with filters:', filters);
    
    const queryBuilder = this.orderRepository
      .createQueryBuilder('order')
      .leftJoinAndSelect('order.restaurant', 'restaurant')
      .leftJoinAndSelect('order.user', 'user')
      .leftJoinAndSelect('order.orderItems', 'orderItems')
      .leftJoinAndSelect('orderItems.menuItem', 'menuItem')
      .where('restaurant.owner.id = :ownerId', { ownerId });
    
    // Apply additional filters if provided
    if (filters?.status) {
      queryBuilder.andWhere('order.status = :status', { status: filters.status });
    }
    
    if (filters?.restaurantId) {
      queryBuilder.andWhere('restaurant.id = :restaurantId', { restaurantId: filters.restaurantId });
    }
    
    queryBuilder.orderBy('order.created_at', 'DESC');
    
    const orders = await queryBuilder.getMany();
    console.log(`Found ${orders.length} orders for restaurant owner ${ownerId}`);
    
    return orders;
  }

  async updateStatus(id: string, status: OrderStatus, estimatedDeliveryTime?: string): Promise<Order> {
    const order = await this.findOne(id);
    order.status = status;
    
    // Update estimated delivery time if provided
    if (estimatedDeliveryTime) {
      order.estimated_delivery_time = estimatedDeliveryTime;
    }
    
    const updatedOrder = await this.orderRepository.save(order);
    
    // Emit WebSocket event for restaurant status update
    this.ordersGateway.emitOrderStatusUpdate(
      order.restaurant.id, 
      id, 
      status,
      updatedOrder
    );

    // 📱 SMART CUSTOMER NOTIFICATION: Only for active orders
    const activeStatuses = ['pending', 'confirmed', 'preparing', 'ready'];
    const finalStatuses = ['delivered', 'cancelled'];
    
    if (order.user?.id) {
      if (activeStatuses.includes(status) || finalStatuses.includes(status)) {
        console.log(`📱 Notifying customer ${order.user.id} about order ${id} status: ${status}${estimatedDeliveryTime ? ` with delivery time: ${estimatedDeliveryTime}` : ''}`);
        this.ordersGateway.emitCustomerOrderUpdate(order.user.id, updatedOrder);
      }
    }
    
    return updatedOrder;
  }

  async cancelOrder(id: string): Promise<Order> {
    const order = await this.findOne(id);
    
    // Only allow cancellation for pending orders (before restaurant confirmation)
    if (order.status !== OrderStatus.PENDING) {
      if (order.status === OrderStatus.DELIVERED) {
        throw new BadRequestException('Cannot cancel delivered order');
      } else if ([OrderStatus.CONFIRMED, OrderStatus.PREPARING, OrderStatus.READY].includes(order.status)) {
        throw new BadRequestException('Cannot cancel order after restaurant confirmation. Please contact the restaurant directly.');
      } else if (order.status === OrderStatus.CANCELED) {
        throw new BadRequestException('Order is already cancelled');
      }
    }

    order.status = OrderStatus.CANCELED;
    const cancelledOrder = await this.orderRepository.save(order);
    
    // Emit WebSocket event for restaurant notification
    this.ordersGateway.emitOrderStatusUpdate(
      order.restaurant.id, 
      id, 
      OrderStatus.CANCELED,
      cancelledOrder
    );

    // Notify customer about cancellation
    if (order.user?.id) {
      console.log(`📱 Notifying customer ${order.user.id} about order ${id} cancellation`);
      this.ordersGateway.emitCustomerOrderUpdate(order.user.id, cancelledOrder);
    }
    
    return cancelledOrder;
  }

  async getOrderStats() {
    const totalOrders = await this.orderRepository.count();
    const pendingOrders = await this.orderRepository.count({
      where: { status: OrderStatus.PENDING },
    });
    const completedOrders = await this.orderRepository.count({
      where: { status: OrderStatus.DELIVERED },
    });
    const canceledOrders = await this.orderRepository.count({
      where: { status: OrderStatus.CANCELED },
    });

    const totalRevenue = await this.orderRepository
      .createQueryBuilder('order')
      .select('SUM(order.total)', 'total')
      .where('order.status = :status', { status: OrderStatus.DELIVERED })
      .getRawOne();

    return {
      totalOrders,
      pendingOrders,
      completedOrders,
      canceledOrders,
      totalRevenue: totalRevenue?.total || 0,
    };
  }

  async remove(id: string): Promise<void> {
    const order = await this.findOne(id);
    await this.orderRepository.remove(order);
  }
} 