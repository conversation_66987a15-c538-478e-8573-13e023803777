import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  SetMetadata,
  BadRequestException,
  Headers,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiQuery } from '@nestjs/swagger';
import { OrdersService } from './orders.service';
import { CreateOrderDto } from './dto/create-order.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { OrderStatus } from './entities/order.entity';
import { GetUser } from '../auth/decorators/get-user.decorator';
import { User } from '../users/entities/user.entity';

// Custom decorator to skip authentication
const Public = () => SetMetadata('isPublic', true);

@ApiTags('orders')
@Controller('orders')
export class OrdersController {
  constructor(private readonly ordersService: OrdersService) {}

  @Post()
  @Public() // Allow public access for order creation
  create(@Body() createOrderDto: any) {
    const transformedDto = {
      restaurant_id: createOrderDto.restaurantId,
      user_id: createOrderDto.userId || null,
      order_type: 'delivery',
      order_items: createOrderDto.items?.map((item: any) => ({
        menu_item_id: item.menuItemId,
        quantity: item.quantity,
        notes: item.special_instructions
      })) || [],
      special_instructions: createOrderDto.special_instructions,
      delivery_address: createOrderDto.delivery_address,
      customer_phone: createOrderDto.customer_phone,
      customer_name: createOrderDto.customer_name,
      credit_amount_used: createOrderDto.credit_amount_used || 0
    };

    return this.ordersService.create(transformedDto);
  }

  @Get()
  // @UseGuards(JwtAuthGuard) // Temporarily disabled for testing
  @ApiOperation({ summary: 'Get all orders with optional role-based filtering' })
  @ApiResponse({ status: 200, description: 'Return all orders or filtered orders for restaurant owners' })
  @ApiQuery({ name: 'restaurant_owner_id', required: false, description: 'Filter orders by restaurant owner ID' })
  @ApiQuery({ name: 'status', required: false, description: 'Filter by order status' })
  @ApiQuery({ name: 'restaurant_id', required: false, description: 'Filter by restaurant ID' })
  async findAll(
    @Query('restaurant_owner_id') restaurantOwnerId?: string,
    @Query('status') status?: string,
    @Query('restaurant_id') restaurantId?: string,
    @Headers('x-user-role') userRole?: string,
    @Headers('x-user-id') userId?: string,
    @GetUser() user?: User
  ) {
    console.log('Get orders - Headers - Role:', userRole, 'ID:', userId);
    console.log('Query parameters:', { restaurantOwnerId, status, restaurantId });
    
    // If user is restaurant owner, restrict to their restaurant orders only
    if (userRole === 'restaurant_owner' && userId) {
      console.log('Filtering orders for restaurant owner:', userId);
      return this.ordersService.findByRestaurantOwner(userId, { status, restaurantId });
    }
    
    // If restaurant_owner_id is specified in query, filter by that owner
    if (restaurantOwnerId) {
      console.log('Filtering orders by restaurant_owner_id:', restaurantOwnerId);
      return this.ordersService.findByRestaurantOwner(restaurantOwnerId, { status, restaurantId });
    }
    
    // Default: return all orders (for admins)
    return this.ordersService.findAll();
  }

  @Get('stats')
  // @UseGuards(JwtAuthGuard) // Temporarily disabled for testing
  getOrderStats() {
    return this.ordersService.getOrderStats();
  }

  @Get('user/me')
  @UseGuards(JwtAuthGuard)
  findMyOrders(@GetUser() user: User) {
    return this.ordersService.findByUser(user.id);
  }

  @Get('user/:userId')
  // @UseGuards(JwtAuthGuard) // Temporarily disabled for testing
  findByUser(@Param('userId') userId: string) {
    return this.ordersService.findByUser(userId);
  }

  @Get('restaurant/:restaurantId')
  // @UseGuards(JwtAuthGuard) // Temporarily disabled for testing
  findByRestaurant(@Param('restaurantId') restaurantId: string) {
    return this.ordersService.findByRestaurant(restaurantId);
  }

  @Get(':id')
  // @UseGuards(JwtAuthGuard) // Temporarily disabled for testing
  findOne(@Param('id') id: string) {
    return this.ordersService.findOne(id);
  }

  @Patch(':id/status')
  // @UseGuards(JwtAuthGuard) // Temporarily disabled for testing
  updateStatus(
    @Param('id') id: string,
    @Body() body: { status: OrderStatus; estimated_delivery_time?: string },
  ) {
    return this.ordersService.updateStatus(id, body.status, body.estimated_delivery_time);
  }

  @Patch(':id/cancel')
  // @UseGuards(JwtAuthGuard) // Temporarily disabled for testing
  cancelOrder(@Param('id') id: string) {
    return this.ordersService.cancelOrder(id);
  }

  @Delete(':id')
  // @UseGuards(JwtAuthGuard) // Temporarily disabled for testing
  remove(@Param('id') id: string) {
    return this.ordersService.remove(id);
  }
} 