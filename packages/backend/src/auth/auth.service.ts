import { Injectable, UnauthorizedException, NotFoundException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { RegisterDto } from './dto/register.dto';
import { LoginDto } from './dto/login.dto';
import { VerifyOtpDto, AuthVerifyOtpDto } from './dto/verify-otp.dto';
import { User } from '../users/entities/user.entity';
import { UsersService } from '../users/users.service';
import { OtpVerification, OtpPurpose } from './entities/otp-verification.entity';
import { UserSession } from './entities/user-session.entity';

@Injectable()
export class AuthService {
  constructor(
    private readonly usersService: UsersService,
    private readonly jwtService: JwtService,
    @InjectRepository(OtpVerification)
    private readonly otpRepository: Repository<OtpVerification>,
    @InjectRepository(UserSession)
    private readonly userSessionRepository: Repository<UserSession>,
  ) {}

  async register(registerDto: RegisterDto): Promise<{ message: string }> {
    // Check if user exists with phone number
    const existingUser = await this.usersService.findByPhoneNumber(registerDto.phone_number);
    
    if (existingUser) {
      // If user exists, send OTP for login
      await this.sendOtp(registerDto.phone_number, OtpPurpose.LOGIN);
      return { message: 'User already exists. OTP sent for login.' };
    }

    // Create new user
    await this.usersService.create(registerDto);
    
    // Send OTP for verification
    await this.sendOtp(registerDto.phone_number, OtpPurpose.REGISTRATION);
    
    return { message: 'User registered successfully. Please verify with OTP.' };
  }

  async login(loginDto: LoginDto): Promise<{ message: string }> {
    const user = await this.usersService.findByPhoneNumber(loginDto.phone_number);
    
    if (!user) {
      throw new NotFoundException('User not found with this phone number');
    }
    
    // Send OTP for login
    await this.sendOtp(loginDto.phone_number, OtpPurpose.LOGIN);
    
    return { message: 'OTP sent to your phone number' };
  }

  async verifyOtp(verifyOtpDto: VerifyOtpDto): Promise<{ accessToken: string; user: User }> {
    const { managerPhone, otp: otpCode } = verifyOtpDto;
    
    // Find valid OTP that hasn't expired yet
    const otpRecord = await this.otpRepository.findOne({
      where: {
        phone_number: managerPhone,
        otp_code: otpCode,
        is_used: false,
      },
    });
    
    if (!otpRecord) {
      throw new UnauthorizedException('Invalid OTP');
    }
    
    // Check if OTP has expired
    if (otpRecord.expires_at < new Date()) {
      throw new UnauthorizedException('OTP has expired');
    }
    
    // Mark OTP as used
    otpRecord.is_used = true;
    await this.otpRepository.save(otpRecord);
    
    // Get or create user
    let user = await this.usersService.findByPhoneNumber(managerPhone);
    
    if (!user && otpRecord.purpose === OtpPurpose.REGISTRATION) {
      // This should not happen normally, but just in case
      throw new NotFoundException('User registration incomplete. Please register first.');
    }
    
    // Create user session
    const token = this.generateToken(user);
    
    // Create or update session
    await this.createUserSession(user, token);
    
    return {
      accessToken: token,
      user,
    };
  }

  // Separate method for auth system verification
  async verifyAuthOtp(verifyOtpDto: AuthVerifyOtpDto): Promise<{ accessToken: string; user: User }> {
    const { phone_number, otp_code } = verifyOtpDto;
    
    // Find valid OTP that hasn't expired yet
    const otpRecord = await this.otpRepository.findOne({
      where: {
        phone_number,
        otp_code,
        is_used: false,
      },
    });
    
    if (!otpRecord) {
      throw new UnauthorizedException('Invalid OTP');
    }

    // Check if OTP has expired
    if (otpRecord.expires_at < new Date()) {
      throw new UnauthorizedException('OTP has expired');
    }

    // Mark OTP as used
    otpRecord.is_used = true;
    await this.otpRepository.save(otpRecord);

    // Get or create user
    let user = await this.usersService.findByPhoneNumber(phone_number);

    if (!user) {
      throw new NotFoundException('User not found. Please register first.');
    }

    // Create user session
    const token = this.generateToken(user);
    await this.createUserSession(user, token);

    return {
      accessToken: token,
      user,
    };
  }

  async logout(userId: string, token: string): Promise<{ message: string }> {
    // Find and remove the session
    await this.userSessionRepository.delete({
      user: { id: userId },
      token,
    });
    
    return { message: 'Logged out successfully' };
  }

  async createAdminUser(adminData: { phone_number: string; full_name: string; email: string }) {
    // Check if admin already exists
    const existingUser = await this.usersService.findByPhoneNumber(adminData.phone_number);
    if (existingUser) {
      return { message: 'Admin user already exists', user: existingUser };
    }

    // Create admin user
    const adminUser = await this.usersService.create({
      phone_number: adminData.phone_number,
      name: adminData.full_name,
      email: adminData.email,
      role: 'admin' as any
    });

    return { message: 'Admin user created successfully', user: adminUser };
  }

  private async sendOtp(phone_number: string, purpose: OtpPurpose): Promise<void> {
    // Find user if exists
    const user = await this.usersService.findByPhoneNumber(phone_number);
    
    // Invalidate any existing unused OTPs for this phone number
    await this.otpRepository.update(
      { phone_number, is_used: false },
      { is_used: true }
    );
    
    // Use fixed OTP for testing (change to random generation for production)
    const otp_code = "123456";
    
    // Set expiration (15 minutes)
    const expires_at = new Date(new Date().getTime() + 15 * 60 * 1000);
    
    // Save OTP
    const otpVerification = this.otpRepository.create({
      user,
      phone_number,
      otp_code,
      purpose,
      expires_at,
    });
    
    await this.otpRepository.save(otpVerification);
    
    // In a real app, you would send the OTP via SMS here
    console.log(`Fixed OTP for ${phone_number}: ${otp_code}`);
  }

  private generateToken(user: User): string {
    const payload = { 
      sub: user.id, 
      phone: user.phone_number,
      email: user.email,
      role: user.role 
    };
    
    return this.jwtService.sign(payload);
  }

  private async createUserSession(user: User, token: string): Promise<UserSession> {
    const expires_at = new Date(new Date().getTime() + 30 * 24 * 60 * 60 * 1000); // 30 days
    
    const session = this.userSessionRepository.create({
      user,
      token,
      expires_at,
      last_activity: new Date(),
    });
    
    return this.userSessionRepository.save(session);
  }
} 