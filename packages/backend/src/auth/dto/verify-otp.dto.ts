import { IsString, IsNotEmpty } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class SendOtpDto {
  @ApiProperty({ description: 'Manager name' })
  @IsString()
  @IsNotEmpty()
  managerName: string;

  @ApiProperty({ description: 'Manager phone number' })
  @IsString()
  @IsNotEmpty()
  managerPhone: string;
}

export class VerifyOtpDto {
  @ApiProperty({ description: 'Manager phone number' })
  @IsString()
  @IsNotEmpty()
  managerPhone: string;

  @ApiProperty({ description: 'OTP code' })
  @IsString()
  @IsNotEmpty()
  otp: string;
}

// Separate DTO for auth system
export class AuthVerifyOtpDto {
  @ApiProperty({ description: 'Phone number' })
  @IsString()
  @IsNotEmpty()
  phone_number: string;

  @ApiProperty({ description: 'OTP code' })
  @IsString()
  @IsNotEmpty()
  otp_code: string;
} 