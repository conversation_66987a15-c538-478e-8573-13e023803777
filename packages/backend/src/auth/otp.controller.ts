import { Controller, Post, Body, HttpCode, HttpStatus } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { SendOtpDto, VerifyOtpDto } from './dto/verify-otp.dto';

@ApiTags('OTP Verification')
@Controller('otp')
export class OtpController {
  
  @Post('send')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Send OTP for restaurant registration verification' })
  @ApiResponse({ status: 200, description: 'OTP sent successfully' })
  @ApiResponse({ status: 400, description: 'Invalid phone number or request' })
  async sendOtp(@Body() sendOtpDto: SendOtpDto) {
    // For development, we'll just simulate sending OTP
    // In production, this would integrate with SMS service
    
    // Validate Afghanistan phone number
    const cleanPhone = sendOtpDto.managerPhone.replace(/[\s\-\(\)]/g, '');
    const afghanPhoneRegex = /^(\+93|0093|93)?(0?[7][0-9])\d{7}$/;
    
    if (!afghanPhoneRegex.test(cleanPhone)) {
      throw new Error('Invalid Afghanistan phone number');
    }

    console.log(`[DEV] OTP for ${sendOtpDto.managerName} (${sendOtpDto.managerPhone}): 123456`);
    
    return {
      success: true,
      message: 'OTP sent successfully',
      data: {
        phone: sendOtpDto.managerPhone,
        // In production, don't include the OTP in response
        devOtp: '123456' // Only for development
      }
    };
  }

  @Post('verify')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Verify OTP for restaurant registration' })
  @ApiResponse({ status: 200, description: 'OTP verified successfully' })
  @ApiResponse({ status: 400, description: 'Invalid OTP or phone number' })
  async verifyOtp(@Body() verifyOtpDto: VerifyOtpDto) {
    // For development, check if OTP is 123456
    if (verifyOtpDto.otp !== '123456') {
      throw new Error('Invalid OTP code');
    }

    console.log(`[DEV] OTP verified for ${verifyOtpDto.managerPhone}`);
    
    return {
      success: true,
      message: 'OTP verified successfully',
      data: {
        phone: verifyOtpDto.managerPhone,
        verified: true,
        token: `verification_token_${Date.now()}` // Simple token for session
      }
    };
  }
} 