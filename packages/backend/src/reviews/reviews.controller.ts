import { Controller, Get, Post, Body, Patch, Param, Delete, UseGuards, Query, Headers } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiBearerAuth, ApiParam, ApiResponse, ApiQuery } from '@nestjs/swagger';
import { ReviewsService } from './reviews.service';
import { CreateReviewDto } from './dto/create-review.dto';
import { UpdateReviewDto } from './dto/update-review.dto';
import { FilterReviewsDto } from './dto/filter-reviews.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { Review } from './entities/review.entity';
import { GetUser } from '../auth/decorators/get-user.decorator';
import { User } from '../users/entities/user.entity';
import { ApiReviewResponses } from './decorators/review-responses.decorator';
import { PaginatedReviewsResponse, ReviewResponse } from './dto/review-response.dto';
import { RestaurantReviewStats } from './interfaces/review-stats.interface';
import { MarkReviewHelpfulDto } from './dto/mark-review-helpful.dto';
import { ReviewStatsDto } from './dto/review-stats.dto';

@ApiTags('reviews')
@Controller('reviews')
export class ReviewsController {
  constructor(private readonly reviewsService: ReviewsService) {}

  @Post()
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Create a new review' })
  @ApiReviewResponses({
    operation: 'review creation',
    okStatus: 201,
    okDescription: 'The review has been created successfully.',
    includeBadRequest: true,
    includeForbidden: true,
    includeNotFound: true,
    includeUnauthorized: true,
  })
  async create(@Body() createReviewDto: CreateReviewDto, @GetUser() user: User): Promise<Review> {
    return this.reviewsService.create(createReviewDto, user);
  }

  @Get()
  @ApiOperation({ summary: 'Find all reviews with optional filtering' })
  @ApiResponse({
    status: 200,
    description: 'Returns a paginated list of reviews based on filters',
    type: PaginatedReviewsResponse,
  })
  @ApiQuery({ name: 'restaurant_owner_id', required: false, description: 'Filter reviews by restaurant owner ID' })
  @ApiReviewResponses({
    operation: 'reviews retrieval',
    okDescription: 'Return filtered reviews.',
  })
  async findAll(
    @Query() filterDto: FilterReviewsDto,
    @Query('restaurant_owner_id') restaurantOwnerId?: string,
    @Headers('x-user-role') userRole?: string,
    @Headers('x-user-id') userId?: string,
    @GetUser() user?: User
  ): Promise<PaginatedReviewsResponse> {
    console.log('Get reviews - Headers - Role:', userRole, 'ID:', userId);
    console.log('Query parameters:', { restaurantOwnerId, ...filterDto });
    
    // If user is restaurant owner, restrict to their restaurant reviews only
    if (userRole === 'restaurant_owner' && userId) {
      console.log('Filtering reviews for restaurant owner:', userId);
      return this.reviewsService.findByRestaurantOwner(userId, filterDto);
    }
    
    // If restaurant_owner_id is specified in query, filter by that owner
    if (restaurantOwnerId) {
      console.log('Filtering reviews by restaurant_owner_id:', restaurantOwnerId);
      return this.reviewsService.findByRestaurantOwner(restaurantOwnerId, filterDto);
    }
    
    // Default: return all reviews (for admins)
    return this.reviewsService.findAll(filterDto);
  }

  @Get('my-reviews')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get current user\'s reviews' })
  @ApiReviewResponses({
    operation: 'user reviews retrieval',
    okDescription: 'Return user reviews.',
    includeUnauthorized: true,
  })
  async getUserReviews(
    @GetUser() user: User, 
    @Query() filterDto: FilterReviewsDto
  ): Promise<PaginatedReviewsResponse> {
    return this.reviewsService.getUserReviews(user.id, filterDto);
  }

  @Get('restaurant/:restaurantId')
  @ApiOperation({ summary: 'Get reviews for a specific restaurant' })
  @ApiParam({ name: 'restaurantId', description: 'Restaurant ID' })
  @ApiReviewResponses({
    operation: 'restaurant reviews retrieval',
    okDescription: 'Return reviews for the restaurant.',
    includeNotFound: true,
  })
  async getRestaurantReviews(
    @Param('restaurantId') restaurantId: string,
    @Query() filterDto: FilterReviewsDto
  ): Promise<PaginatedReviewsResponse> {
    return this.reviewsService.findAll({
      ...filterDto,
      restaurantId,
    });
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a review by ID' })
  @ApiParam({ name: 'id', description: 'Review ID' })
  @ApiReviewResponses({
    operation: 'review retrieval',
    okDescription: 'Return the review.',
    includeNotFound: true,
  })
  async findOne(@Param('id') id: string): Promise<Review> {
    return this.reviewsService.findOne(id);
  }

  @Patch(':id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Update a review' })
  @ApiParam({ name: 'id', description: 'Review ID' })
  @ApiReviewResponses({
    operation: 'review update',
    okDescription: 'The review has been updated successfully.',
    includeBadRequest: true,
    includeForbidden: true,
    includeNotFound: true,
    includeUnauthorized: true,
  })
  async update(
    @Param('id') id: string,
    @Body() updateReviewDto: UpdateReviewDto,
    @GetUser() user: User,
  ): Promise<Review> {
    return this.reviewsService.update(id, updateReviewDto, user);
  }

  @Delete(':id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Delete a review' })
  @ApiParam({ name: 'id', description: 'Review ID' })
  @ApiReviewResponses({
    operation: 'review deletion',
    okDescription: 'The review has been deleted successfully.',
    includeForbidden: true,
    includeNotFound: true,
    includeUnauthorized: true,
  })
  async remove(@Param('id') id: string, @GetUser() user: User): Promise<void> {
    return this.reviewsService.remove(id, user);
  }

  @Get('stats/restaurant/:restaurantId')
  @ApiOperation({ summary: 'Get review statistics for a restaurant' })
  @ApiParam({ name: 'restaurantId', description: 'Restaurant ID' })
  @ApiReviewResponses({
    operation: 'review statistics retrieval',
    okDescription: 'Return the review statistics for the restaurant.',
    includeNotFound: true,
  })
  async getRestaurantReviewStats(
    @Param('restaurantId') restaurantId: string
  ): Promise<ReviewStatsDto> {
    return this.reviewsService.getRestaurantReviewStats(restaurantId);
  }

  @Post('mark-helpful')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Mark a review as helpful or unhelpful' })
  @ApiReviewResponses({
    operation: 'mark review helpful',
    okDescription: 'The review has been marked successfully.',
    includeBadRequest: true,
    includeNotFound: true,
    includeUnauthorized: true,
  })
  async markReviewHelpful(
    @Body() markReviewHelpfulDto: MarkReviewHelpfulDto,
    @GetUser() user: User,
  ): Promise<Review> {
    return this.reviewsService.markReviewHelpful(
      markReviewHelpfulDto.reviewId,
      user.id,
      markReviewHelpfulDto.isHelpful,
    );
  }
} 