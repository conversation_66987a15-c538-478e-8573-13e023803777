import { Injectable, NotFoundException, ForbiddenException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource } from 'typeorm';
import { InjectDataSource } from '@nestjs/typeorm';
import { Review } from './entities/review.entity';
import { CreateReviewDto } from './dto/create-review.dto';
import { UpdateReviewDto } from './dto/update-review.dto';
import { User } from '../users/entities/user.entity';
import { Restaurant } from '../restaurants/entities/restaurant.entity';
import { FilterReviewsDto } from './dto/filter-reviews.dto';
import { PaginatedReviewsResponse } from './dto/review-response.dto';
import { RestaurantReviewStats } from './interfaces/review-stats.interface';
import { ReviewHelpful } from './entities/review-helpful.entity';
import { ReviewStatsDto } from './dto/review-stats.dto';

@Injectable()
export class ReviewsService {
  constructor(
    @InjectRepository(Review)
    private reviewRepository: Repository<Review>,
    @InjectRepository(Restaurant)
    private restaurantRepository: Repository<Restaurant>,
    @InjectRepository(ReviewHelpful)
    private reviewHelpfulRepository: Repository<ReviewHelpful>,
    @InjectDataSource()
    private dataSource: DataSource,
  ) {}

  async create(createReviewDto: CreateReviewDto, user: User): Promise<Review> {
    // Check if restaurant exists
    const restaurant = await this.restaurantRepository.findOne({
      where: { id: createReviewDto.restaurantId },
    });

    if (!restaurant) {
      throw new NotFoundException('Restaurant not found');
    }

    // Check if user already has a review for this restaurant
    const existingReview = await this.reviewRepository.findOne({
      where: {
        restaurant: { id: createReviewDto.restaurantId },
        user: { id: user.id },
      },
    });

    if (existingReview) {
      throw new ForbiddenException('You have already reviewed this restaurant');
    }

    const review = this.reviewRepository.create({
      rating: createReviewDto.rating,
      comment: createReviewDto.comment,
      images: createReviewDto.images,
      restaurantId: createReviewDto.restaurantId,
      userId: user.id,
    });

    return this.reviewRepository.save(review);
  }

  async findAll(filterDto: FilterReviewsDto): Promise<PaginatedReviewsResponse> {
    const { 
      page, 
      limit, 
      sortBy, 
      sortOrder, 
      restaurantId, 
      userId, 
      minRating, 
      maxRating, 
      search,
      createdAfter,
      createdBefore,
      hasImages,
      minHelpfulCount,
      sortByHelpfulness
    } = filterDto;
    
    const queryBuilder = this.reviewRepository.createQueryBuilder('review')
      .leftJoinAndSelect('review.user', 'user')
      .leftJoinAndSelect('review.restaurant', 'restaurant');
    
    // Apply filters
    if (restaurantId) {
      queryBuilder.andWhere('review.restaurantId = :restaurantId', { restaurantId });
    }
    
    if (userId) {
      queryBuilder.andWhere('review.userId = :userId', { userId });
    }
    
    if (minRating) {
      queryBuilder.andWhere('review.rating >= :minRating', { minRating });
    }
    
    if (maxRating) {
      queryBuilder.andWhere('review.rating <= :maxRating', { maxRating });
    }
    
    if (search) {
      queryBuilder.andWhere('(review.comment ILIKE :search OR user.name ILIKE :search)', 
        { search: `%${search}%` });
    }
    
    // New filters
    if (createdAfter) {
      queryBuilder.andWhere('review.createdAt >= :createdAfter', { createdAfter });
    }
    
    if (createdBefore) {
      queryBuilder.andWhere('review.createdAt <= :createdBefore', { createdBefore });
    }
    
    if (hasImages) {
      queryBuilder.andWhere('review.images IS NOT NULL AND array_length(review.images, 1) > 0');
    }
    
    if (minHelpfulCount) {
      queryBuilder.andWhere('review.helpfulCount >= :minHelpfulCount', { minHelpfulCount });
    }
    
    // Apply sorting
    if (sortByHelpfulness) {
      // Sort by helpfulness ratio (helpful / (helpful + unhelpful))
      queryBuilder.addSelect(
        `CASE WHEN (review.helpfulCount + review.unhelpfulCount) = 0 THEN 0 
         ELSE CAST(review.helpfulCount AS float) / NULLIF((review.helpfulCount + review.unhelpfulCount), 0) 
         END`, 
        'helpfulness_ratio'
      );
      queryBuilder.orderBy('helpfulness_ratio', 'DESC');
      // Secondary sort by helpful count for equal ratios
      queryBuilder.addOrderBy('review.helpfulCount', 'DESC');
    } else {
      queryBuilder.orderBy(`review.${sortBy}`, sortOrder);
    }
    
    // Apply pagination
    const skip = (page - 1) * limit;
    queryBuilder.skip(skip).take(limit);
    
    const [data, total] = await queryBuilder.getManyAndCount();
    
    return new PaginatedReviewsResponse(data, total, page, limit);
  }

  async findOne(id: string): Promise<Review> {
    const review = await this.reviewRepository.findOne({
      where: { id },
      relations: ['user', 'restaurant'],
    });

    if (!review) {
      throw new NotFoundException('Review not found');
    }

    return review;
  }

  async update(id: string, updateReviewDto: UpdateReviewDto, user: User): Promise<Review> {
    const review = await this.reviewRepository.findOne({
      where: { id },
      relations: ['user'],
    });

    if (!review) {
      throw new NotFoundException('Review not found');
    }

    // Check if the user owns the review
    if (review.userId !== user.id) {
      throw new ForbiddenException('You can only update your own reviews');
    }

    Object.assign(review, updateReviewDto);

    return this.reviewRepository.save(review);
  }

  async remove(id: string, user: User): Promise<void> {
    const review = await this.reviewRepository.findOne({
      where: { id },
      relations: ['user'],
    });

    if (!review) {
      throw new NotFoundException('Review not found');
    }

    // Check if the user owns the review
    if (review.userId !== user.id) {
      throw new ForbiddenException('You can only delete your own reviews');
    }

    await this.reviewRepository.remove(review);
  }

  async getUserReviews(userId: string, filterDto: FilterReviewsDto = {}): Promise<PaginatedReviewsResponse> {
    return this.findAll({
      ...filterDto,
      userId,
    });
  }

  async getRestaurantReviewStats(restaurantId: string): Promise<ReviewStatsDto> {
    // Verify that the restaurant exists
    const restaurant = await this.restaurantRepository.findOne({
      where: { id: restaurantId },
    });

    if (!restaurant) {
      throw new NotFoundException(`Restaurant with ID "${restaurantId}" not found`);
    }

    // Get all reviews for the restaurant
    const reviews = await this.reviewRepository.find({
      where: { restaurantId },
    });

    if (!reviews.length) {
      return {
        restaurantId,
        averageRating: 0,
        totalReviews: 0,
        ratingDistribution: {
          '1': 0,
          '2': 0,
          '3': 0,
          '4': 0,
          '5': 0,
        },
        totalHelpfulVotes: 0,
        totalUnhelpfulVotes: 0,
        helpfulnessRatio: 0,
      };
    }

    // Calculate average rating
    const ratingSum = reviews.reduce((acc, review) => acc + review.rating, 0);
    const averageRating = parseFloat((ratingSum / reviews.length).toFixed(1));

    // Calculate rating distribution
    const ratingDistribution = {
      '1': 0,
      '2': 0,
      '3': 0,
      '4': 0,
      '5': 0,
    };

    reviews.forEach((review) => {
      const rating = Math.round(review.rating);
      const ratingKey = Math.min(Math.max(rating, 1), 5).toString() as keyof typeof ratingDistribution;
      ratingDistribution[ratingKey]++;
    });

    // Calculate helpful/unhelpful metrics
    const totalHelpfulVotes = reviews.reduce((acc, review) => acc + review.helpfulCount, 0);
    const totalUnhelpfulVotes = reviews.reduce((acc, review) => acc + review.unhelpfulCount, 0);
    const totalVotes = totalHelpfulVotes + totalUnhelpfulVotes;
    const helpfulnessRatio = totalVotes > 0 
      ? parseFloat((totalHelpfulVotes / totalVotes).toFixed(3)) 
      : 0;

    return {
      restaurantId,
      averageRating,
      totalReviews: reviews.length,
      ratingDistribution,
      totalHelpfulVotes,
      totalUnhelpfulVotes,
      helpfulnessRatio,
    };
  }

  async markReviewHelpful(reviewId: string, userId: string, isHelpful: boolean): Promise<Review> {
    // Find the review
    const review = await this.reviewRepository.findOne({
      where: { id: reviewId },
    });

    if (!review) {
      throw new NotFoundException('Review not found');
    }

    // Check if the user has already marked this review
    const existingMark = await this.reviewHelpfulRepository.findOne({
      where: { reviewId, userId },
    });

    // Begin transaction to ensure data consistency
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      if (existingMark) {
        // If the helpfulness status is the same, do nothing
        if (existingMark.isHelpful === isHelpful) {
          return review;
        }

        // If the status has changed, update the mark and adjust counts
        existingMark.isHelpful = isHelpful;
        await queryRunner.manager.save(existingMark);

        // Update the review counts
        if (isHelpful) {
          review.helpfulCount += 1;
          review.unhelpfulCount -= 1;
        } else {
          review.helpfulCount -= 1;
          review.unhelpfulCount += 1;
        }
      } else {
        // Create a new mark
        const newMark = this.reviewHelpfulRepository.create({
          reviewId,
          userId,
          isHelpful,
        });
        await queryRunner.manager.save(newMark);

        // Update the review counts
        if (isHelpful) {
          review.helpfulCount += 1;
        } else {
          review.unhelpfulCount += 1;
        }
      }

      // Save the updated review
      await queryRunner.manager.save(review);
      await queryRunner.commitTransaction();

      return review;
    } catch (error) {
      // Roll back the transaction if anything goes wrong
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      // Release the query runner
      await queryRunner.release();
    }
  }

  async findByRestaurantOwner(ownerId: string, filterDto: FilterReviewsDto): Promise<PaginatedReviewsResponse> {
    console.log('Finding reviews for restaurant owner:', ownerId, 'with filters:', filterDto);
    
    const queryBuilder = this.reviewRepository
      .createQueryBuilder('review')
      .leftJoinAndSelect('review.restaurant', 'restaurant')
      .leftJoinAndSelect('review.user', 'user')
      .leftJoinAndSelect('review.helpful', 'helpful')
      .where('restaurant.owner.id = :ownerId', { ownerId });
    
    // Apply additional filters from FilterReviewsDto
    if (filterDto.restaurantId) {
      queryBuilder.andWhere('restaurant.id = :restaurantId', { restaurantId: filterDto.restaurantId });
    }
    
    if (filterDto.minRating) {
      queryBuilder.andWhere('review.rating >= :minRating', { minRating: filterDto.minRating });
    }
    
    if (filterDto.maxRating) {
      queryBuilder.andWhere('review.rating <= :maxRating', { maxRating: filterDto.maxRating });
    }
    
    if (filterDto.userId) {
      queryBuilder.andWhere('user.id = :userId', { userId: filterDto.userId });
    }
    
    if (filterDto.search) {
      queryBuilder.andWhere('(review.comment ILIKE :search OR user.name ILIKE :search)', {
        search: `%${filterDto.search}%`,
      });
    }
    
    // Apply sorting
    const sortBy = filterDto.sortBy || 'created_at';
    const sortOrder = filterDto.sortOrder || 'DESC';
    queryBuilder.orderBy(`review.${sortBy}`, sortOrder as 'ASC' | 'DESC');
    
    // Apply pagination
    const page = filterDto.page || 1;
    const limit = filterDto.limit || 10;
    const skip = (page - 1) * limit;
    
    queryBuilder.skip(skip).take(limit);
    
    const [reviews, total] = await queryBuilder.getManyAndCount();
    
    console.log(`Found ${reviews.length} reviews for restaurant owner ${ownerId} (total: ${total})`);
    
    return {
      data: reviews,
      total,
      page,
      limit,
      pageCount: Math.ceil(total / limit),
    };
  }
} 