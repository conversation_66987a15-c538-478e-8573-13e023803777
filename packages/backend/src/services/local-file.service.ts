import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as fs from 'fs';
import * as path from 'path';
import { v4 as uuidv4 } from 'uuid';

@Injectable()
export class LocalFileService {
  private readonly uploadPath: string;
  private readonly baseUrl: string;

  constructor(private configService: ConfigService) {
    this.uploadPath = path.join(process.cwd(), 'uploads', 'restaurants');
    this.baseUrl = this.configService.get('BASE_URL') || 'http://localhost:7000';
    
    // Ensure upload directory exists
    if (!fs.existsSync(this.uploadPath)) {
      fs.mkdirSync(this.uploadPath, { recursive: true });
    }
  }

  async uploadImage(file: Express.Multer.File, folder: string = 'general'): Promise<any> {
    const fileExtension = path.extname(file.originalname);
    const fileName = `${uuidv4()}${fileExtension}`;
    const folderPath = path.join(this.uploadPath, folder);
    
    // Create folder if it doesn't exist
    if (!fs.existsSync(folderPath)) {
      fs.mkdirSync(folderPath, { recursive: true });
    }
    
    const filePath = path.join(folderPath, fileName);
    
    // Save file
    fs.writeFileSync(filePath, file.buffer);
    
    // Return URL
    const publicUrl = `${this.baseUrl}/uploads/restaurants/${folder}/${fileName}`;
    
    return {
      secure_url: publicUrl,
      public_id: `${folder}/${fileName}`,
      original_filename: file.originalname,
      bytes: file.size,
    };
  }

  async deleteImage(publicId: string): Promise<any> {
    const filePath = path.join(this.uploadPath, publicId);
    
    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
      return { result: 'ok' };
    }
    
    return { result: 'not found' };
  }

  async uploadMultipleImages(files: Express.Multer.File[], folder: string = 'general'): Promise<any[]> {
    const uploadPromises = files.map(file => this.uploadImage(file, folder));
    return Promise.all(uploadPromises);
  }
} 