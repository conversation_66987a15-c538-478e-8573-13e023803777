import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { UserCredit } from './entities/user-credit.entity';
import { CreditTransaction, TransactionType, TransactionStatus } from './entities/credit-transaction.entity';
import { CreateCreditTransactionDto } from './dto/create-credit-transaction.dto';

@Injectable()
export class CreditsService {
  constructor(
    @InjectRepository(UserCredit)
    private userCreditRepository: Repository<UserCredit>,
    @InjectRepository(CreditTransaction)
    private creditTransactionRepository: Repository<CreditTransaction>,
  ) {}

  async getUserCredits(userId: string): Promise<UserCredit> {
    let userCredit = await this.userCreditRepository.findOne({
      where: { user: { id: userId } },
      relations: ['user'],
    });

    if (!userCredit) {
      userCredit = this.userCreditRepository.create({
        user: { id: userId },
        balance: 0,
        lifetime_earned: 0,
      });
      userCredit = await this.userCreditRepository.save(userCredit);
    }

    return userCredit;
  }

  async addCredits(createTransactionDto: CreateCreditTransactionDto): Promise<CreditTransaction> {
    const userCredit = await this.getUserCredits(createTransactionDto.user_id);
    
    const transaction = this.creditTransactionRepository.create({
      ...createTransactionDto,
      user: { id: createTransactionDto.user_id },
      status: TransactionStatus.COMPLETED,
    });

    const savedTransaction = await this.creditTransactionRepository.save(transaction);

    // Update user credit balance
    userCredit.balance = Number(userCredit.balance) + createTransactionDto.amount;
    if (createTransactionDto.amount > 0) {
      userCredit.lifetime_earned = Number(userCredit.lifetime_earned) + createTransactionDto.amount;
    }
    await this.userCreditRepository.save(userCredit);

    return savedTransaction;
  }

  async deductCredits(userId: string, amount: number, description: string, referenceId?: string): Promise<CreditTransaction> {
    const userCredit = await this.getUserCredits(userId);
    
    if (Number(userCredit.balance) < amount) {
      throw new BadRequestException('Insufficient credit balance');
    }

    const transaction = this.creditTransactionRepository.create({
      user: { id: userId },
      amount: -amount,
      transaction_type: TransactionType.ORDER_PAYMENT,
      description,
      reference_id: referenceId,
      status: TransactionStatus.COMPLETED,
    });

    const savedTransaction = await this.creditTransactionRepository.save(transaction);

    // Update user credit balance
    userCredit.balance = Number(userCredit.balance) - amount;
    await this.userCreditRepository.save(userCredit);

    return savedTransaction;
  }

  async getCreditTransactions(userId: string): Promise<CreditTransaction[]> {
    return this.creditTransactionRepository.find({
      where: { user: { id: userId } },
      order: { created_at: 'DESC' },
    });
  }

  async getAllTransactions(): Promise<CreditTransaction[]> {
    return this.creditTransactionRepository.find({
      relations: ['user'],
      order: { created_at: 'DESC' },
    });
  }

  async reverseTransaction(transactionId: string): Promise<CreditTransaction> {
    const transaction = await this.creditTransactionRepository.findOne({
      where: { id: transactionId },
      relations: ['user'],
    });

    if (!transaction) {
      throw new NotFoundException(`Transaction with ID ${transactionId} not found`);
    }

    if (transaction.status === TransactionStatus.REVERSED) {
      throw new BadRequestException('Transaction is already reversed');
    }

    // Create reverse transaction
    const reverseTransaction = this.creditTransactionRepository.create({
      user: transaction.user,
      amount: -transaction.amount,
      transaction_type: TransactionType.ADMIN_ADJUSTMENT,
      description: `Reversal of transaction ${transactionId}`,
      reference_id: transactionId,
      status: TransactionStatus.COMPLETED,
    });

    const savedReverseTransaction = await this.creditTransactionRepository.save(reverseTransaction);

    // Update original transaction status
    transaction.status = TransactionStatus.REVERSED;
    await this.creditTransactionRepository.save(transaction);

    // Update user credit balance
    const userCredit = await this.getUserCredits(transaction.user.id);
    userCredit.balance = Number(userCredit.balance) - transaction.amount;
    await this.userCreditRepository.save(userCredit);

    return savedReverseTransaction;
  }

  async getCreditStats(): Promise<any> {
    const totalUsers = await this.userCreditRepository.count();
    
    const totalCreditsIssued = await this.creditTransactionRepository
      .createQueryBuilder('transaction')
      .select('SUM(transaction.amount)', 'total')
      .where('transaction.amount > 0')
      .andWhere('transaction.status = :status', { status: TransactionStatus.COMPLETED })
      .getRawOne();

    const totalCreditsUsed = await this.creditTransactionRepository
      .createQueryBuilder('transaction')
      .select('SUM(ABS(transaction.amount))', 'total')
      .where('transaction.amount < 0')
      .andWhere('transaction.status = :status', { status: TransactionStatus.COMPLETED })
      .getRawOne();

    const totalOutstandingBalance = await this.userCreditRepository
      .createQueryBuilder('credit')
      .select('SUM(credit.balance)', 'total')
      .getRawOne();

    return {
      totalUsers,
      totalCreditsIssued: Number(totalCreditsIssued.total) || 0,
      totalCreditsUsed: Number(totalCreditsUsed.total) || 0,
      totalOutstandingBalance: Number(totalOutstandingBalance.total) || 0,
    };
  }

  async getAllUserCredits(options?: { page?: number; limit?: number; search?: string }): Promise<{ data: { id: string; user: { id: string; name: string }; balance: number }[]; total: number }> {
    const page = options?.page ? Number(options.page) : 1;
    const limit = options?.limit ? Number(options.limit) : 10;
    const search = options?.search?.trim();

    const query = this.userCreditRepository.createQueryBuilder('credit')
      .leftJoinAndSelect('credit.user', 'user');

    if (search) {
      query.andWhere('user.name ILIKE :search OR user.id ILIKE :search', { search: `%${search}%` });
    }

    const [credits, total] = await query
      .skip((page - 1) * limit)
      .take(limit)
      .getManyAndCount();

    return {
      data: credits.map(credit => ({
        id: credit.id,
        user: { id: credit.user.id, name: credit.user.name },
        balance: Number(credit.balance),
      })),
      total,
    };
  }
} 