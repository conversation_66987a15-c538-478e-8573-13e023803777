import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  UseGuards,
  Query,
} from '@nestjs/common';
import { CreditsService } from './credits.service';
import { CreateCreditTransactionDto } from './dto/create-credit-transaction.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';

@Controller('credits')
@UseGuards(JwtAuthGuard)
export class CreditsController {
  constructor(private readonly creditsService: CreditsService) {}

  @Get('user/:userId')
  getUserCredits(@Param('userId') userId: string) {
    return this.creditsService.getUserCredits(userId);
  }

  @Post('add')
  addCredits(@Body() createTransactionDto: CreateCreditTransactionDto) {
    return this.creditsService.addCredits(createTransactionDto);
  }

  @Post('deduct/:userId')
  deductCredits(
    @Param('userId') userId: string,
    @Body('amount') amount: number,
    @Body('description') description: string,
    @Body('referenceId') referenceId?: string,
  ) {
    return this.creditsService.deductCredits(userId, amount, description, referenceId);
  }

  @Get('transactions/:userId')
  getCreditTransactions(@Param('userId') userId: string) {
    return this.creditsService.getCreditTransactions(userId);
  }

  @Get('transactions')
  getAllTransactions() {
    return this.creditsService.getAllTransactions();
  }

  @Post('reverse/:transactionId')
  reverseTransaction(@Param('transactionId') transactionId: string) {
    return this.creditsService.reverseTransaction(transactionId);
  }

  @Get('stats')
  getCreditStats() {
    return this.creditsService.getCreditStats();
  }

  @Get()
  getAllUserCredits(
    @Query('page') page?: number,
    @Query('limit') limit?: number,
    @Query('search') search?: string,
  ) {
    return this.creditsService.getAllUserCredits({ page, limit, search });
  }
} 