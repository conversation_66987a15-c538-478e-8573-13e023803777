import { Column, CreateDateColumn, Entity, ManyToOne, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm';
import { Restaurant } from './restaurant.entity';
import { User } from '../../users/entities/user.entity';
import { ApiProperty } from '@nestjs/swagger';

export enum PhotoCategory {
  COVER = 'cover',
  GALLERY = 'gallery',
  MENU = 'menu',
  INTERIOR = 'interior',
  EXTERIOR = 'exterior',
}

@Entity('photo')
export class Photo {
  @ApiProperty({
    description: 'Unique identifier for the photo',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ApiProperty({
    description: 'Restaurant this photo belongs to',
    type: () => Restaurant,
  })
  @ManyToOne(() => Restaurant, (restaurant) => restaurant.photos, { onDelete: 'CASCADE' })
  restaurant: Restaurant;

  @ApiProperty({
    description: 'Restaurant ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @Column({ name: 'restaurant_id' })
  restaurant_id: string;

  @ApiProperty({
    description: 'User who uploaded this photo',
    type: () => User,
  })
  @ManyToOne(() => User, (user) => user.photos)
  user: User;

  @ApiProperty({
    description: 'User ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @Column({ name: 'user_id' })
  user_id: string;

  @ApiProperty({
    description: 'URL of the photo',
    example: 'https://example.com/photos/restaurant-photo.jpg',
  })
  @Column()
  url: string;

  @ApiProperty({
    description: 'Category of the photo',
    enum: PhotoCategory,
    example: PhotoCategory.GALLERY,
  })
  @Column({
    type: 'enum',
    enum: PhotoCategory,
    default: PhotoCategory.GALLERY,
  })
  category: PhotoCategory;

  @ApiProperty({
    description: 'Optional caption or description for the photo',
    example: 'Delicious kabab platter',
    required: false,
  })
  @Column({ nullable: true })
  caption: string;

  @ApiProperty({
    description: 'Whether this photo is the cover photo for the restaurant',
    example: false,
  })
  @Column({ default: false })
  is_cover: boolean;

  @ApiProperty({
    description: 'Display order of the photo',
    example: 1,
  })
  @Column({ default: 0 })
  display_order: number;

  @ApiProperty({
    description: 'When the photo was created',
    example: '2023-01-01T00:00:00Z',
  })
  @CreateDateColumn()
  created_at: Date;

  @ApiProperty({
    description: 'When the photo was last updated',
    example: '2023-01-02T00:00:00Z',
  })
  @UpdateDateColumn()
  updated_at: Date;
} 