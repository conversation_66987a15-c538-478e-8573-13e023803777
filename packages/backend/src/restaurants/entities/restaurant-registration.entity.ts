import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, ManyToOne } from 'typeorm';
import { User } from '../../users/entities/user.entity';

export enum RegistrationStatus {
  PENDING = 'pending',
  UNDER_REVIEW = 'under_review',
  APPROVED = 'approved',
  REJECTED = 'rejected',
  REQUIRES_DOCUMENTS = 'requires_documents'
}

@Entity('restaurant_registrations')
export class RestaurantRegistration {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  // Manager Information
  @Column()
  managerName: string;

  @Column()
  managerPhone: string;

  @Column()
  managerEmail: string;

  @Column()
  nationalIdNumber: string;

  @Column({ nullable: true })
  nationalIdFrontPath: string;

  @Column({ nullable: true })
  nationalIdBackPath: string;

  // Restaurant Information
  @Column()
  restaurantName: string;

  @Column()
  restaurantPhone: string;

  @Column({ nullable: true })
  restaurantEmail: string;

  @Column('text')
  description: string;

  @Column('json')
  cuisineTypes: string[];

  // Location & Address
  @Column()
  address: string;

  @Column()
  city: string;

  @Column()
  province: string;

  @Column({ nullable: true })
  postalCode: string;

  @Column('decimal', { precision: 10, scale: 7 })
  latitude: number;

  @Column('decimal', { precision: 10, scale: 7 })
  longitude: number;

  // Business Documents
  @Column({ nullable: true })
  businessLicensePath: string;

  // Operating Hours
  @Column('json')
  operatingHours: {
    [key: string]: {
      open: string;
      close: string;
      isOpen: boolean;
    };
  };

  // Delivery Settings
  @Column('int')
  deliveryRadius: number;

  @Column('int')
  minimumOrder: number;

  @Column()
  estimatedDeliveryTime: string;

  // Status and Review
  @Column({
    type: 'enum',
    enum: RegistrationStatus,
    default: RegistrationStatus.PENDING
  })
  status: RegistrationStatus;

  @Column({ nullable: true, type: 'text' })
  rejectionReason: string;

  @Column({ nullable: true, type: 'text' })
  adminNotes: string;

  @ManyToOne(() => User, { nullable: true })
  reviewedBy: User;

  @Column({ nullable: true })
  reviewedAt: Date;

  @CreateDateColumn()
  submittedAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
} 