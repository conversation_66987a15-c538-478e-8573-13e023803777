import {
  Controller,
  Post,
  Get,
  Patch,
  Body,
  Param,
  Query,
  UseInterceptors,
  UploadedFiles,
  UseGuards,
  Request,
  BadRequestException
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiConsumes, ApiBearerAuth } from '@nestjs/swagger';
import { FileFieldsInterceptor } from '@nestjs/platform-express';
import { RestaurantRegistrationService } from '../services/restaurant-registration.service';
import { RegisterRestaurantDto, RestaurantRegistrationResponseDto } from '../dto/register-restaurant.dto';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../auth/guards/roles.guard';
import { Roles } from '../../auth/decorators/roles.decorator';
import { UserRole } from '../../users/entities/user.entity';
import { RegistrationStatus } from '../entities/restaurant-registration.entity';

@ApiTags('restaurant-registration')
@Controller('restaurant-registration')
export class RestaurantRegistrationController {
  constructor(
    private readonly registrationService: RestaurantRegistrationService
  ) {}

  @Post('submit')
  @ApiOperation({ summary: 'Submit restaurant registration request' })
  @ApiConsumes('multipart/form-data')
  @ApiResponse({ 
    status: 201, 
    description: 'Registration submitted successfully',
    type: RestaurantRegistrationResponseDto 
  })
  @ApiResponse({ status: 400, description: 'Bad request - validation failed' })
  @UseInterceptors(FileFieldsInterceptor([
    { name: 'nationalIdFront', maxCount: 1 },
    { name: 'nationalIdBack', maxCount: 1 },
    { name: 'businessLicense', maxCount: 1 }
  ]))
  async submitRegistration(
    @Body() dto: RegisterRestaurantDto,
    @UploadedFiles() files: {
      nationalIdFront?: Express.Multer.File[],
      nationalIdBack?: Express.Multer.File[],
      businessLicense?: Express.Multer.File[]
    }
  ): Promise<RestaurantRegistrationResponseDto> {
    // Validate required files
    if (!files.nationalIdFront || !files.nationalIdBack) {
      throw new BadRequestException('National ID front and back photos are required');
    }

    // Validate file types and sizes
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'application/pdf'];
    const maxSize = 5 * 1024 * 1024; // 5MB

    const allFiles = [
      ...(files.nationalIdFront || []),
      ...(files.nationalIdBack || []),
      ...(files.businessLicense || [])
    ];

    for (const file of allFiles) {
      if (!allowedTypes.includes(file.mimetype)) {
        throw new BadRequestException(`File ${file.originalname} has invalid type. Allowed: JPEG, PNG, WebP, PDF`);
      }
      if (file.size > maxSize) {
        throw new BadRequestException(`File ${file.originalname} is too large. Maximum size: 5MB`);
      }
    }

    const registration = await this.registrationService.submitRegistration(dto, files);

    return {
      id: registration.id,
      status: registration.status,
      message: 'Registration submitted successfully. You will be contacted within 24 hours.',
      submittedAt: registration.submittedAt
    };
  }

  @Get('list')
  // @UseGuards(JwtAuthGuard, RolesGuard)
  // @Roles(UserRole.ADMIN)
  // @ApiBearerAuth()
  @ApiOperation({ summary: 'Get all registration requests (Admin only)' })
  @ApiResponse({ status: 200, description: 'List of registration requests' })
  async getAllRegistrations(
    @Query('page') page: number = 1,
    @Query('limit') limit: number = 10,
    @Query('status') status?: RegistrationStatus
  ) {
    return this.registrationService.getAllRegistrations(
      Math.max(1, page),
      Math.min(50, Math.max(1, limit)),
      status
    );
  }

  @Get('stats')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get registration statistics (Admin only)' })
  @ApiResponse({ status: 200, description: 'Registration statistics' })
  async getRegistrationStats() {
    return this.registrationService.getRegistrationStats();
  }

  @Get(':id')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get registration by ID (Admin only)' })
  @ApiResponse({ status: 200, description: 'Registration details' })
  @ApiResponse({ status: 404, description: 'Registration not found' })
  async getRegistrationById(@Param('id') id: string) {
    return this.registrationService.getRegistrationById(id);
  }

  @Patch(':id/review')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Review registration request (Admin only)' })
  @ApiResponse({ status: 200, description: 'Registration reviewed successfully' })
  @ApiResponse({ status: 404, description: 'Registration not found' })
  async reviewRegistration(
    @Param('id') id: string,
    @Body() body: {
      status: RegistrationStatus,
      notes?: string,
      rejectionReason?: string
    },
    @Request() req
  ) {
    return this.registrationService.reviewRegistration(
      id,
      req.user.userId,
      body.status,
      body.notes,
      body.rejectionReason
    );
  }

  @Patch(':id/approve')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Approve registration and create restaurant (Admin only)' })
  @ApiResponse({ status: 200, description: 'Registration approved and restaurant created' })
  async approveRegistration(
    @Param('id') id: string,
    @Body() body: { notes?: string },
    @Request() req
  ) {
    return this.registrationService.reviewRegistration(
      id,
      req.user.userId,
      RegistrationStatus.APPROVED,
      body.notes
    );
  }

  @Patch(':id/reject')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Reject registration request (Admin only)' })
  @ApiResponse({ status: 200, description: 'Registration rejected' })
  async rejectRegistration(
    @Param('id') id: string,
    @Body() body: { rejectionReason: string, notes?: string },
    @Request() req
  ) {
    if (!body.rejectionReason) {
      throw new BadRequestException('Rejection reason is required');
    }

    return this.registrationService.reviewRegistration(
      id,
      req.user.userId,
      RegistrationStatus.REJECTED,
      body.notes,
      body.rejectionReason
    );
  }
} 