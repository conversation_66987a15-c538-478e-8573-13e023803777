import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsOptional, IsString } from 'class-validator';
import { PhotoCategory } from '../entities/photo.entity';

export class CreatePhotoDto {
  @ApiProperty({
    description: 'Category of the photo',
    enum: PhotoCategory,
    example: PhotoCategory.GALLERY,
    required: false,
  })
  @IsOptional()
  @IsEnum(PhotoCategory)
  category?: PhotoCategory;

  @ApiProperty({
    description: 'Optional caption for the photo',
    example: 'Delicious kabab platter',
    required: false,
  })
  @IsOptional()
  @IsString()
  caption?: string;
}

export class UpdatePhotoDto {
  @ApiProperty({
    description: 'Category of the photo',
    enum: PhotoCategory,
    example: PhotoCategory.GALLERY,
    required: false,
  })
  @IsOptional()
  @IsEnum(PhotoCategory)
  category?: PhotoCategory;

  @ApiProperty({
    description: 'Optional description for the photo',
    example: 'Delicious kabab platter',
    required: false,
  })
  @IsOptional()
  @IsString()
  description?: string;
}

export class ReorderPhotosDto {
  @ApiProperty({
    description: 'Array of photo IDs in the desired order',
    example: ['photo-1-id', 'photo-2-id', 'photo-3-id'],
    type: [String],
  })
  @IsString({ each: true })
  photoIds: string[];
} 