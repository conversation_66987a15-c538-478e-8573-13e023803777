import { <PERSON><PERSON><PERSON>, IsEmail, IsOptional, IsNumber, IsObject, IsArray, IsNotEmpty, IsPhoneNumber, IsBoolean } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { Transform, Type } from 'class-transformer';

export class RegisterRestaurantDto {
  // Manager Information
  @ApiProperty({ description: 'Manager full name' })
  @IsString()
  @IsNotEmpty()
  managerName: string;

  @ApiProperty({ description: 'Manager phone number' })
  @IsString()
  @IsNotEmpty()
  managerPhone: string;

  @ApiProperty({ description: 'Manager email address' })
  @IsEmail()
  @IsNotEmpty()
  managerEmail: string;

  @ApiProperty({ description: 'National ID number (13 digits in format: xxxx-xxxx-xxxxx)' })
  @IsString()
  @IsNotEmpty()
  nationalIdNumber: string;

  // Restaurant Information
  @ApiProperty({ description: 'Restaurant name' })
  @IsString()
  @IsNotEmpty()
  restaurantName: string;

  @ApiProperty({ description: 'Restaurant phone number' })
  @IsString()
  @IsNotEmpty()
  restaurantPhone: string;

  @ApiProperty({ description: 'Restaurant email address', required: false })
  @IsEmail()
  @IsOptional()
  restaurantEmail?: string;

  @ApiProperty({ description: 'Restaurant description' })
  @IsString()
  @IsNotEmpty()
  description: string;

  @ApiProperty({ description: 'Types of cuisine offered', type: [String] })
  @IsArray()
  @IsString({ each: true })
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      try {
        return JSON.parse(value);
      } catch {
        return value;
      }
    }
    return value;
  })
  cuisineTypes: string[];

  // Location & Address
  @ApiProperty({ description: 'Full address' })
  @IsString()
  @IsNotEmpty()
  address: string;

  @ApiProperty({ description: 'City' })
  @IsString()
  @IsNotEmpty()
  city: string;

  @ApiProperty({ description: 'Province/State' })
  @IsString()
  @IsNotEmpty()
  province: string;

  @ApiProperty({ description: 'Postal code', required: false })
  @IsString()
  @IsOptional()
  postalCode?: string;

  @ApiProperty({ description: 'Latitude coordinate' })
  @IsNumber()
  @IsNotEmpty()
  @Transform(({ value }) => parseFloat(value))
  latitude: number;

  @ApiProperty({ description: 'Longitude coordinate' })
  @IsNumber()
  @IsNotEmpty()
  @Transform(({ value }) => parseFloat(value))
  longitude: number;

  // Operating Hours
  @ApiProperty({ 
    description: 'Operating hours for each day',
    type: 'object',
    additionalProperties: {
      type: 'object',
      properties: {
        open: { type: 'string' },
        close: { type: 'string' },
        isOpen: { type: 'boolean' }
      }
    }
  })
  @IsObject()
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      try {
        return JSON.parse(value);
      } catch {
        return value;
      }
    }
    return value;
  })
  operatingHours: {
    [key: string]: {
      open: string;
      close: string;
      isOpen: boolean;
    };
  };

  // Delivery Settings
  @ApiProperty({ description: 'Delivery radius in kilometers' })
  @IsNumber()
  @Transform(({ value }) => parseFloat(value))
  deliveryRadius: number;

  @ApiProperty({ description: 'Minimum order amount' })
  @IsNumber()
  @Transform(({ value }) => parseFloat(value))
  minimumOrder: number;

  @ApiProperty({ description: 'Estimated delivery time' })
  @IsString()
  estimatedDeliveryTime: string;
}

export class RestaurantRegistrationResponseDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  status: string;

  @ApiProperty()
  message: string;

  @ApiProperty()
  submittedAt: Date;
} 