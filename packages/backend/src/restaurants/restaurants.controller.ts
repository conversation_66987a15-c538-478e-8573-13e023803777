import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Query,
  UseInterceptors,
  UploadedFiles,
  NotFoundException,
} from '@nestjs/common';
import { FileFieldsInterceptor } from '@nestjs/platform-express';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiConsumes } from '@nestjs/swagger';
import { RestaurantsService } from './restaurants.service';
import { PhotoService } from './services/photo.service';
import { CreateRestaurantDto } from './dto/create-restaurant.dto';
import { UpdateRestaurantDto } from './dto/update-restaurant.dto';
import { CreatePhotoDto } from './dto/create-photo.dto';
import { UpdatePhotoDto } from './dto/update-photo.dto';
import { ReorderPhotosDto } from './dto/reorder-photos.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { GetUser } from '../auth/decorators/get-user.decorator';
import { UserRole } from '../users/entities/user.entity';
import { PhotoCategory } from './entities/photo.entity';

@ApiTags('restaurants')
@Controller('restaurants')
export class RestaurantsController {
  constructor(
    private readonly restaurantsService: RestaurantsService,
    private readonly photoService: PhotoService,
  ) {}

  @Post()
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.RESTAURANT_OWNER, UserRole.ADMIN)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Create a new restaurant' })
  @ApiResponse({ status: 201, description: 'Restaurant created successfully' })
  create(@Body() createRestaurantDto: CreateRestaurantDto, @GetUser() user: any) {
    return this.restaurantsService.createRestaurant(createRestaurantDto);
  }

  @Get()
  @ApiOperation({ summary: 'Get all restaurants' })
  @ApiResponse({ status: 200, description: 'List of restaurants' })
  findAll(@Query() filterDto: any) {
    return this.restaurantsService.findAll(filterDto);
  }

  @Get('owner')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.RESTAURANT_OWNER, UserRole.ADMIN)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get restaurant owned by current user' })
  @ApiResponse({ status: 200, description: 'Restaurant owned by current user' })
  async getOwnerRestaurant(@GetUser() user: any) {
    const restaurants = await this.restaurantsService.findByOwner(user.id);
    if (restaurants.length === 0) {
      throw new NotFoundException('No restaurant found for this owner');
    }
    return restaurants[0]; // Return the first restaurant (assuming one restaurant per owner)
  }

  @Get('stats')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.RESTAURANT_OWNER, UserRole.ADMIN)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get restaurant statistics for current user' })
  @ApiResponse({ status: 200, description: 'Restaurant statistics' })
  async getRestaurantStats(@GetUser() user: any) {
    const restaurants = await this.restaurantsService.findByOwner(user.id);
    if (restaurants.length === 0) {
      throw new NotFoundException('No restaurant found for this owner');
    }
    return this.restaurantsService.getRestaurantStats(restaurants[0].id);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a restaurant by ID' })
  @ApiResponse({ status: 200, description: 'Restaurant details' })
  findOne(@Param('id') id: string) {
    return this.restaurantsService.findOne(id);
  }

  @Patch(':id')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.RESTAURANT_OWNER, UserRole.ADMIN)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Update a restaurant' })
  @ApiResponse({ status: 200, description: 'Restaurant updated successfully' })
  update(
    @Param('id') id: string,
    @Body() updateRestaurantDto: UpdateRestaurantDto,
    @GetUser() user: any,
  ) {
    return this.restaurantsService.update(id, updateRestaurantDto, user);
  }

  @Delete(':id')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.RESTAURANT_OWNER, UserRole.ADMIN)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Delete a restaurant' })
  @ApiResponse({ status: 200, description: 'Restaurant deleted successfully' })
  remove(@Param('id') id: string, @GetUser() user: any) {
    return this.restaurantsService.remove(id, user);
  }

  // Photo Management Endpoints

  @Get(':id/photos')
  @ApiOperation({ summary: 'Get all photos for a restaurant' })
  @ApiResponse({ status: 200, description: 'List of restaurant photos' })
  getPhotos(
    @Param('id') restaurantId: string,
    @Query('category') category?: PhotoCategory,
  ) {
    return this.photoService.findByRestaurant(restaurantId, category);
  }

  @Post(':id/photos')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.RESTAURANT_OWNER, UserRole.ADMIN)
  @ApiBearerAuth()
  @UseInterceptors(FileFieldsInterceptor([{ name: 'photos', maxCount: 10 }]))
  @ApiConsumes('multipart/form-data')
  @ApiOperation({ summary: 'Upload photos for a restaurant' })
  @ApiResponse({ status: 201, description: 'Photos uploaded successfully' })
  uploadPhotos(
    @Param('id') restaurantId: string,
    @GetUser() user: any,
    @UploadedFiles() files: { photos?: Express.Multer.File[] },
    @Body() createPhotoDto: CreatePhotoDto,
  ) {
    return this.photoService.uploadPhotos(
      restaurantId,
      user.id,
      files.photos || [],
      createPhotoDto,
    );
  }

  @Patch(':id/photos/:photoId')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.RESTAURANT_OWNER, UserRole.ADMIN)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Update photo metadata' })
  @ApiResponse({ status: 200, description: 'Photo updated successfully' })
  updatePhoto(
    @Param('photoId') photoId: string,
    @GetUser() user: any,
    @Body() updatePhotoDto: UpdatePhotoDto,
  ) {
    return this.photoService.update(photoId, user.id, updatePhotoDto);
  }

  @Delete(':id/photos/:photoId')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.RESTAURANT_OWNER, UserRole.ADMIN)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Delete a photo' })
  @ApiResponse({ status: 200, description: 'Photo deleted successfully' })
  deletePhoto(@Param('photoId') photoId: string, @GetUser() user: any) {
    return this.photoService.remove(photoId, user.id);
  }

  @Patch(':id/photos/:photoId/cover')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.RESTAURANT_OWNER, UserRole.ADMIN)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Set photo as cover photo' })
  @ApiResponse({ status: 200, description: 'Cover photo set successfully' })
  setCoverPhoto(@Param('photoId') photoId: string, @GetUser() user: any) {
    return this.photoService.setCoverPhoto(photoId, user.id);
  }

  @Patch(':id/photos/reorder')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.RESTAURANT_OWNER, UserRole.ADMIN)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Reorder photos' })
  @ApiResponse({ status: 200, description: 'Photos reordered successfully' })
  reorderPhotos(
    @Param('id') restaurantId: string,
    @GetUser() user: any,
    @Body() reorderDto: ReorderPhotosDto,
  ) {
    return this.photoService.reorderPhotos(restaurantId, user.id, reorderDto);
  }

  @Get(':id/photos/stats')
  @ApiOperation({ summary: 'Get photo statistics for a restaurant' })
  @ApiResponse({ status: 200, description: 'Photo statistics' })
  getPhotoStats(@Param('id') restaurantId: string) {
    return this.photoService.getPhotoStats(restaurantId);
  }
} 