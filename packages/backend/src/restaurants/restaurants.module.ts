import { Module, forwardRef } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { RestaurantsController } from './restaurants.controller';
import { RestaurantsService } from './restaurants.service';
import { Restaurant } from './entities/restaurant.entity';
import { RestaurantStaff } from './entities/restaurant-staff.entity';
import { Photo } from './entities/photo.entity';
import { MenuItem } from './entities/menu-item.entity';
import { Promotion } from './entities/promotion.entity';
import { MenuController } from './controllers/menu.controller';
import { PromotionsController } from './controllers/promotions.controller';
import { MenuService } from './services/menu.service';
import { PromotionService } from './services/promotion.service';
import { PhotoService } from './services/photo.service';
import { CloudinaryService } from '../services/cloudinary.service';
import { AuthModule } from '../auth/auth.module';
import { UsersModule } from '../users/users.module';
import { FavoritesModule } from '../favorites/favorites.module';
import { User } from '../users/entities/user.entity';
import { RestaurantRegistration } from './entities/restaurant-registration.entity';
import { RestaurantRegistrationService } from './services/restaurant-registration.service';
import { RestaurantRegistrationController } from './controllers/restaurant-registration.controller';

@Module({
  imports: [
    TypeOrmModule.forFeature([Restaurant, RestaurantStaff, Photo, MenuItem, Promotion, User, RestaurantRegistration]),
    AuthModule,
    UsersModule,
    forwardRef(() => FavoritesModule),
  ],
  controllers: [RestaurantsController, MenuController, PromotionsController, RestaurantRegistrationController],
  providers: [RestaurantsService, MenuService, PromotionService, PhotoService, CloudinaryService, RestaurantRegistrationService],
  exports: [RestaurantsService, MenuService, PromotionService, PhotoService, CloudinaryService, RestaurantRegistrationService],
})
export class RestaurantsModule {} 