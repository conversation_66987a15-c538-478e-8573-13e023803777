import { Injectable, BadRequestException, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { RestaurantRegistration, RegistrationStatus } from '../entities/restaurant-registration.entity';
import { RegisterRestaurantDto } from '../dto/register-restaurant.dto';
import { CloudinaryService } from '../../services/cloudinary.service';
import { User } from '../../users/entities/user.entity';
import { Restaurant } from '../entities/restaurant.entity';

@Injectable()
export class RestaurantRegistrationService {
  constructor(
    @InjectRepository(RestaurantRegistration)
    private readonly registrationRepository: Repository<RestaurantRegistration>,
    @InjectRepository(Restaurant)
    private readonly restaurantRepository: Repository<Restaurant>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    private readonly cloudinaryService: CloudinaryService,
  ) {}

  async submitRegistration(
    dto: RegisterRestaurantDto,
    files: {
      nationalIdFront?: Express.Multer.File[],
      nationalIdBack?: Express.Multer.File[],
      businessLicense?: Express.Multer.File[]
    }
  ): Promise<RestaurantRegistration> {
    // Validate phone numbers (Afghanistan format)
    this.validateAfghanPhone(dto.managerPhone, 'Manager phone number');
    this.validateAfghanPhone(dto.restaurantPhone, 'Restaurant phone number');

    // Validate National ID number format
    this.validateNationalIdNumber(dto.nationalIdNumber);

    // Check if restaurant name already exists
    const existingRestaurant = await this.restaurantRepository.findOne({
      where: { name: dto.restaurantName }
    });
    if (existingRestaurant) {
      throw new BadRequestException('Restaurant with this name already exists');
    }

    // Check if manager phone already exists in registrations
    const existingRegistration = await this.registrationRepository.findOne({
      where: { managerPhone: dto.managerPhone, status: RegistrationStatus.PENDING }
    });
    if (existingRegistration) {
      throw new BadRequestException('A registration with this manager phone number is already pending');
    }

    let nationalIdFrontPath: string | null = null;
    let nationalIdBackPath: string | null = null;
    let businessLicensePath: string | null = null;

    try {
      // Upload National ID Front (Required)
      if (files.nationalIdFront && files.nationalIdFront[0]) {
        const frontResult = await this.cloudinaryService.uploadImage(
          files.nationalIdFront[0], 
          'restaurant-registrations/national-ids'
        );
        nationalIdFrontPath = frontResult.secure_url;
      } else {
        throw new BadRequestException('National ID front photo is required');
      }

      // Upload National ID Back (Required)
      if (files.nationalIdBack && files.nationalIdBack[0]) {
        const backResult = await this.cloudinaryService.uploadImage(
          files.nationalIdBack[0], 
          'restaurant-registrations/national-ids'
        );
        nationalIdBackPath = backResult.secure_url;
      } else {
        throw new BadRequestException('National ID back photo is required');
      }

      // Upload Business License (Optional)
      if (files.businessLicense && files.businessLicense[0]) {
        const licenseResult = await this.cloudinaryService.uploadImage(
          files.businessLicense[0], 
          'restaurant-registrations/business-licenses'
        );
        businessLicensePath = licenseResult.secure_url;
      }

      // Create registration record
      const registration = this.registrationRepository.create({
        ...dto,
        nationalIdFrontPath,
        nationalIdBackPath,
        businessLicensePath,
        status: RegistrationStatus.PENDING
      });

      const savedRegistration = await this.registrationRepository.save(registration);

      // TODO: Send notification to admins about new registration
      // await this.notificationService.notifyAdminsNewRegistration(savedRegistration);

      return savedRegistration;

    } catch (error) {
      // Clean up uploaded files if registration fails
      if (nationalIdFrontPath) {
        await this.cloudinaryService.deleteImage(nationalIdFrontPath);
      }
      if (nationalIdBackPath) {
        await this.cloudinaryService.deleteImage(nationalIdBackPath);
      }
      if (businessLicensePath) {
        await this.cloudinaryService.deleteImage(businessLicensePath);
      }
      throw error;
    }
  }

  async getAllRegistrations(
    page: number = 1,
    limit: number = 10,
    status?: RegistrationStatus
  ): Promise<{ data: RestaurantRegistration[], total: number, totalPages: number }> {
    try {
      const where: any = {};
      if (status) {
        where.status = status;
      }

      const [data, total] = await this.registrationRepository.findAndCount({
        where,
        order: { submittedAt: 'DESC' },
        skip: (page - 1) * limit,
        take: limit,
      });

      return {
        data,
        total,
        totalPages: Math.ceil(total / limit)
      };
    } catch (error) {
      console.error('Error in getAllRegistrations:', error);
      throw error;
    }
  }

  async getRegistrationById(id: string): Promise<RestaurantRegistration> {
    const registration = await this.registrationRepository.findOne({
      where: { id },
      relations: ['reviewedBy']
    });

    if (!registration) {
      throw new NotFoundException('Registration not found');
    }

    return registration;
  }

  async reviewRegistration(
    id: string,
    adminId: string,
    status: RegistrationStatus,
    notes?: string,
    rejectionReason?: string
  ): Promise<RestaurantRegistration> {
    const registration = await this.getRegistrationById(id);
    const admin = await this.userRepository.findOne({ where: { id: adminId } });

    if (!admin) {
      throw new NotFoundException('Admin user not found');
    }

    registration.status = status;
    registration.reviewedBy = admin;
    registration.reviewedAt = new Date();
    registration.adminNotes = notes || null;
    registration.rejectionReason = rejectionReason || null;

    const updatedRegistration = await this.registrationRepository.save(registration);

    // If approved, create the actual restaurant
    if (status === RegistrationStatus.APPROVED) {
      await this.createRestaurantFromRegistration(registration);
    }

    // TODO: Send notification to restaurant owner about status update
    // await this.notificationService.notifyRegistrationStatusUpdate(updatedRegistration);

    return updatedRegistration;
  }

  private async createRestaurantFromRegistration(registration: RestaurantRegistration): Promise<Restaurant> {
    // Create or find the restaurant owner user
    let owner = await this.userRepository.findOne({
      where: { phone_number: registration.managerPhone }
    });

    if (!owner) {
      // Create new user account for restaurant owner
      owner = this.userRepository.create({
        phone_number: registration.managerPhone,
        email: registration.managerEmail,
        name: registration.managerName,
        role: 'restaurant_owner' as any
      });
      owner = await this.userRepository.save(owner);
    }

    // Create restaurant
    const restaurant = this.restaurantRepository.create({
      owner,
      name: registration.restaurantName,
      description: registration.description,
      address: registration.address,
      city: registration.city,
      state: registration.province,
      zip_code: registration.postalCode || '',
      phone: registration.restaurantPhone,
      email: registration.restaurantEmail,
      latitude: registration.latitude,
      longitude: registration.longitude,
      hours_of_operation: JSON.stringify(registration.operatingHours),
      status: 'active' as any
    });

    return await this.restaurantRepository.save(restaurant);
  }

  private validateAfghanPhone(phone: string, fieldName: string): void {
    const cleanPhone = phone.replace(/\s/g, '');
    
    // Afghanistan phone number patterns:
    // International format: +93 7X XXX XXXX, +93 07X XXX XXXX  
    // National format: 07X XXX XXXX, 7X XXX XXXX
    // Valid prefixes: 70, 71, 72, 73, 74, 75, 76, 77, 78, 79 (mobile)
    // Also accepts: 07X for national format
    
    const afghanPhoneRegex = /^(\+93|0093|93)?(0?[7][0-9])\d{7}$/;
    
    if (!afghanPhoneRegex.test(cleanPhone)) {
      throw new BadRequestException(`${fieldName} must be a valid Afghanistan phone number (example: +93 70 123 4567 or ************)`);
    }
  }

  private validateNationalIdNumber(nationalId: string): void {
    const cleanId = nationalId.replace(/[-\s]/g, '');
    
    if (!/^\d{13}$/.test(cleanId)) {
      throw new BadRequestException('National ID number must be exactly 13 digits in format: xxxx-xxxx-xxxxx');
    }
  }

  async getRegistrationStats(): Promise<{
    total: number,
    pending: number,
    underReview: number,
    approved: number,
    rejected: number
  }> {
    const [total, pending, underReview, approved, rejected] = await Promise.all([
      this.registrationRepository.count(),
      this.registrationRepository.count({ where: { status: RegistrationStatus.PENDING } }),
      this.registrationRepository.count({ where: { status: RegistrationStatus.UNDER_REVIEW } }),
      this.registrationRepository.count({ where: { status: RegistrationStatus.APPROVED } }),
      this.registrationRepository.count({ where: { status: RegistrationStatus.REJECTED } })
    ]);

    return { total, pending, underReview, approved, rejected };
  }
} 