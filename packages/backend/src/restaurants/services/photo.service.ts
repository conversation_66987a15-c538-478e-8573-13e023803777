import { Injectable, NotFoundException, ForbiddenException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Photo, PhotoCategory } from '../entities/photo.entity';
import { Restaurant } from '../entities/restaurant.entity';
import { CreatePhotoDto } from '../dto/create-photo.dto';
import { UpdatePhotoDto } from '../dto/update-photo.dto';
import { ReorderPhotosDto } from '../dto/reorder-photos.dto';
import { CloudinaryService } from '../../services/cloudinary.service';

@Injectable()
export class PhotoService {
  constructor(
    @InjectRepository(Photo)
    private photoRepository: Repository<Photo>,
    @InjectRepository(Restaurant)
    private restaurantRepository: Repository<Restaurant>,
    private cloudinaryService: CloudinaryService,
  ) {}

  async uploadPhotos(
    restaurantId: string,
    userId: string,
    files: Express.Multer.File[],
    createPhotoDto: CreatePhotoDto,
  ): Promise<Photo[]> {
    // Verify restaurant exists and user owns it
    const restaurant = await this.restaurantRepository.findOne({
      where: { id: restaurantId },
      relations: ['owner'],
    });

    if (!restaurant) {
      throw new NotFoundException('Restaurant not found');
    }

    if (restaurant.owner.id !== userId) {
      throw new ForbiddenException('You can only upload photos to your own restaurant');
    }

    // Upload files to Cloudinary
    const uploadResults = await this.cloudinaryService.uploadMultipleImages(
      files,
      `restaurants/${restaurantId}`
    );

    // Create photo entities
    const photos = uploadResults.map((result, index) => {
      const photo = new Photo();
      photo.url = result.secure_url;
      photo.restaurant_id = restaurantId;
      photo.user_id = userId;
      photo.category = createPhotoDto.category || PhotoCategory.GALLERY;
      photo.caption = createPhotoDto.caption || null;
      photo.is_cover = false;
      photo.display_order = index;
      return photo;
    });

    return this.photoRepository.save(photos);
  }

  async findByRestaurant(restaurantId: string, category?: PhotoCategory): Promise<Photo[]> {
    const queryBuilder = this.photoRepository
      .createQueryBuilder('photo')
      .where('photo.restaurant_id = :restaurantId', { restaurantId })
      .orderBy('photo.display_order', 'ASC')
      .addOrderBy('photo.created_at', 'DESC');

    if (category) {
      queryBuilder.andWhere('photo.category = :category', { category });
    }

    return queryBuilder.getMany();
  }

  async findOne(id: string): Promise<Photo> {
    const photo = await this.photoRepository.findOne({ where: { id } });
    if (!photo) {
      throw new NotFoundException('Photo not found');
    }
    return photo;
  }

  async update(id: string, userId: string, updatePhotoDto: UpdatePhotoDto): Promise<Photo> {
    const photo = await this.findOne(id);

    if (photo.user_id !== userId) {
      throw new ForbiddenException('You can only update your own photos');
    }

    Object.assign(photo, updatePhotoDto);
    return this.photoRepository.save(photo);
  }

  async remove(id: string, userId: string): Promise<void> {
    const photo = await this.findOne(id);

    if (photo.user_id !== userId) {
      throw new ForbiddenException('You can only delete your own photos');
    }

    // Extract public_id from Cloudinary URL to delete from cloud
    const publicId = this.extractPublicId(photo.url);
    if (publicId) {
      await this.cloudinaryService.deleteImage(publicId);
    }

    await this.photoRepository.remove(photo);
  }

  async setCoverPhoto(photoId: string, userId: string): Promise<Photo> {
    const photo = await this.findOne(photoId);

    if (photo.user_id !== userId) {
      throw new ForbiddenException('You can only set cover photos for your own restaurant');
    }

    // Remove existing cover photo
    await this.photoRepository.update(
      { restaurant_id: photo.restaurant_id, is_cover: true },
      { is_cover: false }
    );

    // Set new cover photo
    photo.is_cover = true;
    return this.photoRepository.save(photo);
  }

  async reorderPhotos(restaurantId: string, userId: string, reorderDto: ReorderPhotosDto): Promise<Photo[]> {
    // Verify restaurant ownership
    const restaurant = await this.restaurantRepository.findOne({
      where: { id: restaurantId },
      relations: ['owner'],
    });

    if (!restaurant || restaurant.owner.id !== userId) {
      throw new ForbiddenException('You can only reorder photos for your own restaurant');
    }

    // Update display order for each photo
    const updatePromises = reorderDto.photoIds.map((photoId, index) =>
      this.photoRepository.update({ id: photoId }, { display_order: index })
    );

    await Promise.all(updatePromises);

    return this.findByRestaurant(restaurantId);
  }

  async getPhotoStats(restaurantId: string): Promise<any> {
    const stats = await this.photoRepository
      .createQueryBuilder('photo')
      .select('photo.category', 'category')
      .addSelect('COUNT(*)', 'count')
      .where('photo.restaurant_id = :restaurantId', { restaurantId })
      .groupBy('photo.category')
      .getRawMany();

    const totalPhotos = await this.photoRepository.count({
      where: { restaurant_id: restaurantId },
    });

    const coverPhoto = await this.photoRepository.findOne({
      where: { restaurant_id: restaurantId, is_cover: true },
    });

    return {
      totalPhotos,
      hasCoverPhoto: !!coverPhoto,
      categoryStats: stats.reduce((acc, stat) => {
        acc[stat.category] = parseInt(stat.count);
        return acc;
      }, {}),
    };
  }

  private extractPublicId(url: string): string | null {
    try {
      const parts = url.split('/');
      const filename = parts[parts.length - 1];
      return filename.split('.')[0];
    } catch (error) {
      return null;
    }
  }
} 