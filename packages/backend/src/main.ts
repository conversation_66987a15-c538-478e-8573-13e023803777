import { ValidationPipe } from '@nestjs/common';
import { NestFactory } from '@nestjs/core';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import { AppModule } from './app.module';
import { ConfigService } from '@nestjs/config';
import { IoAdapter } from '@nestjs/platform-socket.io';
import { NestExpressApplication } from '@nestjs/platform-express';

async function bootstrap() {
  const app = await NestFactory.create<NestExpressApplication>(AppModule);
  const configService = app.get(ConfigService);

  // Configure WebSocket adapter
  app.useWebSocketAdapter(new IoAdapter(app));

  // Set global prefix
  const apiPrefix = configService.get<string>('API_PREFIX') || 'api';
  app.setGlobalPrefix(apiPrefix);

  // Enable CORS with WebSocket support
  app.enableCors({
    origin: [
      'http://localhost:3000', // Website
      'http://localhost:3001', // Alternative website port
      'http://localhost:5000', // Admin panel
      'http://localhost:5001', // Alternative admin port
      'http://localhost:5050', // Alternative admin port
      'https://2308d3c1ce15.ngrok-free.app', // Current backend ngrok URL
      'https://*.ngrok-free.app', // Allow any ngrok free URLs
      'https://azkuja.com',
      'https://admin.azkuja.com',
    ],
    methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS'],
    allowedHeaders: [
      'Content-Type',
      'Authorization',
      'Accept',
      'X-Requested-With',
      'x-user-id',
      'x-user-role',
    ],
    credentials: true,
  });

  // Serve static files for testing
  app.useStaticAssets('public');

  // Set up validation
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      transform: true,
      forbidNonWhitelisted: true,
    }),
  );

  // Set up Swagger documentation
  const config = new DocumentBuilder()
    .setTitle('Azkuja Restaurant Directory API')
    .setDescription('The API documentation for Azkuja Restaurant Directory')
    .setVersion('1.0')
    .addBearerAuth()
    .build();
  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup(`${apiPrefix}/docs`, app, document);

  // Start the server
  const port = configService.get<number>('PORT') || 7000;
  const host = configService.get<string>('HOSTNAME') || '0.0.0.0';
  await app.listen(port, host);
  console.log(`Application is running on: http://${host}:${port}/${apiPrefix}`);
}
bootstrap();
