# Dependencies
node_modules
.pnp
.pnp.js
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/sdks
!.yarn/versions

# Build artifacts
.next/
out/
build
dist
.turbo
.vercel

# Testing
coverage

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Debug logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# IDEs and editors
.vscode/*
!.vscode/extensions.json
.idea
.project
.classpath
*.launch
.settings/
*.sublime-workspace

# OS specific
.DS_Store
Thumbs.db

# Ngrok
ngrok.yml
.ngrok2/
.ngrok/

# Expo/React Native
.expo/
ios/
android/
web-build/
*.jks
*.p8
*.p12
*.key
*.mobileprovision
*.orig.*

# Local Netlify folder
.netlify

# Monorepo specific
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz 