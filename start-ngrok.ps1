#!/usr/bin/env pwsh

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "    Azkuja <PERSON>rok Tunnel Starter" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

Write-Host "Checking if services are running..." -ForegroundColor Yellow
Write-Host ""

# Check if services are running
$backend = netstat -an | Select-String ":7000.*LISTENING"
$admin = netstat -an | Select-String ":5000.*LISTENING"
$website = netstat -an | Select-String ":3000.*LISTENING"

if (-not $backend) {
    Write-Host "WARNING: Backend service not detected on port 7000" -ForegroundColor Red
    Write-Host "Please start: cd packages/backend && yarn start:dev" -ForegroundColor Yellow
    Write-Host ""
}

if (-not $admin) {
    Write-Host "WARNING: Admin service not detected on port 5000" -ForegroundColor Red
    Write-Host "Please start: cd packages/admin && yarn dev" -ForegroundColor Yellow
    Write-Host ""
}

if (-not $website) {
    Write-Host "WARNING: Website service not detected on port 3000" -ForegroundColor Red
    Write-Host "Please start: cd packages/website && yarn dev" -ForegroundColor Yellow
    Write-Host ""
}

Write-Host "Starting ngrok tunnels..." -ForegroundColor Green
Write-Host ""

# Start ngrok tunnels
Write-Host "[1/3] Starting Backend API tunnel (Port 7000)..." -ForegroundColor Cyan
Start-Process -FilePath "ngrok" -ArgumentList "http", "7000" -WindowStyle Normal
Start-Sleep -Seconds 3

Write-Host "[2/3] Starting Admin Panel tunnel (Port 5000)..." -ForegroundColor Cyan
Start-Process -FilePath "ngrok" -ArgumentList "http", "5000" -WindowStyle Normal
Start-Sleep -Seconds 3

Write-Host "[3/3] Starting Website tunnel (Port 3000)..." -ForegroundColor Cyan
Start-Process -FilePath "ngrok" -ArgumentList "http", "3000" -WindowStyle Normal
Start-Sleep -Seconds 3

Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "    Tunnels Started Successfully!" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""

Write-Host "Your services will be available at:" -ForegroundColor White
Write-Host "  Backend API: Check first ngrok window" -ForegroundColor Gray
Write-Host "  Admin Panel: Check second ngrok window" -ForegroundColor Gray
Write-Host "  Website:     Check third ngrok window" -ForegroundColor Gray
Write-Host ""

Write-Host "To view all tunnel URLs, visit: " -NoNewline -ForegroundColor White
Write-Host "http://localhost:4040" -ForegroundColor Blue
Write-Host ""

Write-Host "IMPORTANT: Update your CORS configuration with the ngrok URLs!" -ForegroundColor Red
Write-Host "See ngrok-setup.md for detailed instructions." -ForegroundColor Yellow
Write-Host ""

Write-Host "Press any key to stop all tunnels and exit..." -ForegroundColor White
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")

Write-Host ""
Write-Host "Stopping all ngrok processes..." -ForegroundColor Yellow
Get-Process -Name "ngrok" -ErrorAction SilentlyContinue | Stop-Process -Force
Write-Host "All tunnels stopped." -ForegroundColor Green
Write-Host ""

Write-Host "Press any key to exit..." -ForegroundColor White
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown") 