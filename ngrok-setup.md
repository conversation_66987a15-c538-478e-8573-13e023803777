# Ngrok Setup Guide for Azkuja Application

## 1. Installation Verification
After installing ngrok, **restart your terminal/PowerShell** and run:
```bash
ngrok --version
```

## 2. Account Setup
1. Go to [ngrok.com](https://ngrok.com) and create a free account
2. Get your auth token from the dashboard
3. Run: `ngrok config add-authtoken YOUR_TOKEN_HERE`

## 3. Configuration for Multiple Services

### Option A: Individual Tunnels (Recommended for Testing)

**Backend API (Port 7000):**
```bash
ngrok http 7000 --subdomain=azkuja-api
```

**Admin Panel (Port 5000):**
```bash
ngrok http 5000 --subdomain=azkuja-admin
```

**Website (Port 3000):**
```bash
ngrok http 3000 --subdomain=azkuja-web
```

### Option B: Configuration File (Advanced)
Create `ngrok.yml` in your project root:

```yaml
version: "2"
authtoken: YOUR_TOKEN_HERE
tunnels:
  backend:
    addr: 7000
    proto: http
    subdomain: azkuja-api
    inspect: true
  admin:
    addr: 5000
    proto: http
    subdomain: azkuja-admin
    inspect: true
  website:
    addr: 3000
    proto: http
    subdomain: azkuja-web
    inspect: true
```

Then run: `ngrok start --all`

## 4. Update CORS Configuration

Once you have your ngrok URLs, update the backend CORS configuration in `packages/backend/src/main.ts`:

```typescript
app.enableCors({
  origin: [
    "http://localhost:3000",  // Website
    "http://localhost:5000",  // Admin panel
    "https://azkuja-web.ngrok.io",      // Ngrok website
    "https://azkuja-admin.ngrok.io",    // Ngrok admin
    "https://azkuja.com",
    "https://admin.azkuja.com"
  ],
  methods: ["GET", "POST", "PUT", "PATCH", "DELETE", "OPTIONS"],
  allowedHeaders: ["Content-Type", "Authorization", "Accept", "X-Requested-With", "x-user-id", "x-user-role"],
  credentials: true,
});
```

## 5. Update Frontend API URLs

### Admin Panel (`packages/admin/src/providers/dataProvider.ts`):
```typescript
const API_URL = process.env.NODE_ENV === 'production' 
  ? 'https://azkuja-api.ngrok.io/api'
  : 'http://localhost:7000/api';
```

### Website (`packages/website/src/services/api.ts`):
```typescript
const API_BASE_URL = process.env.NODE_ENV === 'production'
  ? 'https://azkuja-api.ngrok.io/api'
  : 'http://localhost:7000/api';
```

## 6. Quick Start Script

Create a batch file `start-ngrok.bat`:

```batch
@echo off
echo Starting Azkuja with Ngrok tunnels...
echo.

echo Starting Backend tunnel...
start "Backend Tunnel" ngrok http 7000 --subdomain=azkuja-api

echo Starting Admin tunnel...
start "Admin Tunnel" ngrok http 5000 --subdomain=azkuja-admin

echo Starting Website tunnel...
start "Website Tunnel" ngrok http 3000 --subdomain=azkuja-web

echo.
echo Tunnels started! Your URLs will be:
echo Backend:  https://azkuja-api.ngrok.io
echo Admin:    https://azkuja-admin.ngrok.io
echo Website:  https://azkuja-web.ngrok.io
echo.
echo Press any key to stop all tunnels...
pause
taskkill /f /im ngrok.exe
```

## 7. Environment Variables

Create `.env.ngrok` file:
```
NEXT_PUBLIC_API_URL=https://azkuja-api.ngrok.io/api
NEXT_PUBLIC_WEBSITE_URL=https://azkuja-web.ngrok.io
NEXT_PUBLIC_ADMIN_URL=https://azkuja-admin.ngrok.io
```

## 8. Testing Steps

1. Start all your services locally:
   ```bash
   # Terminal 1: Backend
   cd packages/backend && yarn start:dev
   
   # Terminal 2: Admin
   cd packages/admin && yarn dev
   
   # Terminal 3: Website
   cd packages/website && yarn dev
   ```

2. Start ngrok tunnels (new terminals):
   ```bash
   ngrok http 7000 --subdomain=azkuja-api
   ngrok http 5000 --subdomain=azkuja-admin
   ngrok http 3000 --subdomain=azkuja-web
   ```

3. Share the ngrok URLs with testers:
   - Backend API: `https://azkuja-api.ngrok.io`
   - Admin Panel: `https://azkuja-admin.ngrok.io`
   - Website: `https://azkuja-web.ngrok.io`

## 9. Free vs Paid Plans

**Free Plan Limitations:**
- Random subdomains (changes each restart)
- 1 online ngrok process
- 40 connections/minute

**Paid Plan Benefits:**
- Custom subdomains (consistent URLs)
- Multiple simultaneous tunnels
- Higher connection limits
- Password protection
- Custom domains

## 10. Security Notes

- **Don't use in production**: ngrok is for testing only
- **Temporary URLs**: Free plan URLs change on restart
- **Monitor usage**: Check ngrok dashboard for activity
- **Environment separation**: Use different subdomains for different environments

## 11. Troubleshooting

**Common Issues:**
1. **Port already in use**: Make sure your services are running on the correct ports
2. **CORS errors**: Update the backend CORS configuration with ngrok URLs
3. **Subdomain taken**: Try different subdomain names
4. **Connection refused**: Ensure your local services are running

**Useful Commands:**
```bash
# Check running processes
netstat -an | findstr :7000
netstat -an | findstr :5000
netstat -an | findstr :3000

# Kill ngrok processes
taskkill /f /im ngrok.exe
``` 