version: "2"
# Replace YOUR_TOKEN_HERE with your actual ngrok auth token
# Get it from: https://dashboard.ngrok.com/get-started/your-authtoken
authtoken: YOUR_TOKEN_HERE

# Web interface configuration
web_addr: localhost:4040
inspect_db_size: 50000000

# Tunnel definitions
tunnels:
  # Backend API
  backend:
    addr: 7000
    proto: http
    # Remove subdomain for free plan (will get random URL)
    # subdomain: azkuja-api  # Uncomment for paid plan
    inspect: true
    bind_tls: true
    
  # Admin Panel
  admin:
    addr: 5000
    proto: http
    # subdomain: azkuja-admin  # Uncomment for paid plan
    inspect: true
    bind_tls: true
    
  # Website
  website:
    addr: 3000
    proto: http
    # subdomain: azkuja-web  # Uncomment for paid plan
    inspect: true
    bind_tls: true

# Regional configuration (optional)
region: us

# Logging configuration
log_level: info
log_format: logfmt
log: stdout 