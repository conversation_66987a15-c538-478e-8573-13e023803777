@echo off
echo ========================================
echo    Testing Ngrok Setup - Website Only
echo ========================================
echo.

echo Checking if website is running on port 3000...
netstat -an | findstr ":3000.*LISTENING" > nul
if %errorlevel% neq 0 (
    echo ERROR: Website not running on port 3000
    echo Please start: yarn dev:website
    pause
    exit /b 1
)

echo ✅ Website is running on port 3000
echo.

echo Starting ngrok tunnel for website...
echo.
echo After ngrok starts, you'll see a URL like: https://abc123.ngrok.io
echo Share this URL with others to test your website!
echo.
echo Press Ctrl+C to stop the tunnel when done.
echo.

ngrok http 3000 