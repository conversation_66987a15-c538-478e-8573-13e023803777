@echo off
echo ========================================
echo    Azkuja Service Tunnel Switcher
echo ========================================
echo.

echo Current tunnel status:
echo.

REM Check current tunnel
curl -s http://localhost:4040/api/tunnels > nul 2>&1
if %errorlevel% neq 0 (
    echo No tunnel currently running.
    echo.
    goto :menu
)

for /f "tokens=*" %%i in ('curl -s http://localhost:4040/api/tunnels') do (
    echo %%i | findstr "public_url" > nul
    if !errorlevel! equ 0 (
        echo Active tunnel found
    )
)

echo.

:menu
echo Which service would you like to tunnel?
echo.
echo 1. Website (Port 3000)
echo 2. Admin Panel (Port 5000)  
echo 3. Backend API (Port 7000)
echo 4. Stop all tunnels
echo 5. Exit
echo.

set /p choice="Enter your choice (1-5): "

if "%choice%"=="1" goto :website
if "%choice%"=="2" goto :admin
if "%choice%"=="3" goto :backend
if "%choice%"=="4" goto :stop
if "%choice%"=="5" goto :exit

echo Invalid choice. Please try again.
goto :menu

:website
echo.
echo Stopping existing tunnels...
taskkill /F /IM ngrok.exe > nul 2>&1
echo Starting website tunnel (Port 3000)...
echo.
echo Your website will be available at the URL shown below:
echo Share this URL with others to test your website!
echo.
ngrok http 3000
goto :end

:admin
echo.
echo Stopping existing tunnels...
taskkill /F /IM ngrok.exe > nul 2>&1
echo Starting admin panel tunnel (Port 5000)...
echo.
echo Your admin panel will be available at the URL shown below:
echo Share this URL with others to test your admin panel!
echo.
ngrok http 5000
goto :end

:backend
echo.
echo Stopping existing tunnels...
taskkill /F /IM ngrok.exe > nul 2>&1
echo Starting backend API tunnel (Port 7000)...
echo.
echo Your backend API will be available at the URL shown below:
echo Share this URL with others to test your API!
echo.
ngrok http 7000
goto :end

:stop
echo.
echo Stopping all tunnels...
taskkill /F /IM ngrok.exe > nul 2>&1
echo All tunnels stopped.
echo.
pause
goto :end

:exit
echo.
echo Goodbye!
goto :end

:end
echo. 