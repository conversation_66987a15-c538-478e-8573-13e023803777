@echo off
echo ========================================
echo    Azkuja Ngrok Tunnel Starter
echo ========================================
echo.

echo Checking if services are running...
echo.

REM Check if services are running
netstat -an | findstr :7000 > nul
if %errorlevel% neq 0 (
    echo WARNING: Backend service not detected on port 7000
    echo Please start: cd packages/backend && yarn start:dev
    echo.
)

netstat -an | findstr :5000 > nul
if %errorlevel% neq 0 (
    echo WARNING: Admin service not detected on port 5000
    echo Please start: cd packages/admin && yarn dev
    echo.
)

netstat -an | findstr :3000 > nul
if %errorlevel% neq 0 (
    echo WARNING: Website service not detected on port 3000
    echo Please start: cd packages/website && yarn dev
    echo.
)

echo Starting ngrok tunnels...
echo.

echo [1/3] Starting Backend API tunnel (Port 7000)...
start "Azkuja Backend API" ngrok http 7000
timeout /t 3 /nobreak > nul

echo [2/3] Starting Admin Panel tunnel (Port 5000)...
start "Azkuja Admin Panel" ngrok http 5000
timeout /t 3 /nobreak > nul

echo [3/3] Starting Website tunnel (Port 3000)...
start "Azkuja Website" ngrok http 3000
timeout /t 3 /nobreak > nul

echo.
echo ========================================
echo    Tunnels Started Successfully!
echo ========================================
echo.
echo Your services will be available at:
echo   Backend API: Check first ngrok window
echo   Admin Panel: Check second ngrok window  
echo   Website:     Check third ngrok window
echo.
echo To view all tunnel URLs, visit: http://localhost:4040
echo.
echo IMPORTANT: Update your CORS configuration with the ngrok URLs!
echo See ngrok-setup.md for detailed instructions.
echo.
echo Press any key to stop all tunnels and exit...
pause > nul

echo.
echo Stopping all ngrok processes...
taskkill /f /im ngrok.exe > nul 2>&1
echo All tunnels stopped.
echo.
pause 