# 🧪 Role-Based Feature Limitation Testing Guide

## 📋 **Testing Overview**

This guide covers testing the complete implementation of **Option 1: Role-Based Feature Limitation** across all three applications.

## 🌐 **Application URLs**

- **Website**: http://localhost:3000
- **Admin Panel**: http://localhost:5000  
- **Backend API**: http://localhost:7000

## 👥 **Test User Roles**

### **Admin User**
- **Role**: `admin`
- **Access**: Full system access
- **Can see**: All restaurants, orders, reviews, users

### **Restaurant Owner**
- **Role**: `restaurant_owner`
- **Access**: Limited to own restaurants
- **Can see**: Only their restaurants, orders, reviews

### **Customer**
- **Role**: `customer`
- **Access**: Website only
- **Cannot access**: Admin panel

## 🧪 **Testing Scenarios**

### **Scenario 1: Admin Panel Access Control**

#### **Test 1.1: Admin Login**
1. Go to http://localhost:5000
2. Login with admin credentials
3. ✅ **Expected**: See all menu items (Users, Categories, etc.)
4. ✅ **Expected**: See all restaurants in restaurant list
5. ✅ **Expected**: See all orders in orders list

#### **Test 1.2: Restaurant Owner Login**
1. Go to http://localhost:5000
2. Login with restaurant owner credentials
3. ✅ **Expected**: See limited menu items (no Users, no Categories)
4. ✅ **Expected**: See warning banner suggesting main dashboard
5. ✅ **Expected**: See only owned restaurants in restaurant list
6. ✅ **Expected**: See only owned restaurant orders in orders list

#### **Test 1.3: Customer Login Attempt**
1. Go to http://localhost:5000
2. Try to login with customer credentials
3. ✅ **Expected**: Access denied or redirect to login

### **Scenario 2: Data Filtering Verification**

#### **Test 2.1: Restaurant Owner Data Isolation**
1. Login as restaurant owner in admin panel
2. Navigate to Restaurants page
3. ✅ **Expected**: Only see restaurants owned by this user
4. Navigate to Orders page
5. ✅ **Expected**: Only see orders for owned restaurants
6. Navigate to Reviews page
7. ✅ **Expected**: Only see reviews for owned restaurants

#### **Test 2.2: Admin Full Access**
1. Login as admin in admin panel
2. Navigate to all pages
3. ✅ **Expected**: See all data across all restaurants
4. ✅ **Expected**: Access to Users and Categories management

### **Scenario 3: Website Integration**

#### **Test 3.1: Restaurant Owner Website Access**
1. Login as restaurant owner on website
2. Go to profile dropdown
3. ✅ **Expected**: See "داشبورد رستوران" link
4. ✅ **Expected**: See "پنل مدیریت (پیشرفته)" link
5. Click main dashboard link
6. ✅ **Expected**: Access to restaurant dashboard
7. Click admin panel link
8. ✅ **Expected**: Open admin panel in new tab

### **Scenario 4: API Endpoint Testing**

#### **Test 4.1: Direct API Testing**
```bash
# Test restaurant owner filtering
curl -H "X-User-Role: restaurant_owner" \
     -H "X-User-ID: [owner-id]" \
     http://localhost:7000/api/restaurants

# Test admin access
curl -H "X-User-Role: admin" \
     -H "X-User-ID: [admin-id]" \
     http://localhost:7000/api/restaurants

# Test orders filtering
curl -H "X-User-Role: restaurant_owner" \
     -H "X-User-ID: [owner-id]" \
     http://localhost:7000/api/orders
```

## 🔍 **Verification Checklist**

### **✅ Backend Implementation**
- [ ] Restaurants controller accepts `owner_id` parameter
- [ ] Orders controller accepts `restaurant_owner_id` parameter
- [ ] Reviews controller accepts `restaurant_owner_id` parameter
- [ ] All controllers read `X-User-Role` and `X-User-ID` headers
- [ ] Service methods filter data by ownership
- [ ] Database queries include owner relationships

### **✅ Admin Panel Implementation**
- [ ] Data provider sends user context headers
- [ ] Menu items filtered by user role
- [ ] Restaurant owners see warning banner
- [ ] Data queries filtered by ownership
- [ ] Edit/delete operations verify ownership

### **✅ Website Integration**
- [ ] Restaurant owners have admin panel link
- [ ] Main dashboard remains primary interface
- [ ] Proper role-based navigation

## 🐛 **Common Issues & Solutions**

### **Issue 1: Restaurant Owner Sees All Data**
**Solution**: Check if headers are being sent correctly from data provider

### **Issue 2: Admin Panel Access Denied**
**Solution**: Verify role is correctly stored in localStorage

### **Issue 3: API Filtering Not Working**
**Solution**: Check backend controller parameter names and service methods

### **Issue 4: Menu Items Not Filtered**
**Solution**: Verify role-based menu filtering in layout component

## 📊 **Performance Testing**

### **Test Database Query Efficiency**
1. Check query execution time with ownership filters
2. Verify indexes on owner relationships
3. Monitor memory usage with filtered data

### **Test Frontend Performance**
1. Measure page load times with filtered data
2. Check for unnecessary API calls
3. Verify proper caching behavior

## 🎯 **Success Criteria**

The implementation is successful when:

1. **✅ Restaurant owners can only see their own data**
2. **✅ Admins can see all data**
3. **✅ Customers cannot access admin panel**
4. **✅ All CRUD operations respect ownership**
5. **✅ Performance remains acceptable**
6. **✅ User experience is clear and intuitive**

## 🔄 **Next Steps After Testing**

1. **Fix any discovered issues**
2. **Optimize database queries**
3. **Add more comprehensive error handling**
4. **Implement audit logging**
5. **Add unit and integration tests**
6. **Deploy to staging environment**

---

## 📝 **Testing Log Template**

```
Date: ___________
Tester: ___________
Environment: ___________

Test Results:
[ ] Admin Panel Access Control
[ ] Data Filtering Verification  
[ ] Website Integration
[ ] API Endpoint Testing

Issues Found:
1. ___________
2. ___________

Resolution Status:
[ ] All tests passed
[ ] Issues identified and logged
[ ] Ready for production
``` 