# Cloudinary Setup Guide for Azkuja Restaurant Photo Management

## 1. Create Cloudinary Account

1. Go to [Cloudinary.com](https://cloudinary.com/)
2. Sign up for a free account
3. You'll get 25GB storage + 25GB bandwidth free

## 2. Get API Credentials

1. Go to your Cloudinary Dashboard
2. Copy the following credentials:
   - **Cloud Name**: `your_cloud_name`
   - **API Key**: `your_api_key`
   - **API Secret**: `your_api_secret`

## 3. Environment Variables

Add these to your `.env` file in `packages/backend/`:

```env
# Cloudinary Configuration
CLOUDINARY_CLOUD_NAME=your_cloud_name
CLOUDINARY_API_KEY=your_api_key
CLOUDINARY_API_SECRET=your_api_secret
```

## 4. Hostinger Deployment

### Option A: Environment Variables in Hostinger
1. Go to your Hostinger hosting panel
2. Navigate to "Advanced" → "Environment Variables"
3. Add the three Cloudinary variables

### Option B: .env File Upload
1. Create `.env` file with your credentials
2. Upload to your backend directory on Hostinger
3. Make sure it's not publicly accessible

## 5. Image Upload Features

The system automatically:
- ✅ Resizes images to max 1200x800px
- ✅ Optimizes quality automatically
- ✅ Converts to best format (WebP/AVIF)
- ✅ Organizes in folders by restaurant
- ✅ Provides fast CDN delivery worldwide

## 6. Folder Structure

Images are organized as:
```
restaurants/
  ├── restaurant-id-1/
  │   ├── photo1.jpg
  │   └── photo2.jpg
  └── restaurant-id-2/
      ├── photo1.jpg
      └── photo2.jpg
```

## 7. Cost Estimation

**Free Tier**: 25GB storage + 25GB bandwidth
- **Perfect for**: 5,000-10,000 restaurant photos
- **Upgrade**: $89/month for 100GB when needed

## 8. Alternative Options

### If you prefer AWS S3:
- Create S3 bucket
- Replace CloudinaryService with S3Service
- Cost: ~$0.023/GB/month

### If you prefer local storage:
- Use Hostinger's file storage
- Implement local file upload
- Less optimization features

## 9. Testing

Test the upload with:
```bash
# Start backend
yarn start:dev

# Test upload endpoint
curl -X POST http://localhost:7000/api/restaurants/[restaurant-id]/photos \
  -H "Authorization: Bearer [your-token]" \
  -F "photos=@test-image.jpg" \
  -F "category=gallery"
```

## 10. Production Checklist

- [ ] Cloudinary account created
- [ ] API credentials added to environment
- [ ] Backend deployed with new environment variables
- [ ] Test photo upload functionality
- [ ] Verify image optimization is working
- [ ] Check CDN delivery speed

## Support

If you need help with setup:
1. Check Cloudinary documentation
2. Test with small images first
3. Monitor usage in Cloudinary dashboard
4. Set up webhooks for advanced features if needed 